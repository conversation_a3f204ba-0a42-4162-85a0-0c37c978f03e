# Budget and Cost Update Command Documentation

## Overview

The `budget:update-costs` Laravel Artisan command performs a comprehensive update of booking costs and weekly budget utilisation across all existing bookings in the system.

## Command Signature

```bash
php artisan budget:update-costs [options]
```

## Options

| Option | Description | Default |
|--------|-------------|---------|
| `--batch-size=N` | Number of bookings to process in each batch | 100 |
| `--dry-run` | Run without making actual changes (simulation mode) | false |
| `--summary-only` | Show only high-level summary without detailed processing (very fast) | false |
| `--reset-all` | Reset all processing flags to reprocess everything | false |

## What the Command Does

### Step 0: Reset Weekly Utilisation Data (NEW)
- **Purpose**: Avoid duplication of previously calculated values
- **Actions**:
  - Truncates `weekly_utilised_fund_logs` table completely
  - Resets `client_unit_weekly_budgets.total_weekly_utilisation` to 0
  - Resets `client_unit_weekly_budgets.percent_utilisation` to 0
  - Resets `client_unit_weekly_budgets.weekly_unutilised_fund` to full allocation
  - Triggers recalculation for all annual budgets
  - Resets `bookings.budget_utilisation_updated` to `false` for unprocessed bookings only
  - With `--reset-all`: Resets both `budget_utilisation_updated` and `is_update_initial_cost_processed` to `false` for ALL bookings

### Step 1: Update Initial Costs
- **Target**: Bookings where `is_update_initial_cost_processed = false` (not yet processed)
- **Action**: 
  - Calculates cost using `calculateCostBookingNumberonly()` method
  - Updates `bookings.initial_cost` field
  - Sets `bookings.is_update_initial_cost_processed` to `true` (1)
  - Sets `bookings.budget_utilisation_updated` to `false` (0)
- **Efficiency**: Avoids reprocessing bookings that have already been calculated

### Step 2: Update Weekly Budget Utilisation
- **Target**: Bookings where:
  - `is_update_initial_cost_processed = false` (unprocessed bookings only)
  - `budget_utilisation_updated = false` (not yet processed)
  - `unitStatus IN (1, 4)` (confirmed or new bookings only)
- **Action**:
  - Calls `updateWeeklyUtilisedFund()` helper function
  - Uses `bookings.unit_cost` if non-null and > 0, otherwise falls back to `bookings.initial_cost`
  - Updates `client_unit_weekly_budgets.total_weekly_utilisation`
  - Sets `bookings.budget_utilisation_updated` to `true` (1) after successful update
  - Uses "addition" as `adjustmentType` for the helper function
- **Efficiency**: Only processes bookings that haven't been processed yet

## Usage Examples

### 1. Quick Summary (Recommended First Step)
```bash
php artisan budget:update-costs --summary-only
```
- **Very fast** execution (seconds)
- Shows high-level overview of what would be done
- No actual processing or detailed calculations
- Perfect for initial assessment

### 2. Detailed Dry Run (Recommended Before Live Run)
```bash
php artisan budget:update-costs --dry-run
```
- **Fast** execution (optimized for speed)
- Simulates the entire process without making changes
- Shows step-by-step progress without per-record calculations
- Safe for testing and verification

### 3. Live Run with Default Settings
```bash
php artisan budget:update-costs
```
- **WARNING**: Makes actual database changes
- Processes 100 bookings per batch
- Use only after testing with dry-run modes

### 4. Custom Batch Size
```bash
php artisan budget:update-costs --batch-size=50
```
- Processes 50 bookings per batch (useful for systems with memory constraints)

### 5. Combined Options for Testing
```bash
php artisan budget:update-costs --batch-size=200 --dry-run
```
- Fast dry run with larger batch size for simulation

## Prerequisites

### Database Migrations
Ensure these migrations have been run:
```bash
php artisan migrate
```

Required tables and columns:
- `bookings.initial_cost` (decimal 10,3, nullable)
- `bookings.budget_utilisation_updated` (boolean, default false)
- `bookings.unit_cost` (existing)
- `client_unit_weekly_budgets` table (existing)

### Helper Function
The `updateWeeklyUtilisedFund()` helper function must be available in `app/Helpers/helpers.php`.

## Output and Progress

The command provides detailed progress information:

```
Starting Budget and Cost Update Process...

=== STEP 0: Resetting weekly utilisation data ===
Found 2500 existing fund logs to clear
✅ Cleared weekly_utilised_fund_logs table
Found 520 weekly budgets to reset
✅ Reset utilisation for 520 weekly budgets
Recalculating: 52/52 [████████████████████████████] 100%
✅ Recalculation triggered for 52 annual budgets
✅ Reset budget_utilisation_updated flag for 1500 bookings
Step 0 completed: Weekly utilisation data reset

=== STEP 1: Updating initial_cost for all bookings ===
Found 1500 total bookings to process
Processing: 1500/1500 [████████████████████████████] 100% - Updated: 1450

Step 1 Results:
- Processed: 1500 bookings
- Updated: 1450 bookings
- Errors: 50 bookings (check logs for details)

=== STEP 2: Updating weekly budget utilisation ===
Found 800 eligible bookings for budget utilisation update
Processing: 800/800 [████████████████████████████] 100% - Updated: 780

Step 2 Results:
- Processed: 800 eligible bookings
- Updated: 780 bookings
- Errors: 20 bookings (check logs for details)

✅ Budget and Cost Update Process completed successfully!
```

## Error Handling

### Logging
All errors are logged to Laravel's log system with detailed context:
- Booking ID
- Error message
- Stack trace (for debugging)

### Transaction Safety
- Step 2 uses database transactions for each booking
- Failed updates are rolled back automatically
- Partial failures don't affect other bookings

### Recovery
If the command fails partway through:
- Step 1: Bookings with `initial_cost` already set can be safely re-run
- Step 2: Only processes bookings where `budget_utilisation_updated = false`
- Safe to re-run the entire command multiple times

## Performance Considerations

### Batch Processing
- Default batch size: 100 bookings
- Adjust `--batch-size` based on server memory and performance
- Smaller batches = less memory usage, more database queries
- Larger batches = more memory usage, fewer database queries

### Memory Usage
- Each booking requires cost calculation
- Monitor memory usage with large batch sizes
- Consider running during off-peak hours

### Database Load
- Step 1: Updates booking records in batches
- Step 2: Updates multiple tables (bookings + weekly budgets)
- Consider impact on concurrent application usage

## Monitoring and Verification

### Before Running
```bash
# Test the command setup
php test_budget_command.php

# Check current state
SELECT COUNT(*) FROM bookings WHERE initial_cost IS NULL;
SELECT COUNT(*) FROM bookings WHERE budget_utilisation_updated = 0;
SELECT COUNT(*) FROM weekly_utilised_fund_logs;
SELECT SUM(total_weekly_utilisation) FROM client_unit_weekly_budgets;
```

### After Running
```bash
# Verify results
SELECT COUNT(*) FROM bookings WHERE initial_cost IS NOT NULL;
SELECT COUNT(*) FROM bookings WHERE budget_utilisation_updated = 1;
SELECT COUNT(*) FROM weekly_utilised_fund_logs;
SELECT SUM(total_weekly_utilisation) FROM client_unit_weekly_budgets;

# Check for any remaining null costs
SELECT bookingId, unitId, date FROM bookings WHERE initial_cost IS NULL LIMIT 10;

# Verify weekly budget totals match log entries
SELECT 
    wub.id,
    wub.total_weekly_utilisation,
    COALESCE(SUM(wufl.adjustment_amount), 0) as log_total
FROM client_unit_weekly_budgets wub
LEFT JOIN weekly_utilised_fund_logs wufl ON wub.id = wufl.client_unit_weekly_budget_id
    AND wufl.adjustment_type = 'addition'
GROUP BY wub.id, wub.total_weekly_utilisation
HAVING ABS(wub.total_weekly_utilisation - COALESCE(SUM(wufl.adjustment_amount), 0)) > 0.01
LIMIT 10;
```

### Log Files
Check Laravel logs for detailed execution information:
```bash
tail -f storage/logs/laravel.log | grep "Budget update"
```

## Troubleshooting

### Common Issues

1. **"Command not found"**
   - Ensure the command file exists in `app/Console/Commands/`
   - Run `php artisan list` to see available commands

2. **"Helper function not found"**
   - Verify `updateWeeklyUtilisedFund()` exists in `app/Helpers/helpers.php`
   - Check if helpers are properly loaded in `composer.json`

3. **"Column not found"**
   - Run pending migrations: `php artisan migrate`
   - Check database schema matches requirements

4. **Memory errors with large batches**
   - Reduce `--batch-size` option
   - Monitor server memory usage

5. **Weekly budget lookup failures**
   - Ensure annual budgets exist for the financial years
   - Check `client_unit_weekly_budgets` table has proper date ranges

### Docker Usage
```bash
# Run in Docker container
docker exec -it clientportal-php php artisan budget:update-costs --dry-run

# Check Docker logs
docker logs clientportal-php | grep "Budget update"
```

## Safety Recommendations

1. **Always run dry-run first**
   ```bash
   php artisan budget:update-costs --dry-run
   ```

2. **Backup database before live run**
   ```bash
   # Example backup
   docker exec clientportal-mysql mysqldump -u root -pdev smartseek > backup_before_budget_update.sql
   ```

3. **Important: Data Reset Warning**
   - **Step 0 will completely clear** `weekly_utilised_fund_logs` table
   - **Step 0 will reset all** weekly budget utilisation values to 0
   - **This is intentional** to avoid duplication and ensure clean recalculation
   - **Always backup before running** in production

4. **Run during maintenance window**
   - Lower application usage
   - More system resources available

5. **Monitor progress**
   - Keep terminal session active
   - Monitor log files
   - Check system resources

6. **Verify results**
   - Compare before/after counts
   - Spot-check calculation accuracy
   - Validate weekly budget totals

## Command File Location

```
app/Console/Commands/UpdateBudgetAndCosts.php
```

## Related Files

- `app/Helpers/helpers.php` - Contains `updateWeeklyUtilisedFund()` function
- `app/Http/Controllers/UnitPortal/BookingController.php` - Contains cost calculation methods
- `app/Models/Booking.php` - Booking model with fillable fields
- `database/migrations/*booking*.php` - Database schema migrations
