<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use ParagonIE\Sodium\Core\Curve25519\Ge\P1p1;
use Illuminate\Support\Facades\Storage;

class Timesheet extends Model
{
    // use Cachable;
    protected $table = 'timesheets';

    protected $primaryKey = 'timesheetId';

    protected $fillable = [
      'bookingId',
      'number',
      'startTime',
      'endTime',
      'status',
      'timesheetRefId',
      'image',
      'comments',
      'checkInBy',
      'checked_at',
      'verifiedBy',
      'verified_at',
      'breakHours',
      'staff_break',
      'staffHours',
      'smsAcceptedStatus',
      'smsRejectedStatus',
      'unitHours',
      'isMovedFromPreviousWeek',
      'isMovedToNextWeek',
      'invoice_status',
      'invoice_generated_by',
      'verification_from_portal',
      'decline_reason',
      'unit_expense_type',
      'unit_expense_amount',
    ];

    public function booking(){
      return $this->hasOne(Booking::class,'bookingId','bookingId');
    }
    
    public function quotation(){
      return $this->hasOne(Quotation::class,'timesheetId','timesheetId');
    }

    public function checkin(){
      return $this->hasOne(Admin::class,'adminId','checkInBy')->withDefault([
        'name' =>''
      ]);
    }

    public function verify(){
      return $this->hasOne(Admin::class,'adminId','verifiedBy')->withDefault([
        'name' =>''
      ]);
    }

    public function getImageUrlAttribute(){
      if($this->image){
        return Storage::disk('s3')->url('timesheets/'.$this->image); 
      }else{
        return "";
      }
      
    }

    protected $appends = ['image_url'];
}
