<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Support\Facades\Log;

class Booking extends Model
{
    use SoftDeletes;
    // use Cachable;
    protected $primaryKey = 'bookingId';

    protected $fillable = [
      'categoryId',
      'start_time',
      'end_time',
      'date',
      'time',
      'handledBy',
      'app_visible',
      'is_premium',
      'premium_amount',
      'app_visibility_radius',
      'unitId',
      'unitStatus',
      'unitAmend',
      'type',
      'shiftType',
      'outBoundPickupTime',
      'modeOfRequest',
      'requestedBy',
      'requestedTime',
      'requestedDate',
      'shiftId',
      'noOfShifts',
      'assigned_by',
      'assigned_at',
      'homeStatus',
      'staffId',
      'staffStatus',
      'confirmedBy',
      'confirmedAt',
      'reason',
      'aggreedHrRate',
      'importantNotes',
      'newSmsStatus',
      'finalConfirmationSms',
      'modeOfCancelRequest',
      'cancelDate',
      'cancelTime',
      'cancelRequestedBy',
      'cancelExplainedReason',
      'cancelInformUnitTo',
      'canceledOrUTCreason',
      'confirmSmsStatus',
      'comments',
      'tvStatus',
      'paymentStatus',
      'invoiceStatus',
      'status',
      'distanceToWorkplace',
      'staffAllocateStatus',
      'modeOfTransport',
      'outBoundDriver',
      'outBoundDriverType',
      'outBoundDriverId',
      'outBoundClubId',
      'inBoundDriver',
      'inBoundDriverType',
      'inBoundDriverId',
      'bonus',
      'final_hr_with_bonus',
      'ir35_status',
      'finalConfirm',
      'transportAllowence',
      'outBoundClubId',
      'inBoundClubId',
      'cancelNotes',
      'cancelAuthorizedBy',
      'unit_cost',
      'staff_cost',
      'fsc_sms_scheduled',
      'time_to_cover',
      'staff_clear_status',
      'created_time',
      'schedule_time_change',
      'po_number',
      'booked_user_id',
      'booked_portal',
      'booked_portal_id',
      'initial_cost',
      'budget_utilisation_updated',
      'unit_final_cost'
            
    ];

    protected $casts = [
        'budget_utilisation_updated' => 'boolean'
    ];
    public function getIsTripGeneratedAttribute(){
        $trip = Trip::where('date',$this->date)->where('shift_id',$this->shiftId)->where('unit_id',$this->unitId)->count();
        return $trip;
    }

    public function bannedunits(){
      return $this->hasMany(StaffBannedUnit::class,'unitId','unitId');
    }
    public function unitPayments(){
      return $this->hasOne(StaffUnitPayment::class,'staffId','staffId')->where('clientUnitId',$this->unitId);
    }

    public function trip(){
        return $this->hasOne(Trip::class,'unit_id','unitId')->where('date',$this->date)->where('shift_id',$this->shiftId);
    }

    public function timesheet(){
      return $this->hasOne(Timesheet::class,'bookingId','bookingId')->withDefault([
        'startTime' =>'',
        'endTime' =>'',
        'breakHours' =>'',
        'staffHours' =>'',
      ]);
    }

    public function getBonusReasonTextAttribute(){
      switch ($this->bonusReason) {
        case 1:
          return "Last min call";
          break;
        case 2:
          return "Short Shift-less than 4hr";
          break;
        case 3:
          return "Staff cancellation";
          break;
        case 4:
          return "Booking error";
          break;
        case 5:
          return "Weather Condition";
          break;
        case 6:
          return "Staying over time";
          break;
        case 7:
          return "Other reason";
          break;
      }
    }

    public function shift(){
      return $this->hasOne(Shift::class,'shiftId','shiftId')->withDefault([
        'name' =>''
      ])->orderBy('name','ASC');
    }
    public function unit(){
      return $this->hasOne(ClientUnit::class,'clientUnitId','unitId')->withDefault([
        'name' =>''
      ])->select([
        'clientUnitId',
        'name',
        'alias',
        'alias2',
        'company',
        'ri',
        'type',
        'home_type',
        'priority_homes',
        'paid_training',
        'ir35_status',
        'tier_class',
        'admin_team_id',
        'clientId',
        'branchId',
        'remember_token',
        'enic_status',
        'covid_status',
        'businessAddress',
        'nameOfManager',
        'fax',
        'website',
        'nsgManagerId',
        'poOrder',
        'address',
        'postCode',
        'latitude',
        'longitude',
        'email',
        'localAuthoritySocialServices',
        'nameOfDeputyManager',
        'nameOfRotaHRAdministrator',
        'residenceCapacity',
        'agencyUsageLevelHCA',
        'agencyUsageLevelRGN',
        'agencyUsageLevelOthers',
        'invoiceFrequency',
        'paymentTermAgreed',
        'latestCQCReport',
        'status',
        'image',
        'staff_profile_type',
        'created_at',
        'updated_at',
        'deleted_at'

    ]);
    }
    public function category(){
      return $this->hasOne(StaffCategory::class,'categoryId','categoryId')->withDefault([
        'name' =>''
      ]);
    }
    public function bookedby(){
      return $this->hasOne(User::class,'userable_id','booked_user_id')->withTrashed();
    }
    public function staff(){
      return $this->hasOne(Staff::class,'staffId','staffId')->withDefault([
        'name' =>'',
        'forname' =>'',
        'modeOfTransport' =>''
      ]);

    }
    public function staffsapi(){
      return $this->hasOne(Staff::class,'staffId','staffId')->withDefault([
        'name' =>'',
        'forname' =>'',
        'modeOfTransport' =>''
      ])->select([
        'staffId',
        'code',
        'title',
        'forname',
        'surname',
        'middle_name',
        'company',
        'admin_team_id',
        'categoryId',
        'deviceId',
        'home_type',
        'shiftCount',
        'dateOfBirth',
        'gender',
        'photo',
        'performancePoint',
        'isPermenent',
        'working_type',
        'tier_class',
        'address',
        'quickNotes',
        'booking_team_notes',
        'onboarding_steps',
        'pincode',
        'latitude',
        'longitude',
        'joinedDate',
        'modeOfTransport',
        'pickupLocation',
        'unitId',
        'branchId',
        'zoneId',
        'prefered_shift',
        'personalProgress',
        'referenceProgress',
        'rtwProgress',
        'dbsProgress',
        'trainingProgress',
        'workExperienceProgress',
        'academicProgress',
        'nokFullName',
        'nokRelationship',
        'nokAddress',
        'nokPostCode',
        'nokPhone',
        'nokMobile',
        'nokEmail',
        'nmcPinNumber',
        'nmcPinExpiryDate',
        'nmcPinReValidationDate',
        'payroll_id',
        'ri',
        'max_hours_weekly',
        'bankSortCodeA',
        'bankSortCodeB',
        'bankSortCodeC',
        'bankAccountNumber',
        'verified_acc_number',
        'niNumber',
        'niDocumentFile',
        'niTable',
        'studentLoan',
        'latestTaxBand',
        'paymentMode',
        'selfPaymentCompanyName',
        'selfPaymentCompanyNumber',
        'selfPaymentCompanyRegAddress',
        'status',
        'profileDocumentFile',
        'birth_certificate_document',
        'passport_document',
        'visa_document',
        'brp_document',
        'id_card_document',
        'driving_license_document',
        'address_proof_document',
        'sw_profile',
        'bank_statement',
        'completedApplication',
        'eupassport',
        'onlinecheck',
        'settlement',
        'verificationReport',
        'citizen',
        'countersigned',
        'starterCheck',
        'pfourfive',
        'utility',
        'localauth',
        'hrmc',
        'financialState',
        'creditUnion',
        'mortage',
        'councilRent',
        'pensionbenefits',
        'auditreport',
        'covid_vaccine_certificate',
        'nmc_registration',
        'other',
        'other1',
        'prfy',
        'shift_pattern_type',
        'vaccine_first_dose',
        'vaccine_second_dose',
        'covidPassDate',
        'covidPassStatus',
        'deleted_at',
        'created_at',
        'updated_at',
        'remember_token',
        'last_msg_date_time',
        'unknown_number'
      ]);
    }

    public function transportation(){
      return $this->hasMany(Transportation::class,'bookingId','bookingId');
    }

    public function logs(){
      return $this->hasMany(BookingLog::class,'bookingId','bookingId');
    }

    public function confirmedby(){
      return $this->hasOne(Admin::class,'adminId','confirmedBy')->withDefault([
        'name' =>''
      ]);
    }

    public function handledby(){
      return $this->hasOne(Admin::class,'adminId','handledBy')->withDefault([
        'name' =>''
      ]);
    }

    public function assignedby(){
      return $this->hasOne(Admin::class,'adminId','assigned_by')->withDefault([
        'name' =>''
      ]);
    }

    public function requestedby(){
      return $this->hasOne(ClientUnitContact::class,'clientUnitId','requestedBy');
    }

    public function requestedbyrelation(){
      return $this->hasOne(ClientUnitContact::class,'clientUnitPhoneId','requestedBy');
    }

    public function canceled(){
      return $this->hasOne(Admin::class,'adminId','cancelAuthorizedBy')->withDefault([
        'name' =>''
      ]);
    }

    public function unabletocover(){
      return $this->hasOne(BookingUnitStatus::class,'bookingId','bookingId')->withDefault([
        'name' =>''
      ]);
    }

    public function unabletocoverby(){
      return $this->hasOne(Admin::class,'adminId','unableToCoverBy')->withDefault([
        'name' =>''
      ]);
    }
    public function bonusauthby(){
      return $this->hasOne(Admin::class,'adminId','bonusAuthorizedBy')->withDefault([
        'name' =>''
      ]);
    }

    public function unitstatus(){
      return $this->hasOne(BookingUnitStatus::class,'bookingId','bookingId')->withDefault([
        'name' =>''
      ]);
    }

    public function unitcontacts(){
      return $this->hasMany(ClientUnitContact::class,'clientUnitId','unitId');
    }

    public function chats(){
      return $this->hasMany(ClientUnitChat::class,'booking_id','bookingId');
    }

    public function unitinformlog(){
      return $this->hasOne(ClientUnitInformLog::class,'bookingId','bookingId');
    }

    public function scopeUnit($query,$unitId){
      return $query->where('unitId',$unitId)->with(['staff','category','shift']);
    }

    public function getEntryDateAttribute(){
      return date('d-M-Y',strtotime($this->created_at));
    }
    public function getBookingDateHumanAttribute(){
      return date('d-M-Y, D',strtotime($this->date));
    }

    public function getConfirmedAtAttribute(){
      return date('Y-m-d',strtotime($this->created_at));
    }

    public function getStaffProfileLinkAttribute(){
      if(isset($this->staff)){
        return asset('storage/app/staff/staff_profile/'.$this->staff->profileDocumentFile);
      }else{
        return '';
      }
    }

    public function scopeIgnoredummy($query){
      $query->whereHas('unit',function($query1){
        $query1->whereNotIn('clientId',[16,37,44,25]);
      });
    }
    public function admin(){
      return $this->hasOne(Admin::class,'adminId','ri');
    }

    public function getReasonTextAttribute(){
      switch ($this->reason) {
        case '1':
          return 'Staff Sickness';
          break;
        case '2':
          return 'Holiday cover';
          break;
        case '3':
          return 'Vacant Position';
          break;
        case '4':
          return 'New Resident admission';
          break;
        case '5':
          return '1 to 1 care';
          break;
        case '6':
          return 'Extra staff requirement';
          break;
        case '7':
          return 'Staff training day';
          break;

        default:
          return 'No Reason';
          break;
      }
    }

    public function getBookingUnitCostAttribute(){
        //return null;
      $allClientPayments = ClientUnitPayment::where('clientUnitId',$this->unitId)->get();
      $day = strtolower(date('D',strtotime($this->date)));
      $shiftId = $this->shiftId;
      $clientPayment = $allClientPayments->where('staffCategoryId',$this->categoryId)->where('rateType',1);
      $filteredData = $clientPayment->first();

      $ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];

      $clientPaymentsEnic = $allClientPayments->where('staffCategoryId',$this->categoryId)->where('rateType',2);
      $filteredDataEnic = $clientPaymentsEnic->first();

      switch ($day) {
          case "mon":
              $selectColumn = "Monday";
              break;
          case "tue":
              $selectColumn = "Tuesday";
              break;
          case "wed":
              $selectColumn = "Wednesday";
              break;
          case "thu":
              $selectColumn = "Thursday";
              break;
          case "fri":
              $selectColumn = "Friday";
              break;
          case "sat":
              $selectColumn = "Saturday";
              break;
          case "sun":
              $selectColumn = "Sunday";
              break;
          }

      if($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
          $columnName = "day".$selectColumn;
      } else if($shiftId == 5) {
        $columnName = "twilight_".strtolower($selectColumn);
      } else {
          $columnName = "night".$selectColumn;
      }

      $payAmountPerHr = $filteredData[$columnName];

      $clientPaymentsEnicRate = $filteredDataEnic[$columnName];

      $schedule = ClientUnitSchedule::where('clientUnitId',$this->unitId)
                                      ->where('staffCategoryId',$this->categoryId)
                                      ->where('shiftId',$this->shiftId)
                                      ->first();

      if(isset($schedule->totalHoursUnit)){
        $totalHoursUnit = $schedule->totalHoursUnit;
      } else{
        $totalHoursUnit = 0;
      }
      $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate)+$ta;
      return  $inTotal;
    }

    // protected $appends = ['reason_text','entry_date','confirmed_at','is_trip_generated','booking_unit_cost','booking_date_human','staff_profile_link','bonus_reason_text'];
    protected $appends = ['reason_text','entry_date','confirmed_at','is_trip_generated','booking_date_human','staff_profile_link','bonus_reason_text'];

    //Relation to get the workflow approvers, booking has workflow_id and workflow_approvers table has workflow_id list
    public function workflowApprovers(){
      return $this->hasMany(WorkflowApprover::class,'workflow_id','workflow_id')->orderBy('approval_order');
    }
    public function workflow(){
      return $this->hasOne(Workflow::class,'id','workflow_id');
    }
    public function bookingWorkflowStatus(){
      return $this->hasMany(BookingWorkflowApproverStatus::class,'booking_id','bookingId');
    }
    public function bookingWorkflows()
    {
        return $this->hasMany(BookingWorkflow::class, 'booking_id', 'bookingId');
    }
    public function workflows()
    {
        return $this->hasManyThrough(
            Workflow::class,              // Final model
            BookingWorkflow::class,       // Intermediate model
            'booking_id',                 // Foreign key on BookingWorkflow table
            'id',                         // Foreign key on Workflow table
            'bookingId',                  // Local key on Booking table
            'workflow_id'                 // Local key on BookingWorkflow table
        );
    }
}
