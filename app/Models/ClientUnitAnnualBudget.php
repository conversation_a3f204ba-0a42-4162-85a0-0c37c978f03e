<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClientUnitAnnualBudget extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_unit_annual_budgets';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_unit_id',
        'financial_year',
        'financial_year_start',
        'financial_year_end',
        'annual_budget',
        'created_by'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'annual_budget' => 'decimal:2',
    ];

    /**
     * Get the client unit that owns the annual budget.
     */
    public function clientUnit()
    {
        return $this->belongsTo(ClientUnit::class, 'client_unit_id');
    }

    /**
     * Get the weekly budgets for this annual budget.
     */
    public function weeklyBudgets()
    {
        return $this->hasMany(ClientUnitWeeklyBudget::class, 'client_unit_annual_budget_id');
    }
}
