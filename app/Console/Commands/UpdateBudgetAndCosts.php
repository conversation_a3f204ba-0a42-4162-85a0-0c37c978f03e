<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Models\ClientUnitWeeklyBudget;
use App\Models\WeeklyUtilisedFundLog;
use App\Http\Controllers\UnitPortal\BookingController;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class UpdateBudgetAndCosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'budget:update-costs 
                            {--batch-size=100 : Number of bookings to process in each batch}
                            {--dry-run : Run without making actual changes}
                            {--summary-only : Show only high-level summary without detailed processing}
                            {--reset-all : Reset all processing flags to reprocess everything}
                            {--booking-id= : Calculate and update initial cost for a specific booking ID}
                            {--unit-id=* : Process bookings only for specific unit IDs (comma-separated or multiple --unit-id options)}
                            {--client-id= : Process bookings only for units belonging to specific client ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update booking initial costs and weekly budget utilisation for all existing bookings, or filter by specific booking ID, unit IDs, or client ID';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Starting Budget and Cost Update Process...');
        
        $dryRun = $this->option('dry-run');
        $summaryOnly = $this->option('summary-only');
        $resetAll = $this->option('reset-all');
        $bookingId = $this->option('booking-id');
        $batchSize = (int) $this->option('batch-size');
        
        // Resolve unit filtering options
        $unitIds = $this->resolveUnitIds();
        
        // Handle single booking ID calculation
        if ($bookingId) {
            $this->info("Calculating and updating costs for booking ID: {$bookingId}");
            $result = self::updateBookingCostsById((int) $bookingId);
            
            if ($result['success']) {
                $this->info("✅ " . $result['message']);
                if (isset($result['unit_final_cost'])) {
                    $this->info("💰 Unit final cost: £" . number_format($result['unit_final_cost'], 2));
                }
                if (isset($result['calculated_cost'])) {
                    $this->info("🧮 Calculated cost: £" . number_format($result['calculated_cost'], 2));
                }
                if (isset($result['fallback_cost'])) {
                    $this->info("🔄 Fallback cost: £" . number_format($result['fallback_cost'], 2));
                }
                $this->info("📊 Final cost used: £" . number_format($result['final_cost'], 2));
                
                // Show duplicate prevention notice
                if (isset($result['duplicate_prevention']) && $result['duplicate_prevention']) {
                    $this->warn("🛡️  DUPLICATE PREVENTION: Budget utilisation was not re-added to prevent duplication");
                }
                
                // Show logging information
                if (isset($result['log_id'])) {
                    $this->info("📝 Logged with ID: " . $result['log_id']);
                }
            } else {
                $this->error("❌ " . $result['message']);
                if (isset($result['error_details'])) {
                    $this->error("🔍 Details: " . $result['error_details']);
                }
            }
            
            return $result['success'] ? 0 : 1;
        }
        
        // If summary-only is specified, force dry-run mode
        if ($summaryOnly) {
            $dryRun = true;
            $this->warn('SUMMARY-ONLY MODE: Showing high-level overview only (fast)');
        } elseif ($dryRun) {
            $this->warn('DRY RUN MODE: No actual changes will be made.');
        }
        
        if ($resetAll && !$dryRun) {
            $this->warn('RESET-ALL MODE: Will reset all processing flags to reprocess everything.');
        }
        
        try {
            // For summary-only, show quick overview
            if ($summaryOnly) {
                $this->showQuickSummary($unitIds);
                return 0;
            }
            
            // Step 0: Reset weekly utilisation data to avoid duplication
            $this->info("\n=== STEP 0: Resetting weekly utilisation data ===");
            $this->resetWeeklyUtilisationData($dryRun, $resetAll, $unitIds);
            
            // Step 1: Update bookings.initial_cost
            $this->info("\n=== STEP 1: Updating initial_cost and unit_final_cost for all bookings ===");
            $this->updateInitialCosts($batchSize, $dryRun, $unitIds);
            
            // Step 2: Update weekly budget utilisation
            $this->info("\n=== STEP 2: Updating weekly budget utilisation ===");
            $this->updateWeeklyBudgetUtilisation($batchSize, $dryRun, $unitIds);
            
            $this->info("\n✅ Budget and Cost Update Process completed successfully!");
            
        } catch (Exception $e) {
            $this->error("\n❌ Error during budget update: " . $e->getMessage());
            Log::error('Budget update command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
        
        return 0;
    }
    
    /**
     * Step 0: Reset weekly utilisation data to avoid duplication
     */
    private function resetWeeklyUtilisationData(bool $dryRun, bool $resetAll = false, array $unitIds = null)
    {
        $this->info("Resetting weekly utilisation data to avoid duplication...");
        
        if ($unitIds !== null) {
            $this->info("🎯 Unit filtering enabled - only affecting specified units");
        }
        
        if ($resetAll && !$dryRun) {
            $this->warn("RESET-ALL: Resetting processing flags for ALL bookings...");
        }
        
        if ($dryRun) {
            // Fast dry run - show what would be reset with unit filtering
            $logCount = DB::table('weekly_utilised_fund_logs')->count();
            $weeklyBudgetCount = DB::table('client_unit_weekly_budgets')->count();
            
            $bookingQuery = DB::table('bookings');
            if ($unitIds !== null) {
                $bookingQuery->whereIn('unitId', $unitIds);
            }
            $bookingCount = $bookingQuery->count();
            
            $annualBudgetCount = DB::table('client_unit_annual_budgets')->count();
            
            $this->info("🔍 DRY RUN: Found data to reset:");
            $this->info("   - {$logCount} weekly fund logs would be cleared");
            $this->info("   - {$weeklyBudgetCount} weekly budgets would be reset");
            if ($unitIds !== null) {
                $this->info("   - {$bookingCount} bookings (filtered by units) would have budget flags reset");
            } else {
                $this->info("   - {$bookingCount} bookings would have budget flags reset");
            }
            $this->info("   - {$annualBudgetCount} annual budgets would trigger recalculation");
            $this->info("⚡ DRY RUN: Skipping actual reset operations for speed");
            return;
        }
        
        if (!$dryRun) {
            try {
                // Step 0a: Truncate weekly_utilised_fund_logs table
                $logCount = DB::table('weekly_utilised_fund_logs')->count();
                $this->info("Found {$logCount} existing fund logs to clear");
                
                DB::table('weekly_utilised_fund_logs')->truncate();
                $this->info("✅ Cleared weekly_utilised_fund_logs table");
                
                // Step 0b: Reset total_weekly_utilisation to 0 for all weekly budgets
                $weeklyBudgetCount = DB::table('client_unit_weekly_budgets')->count();
                $this->info("Found {$weeklyBudgetCount} weekly budgets to reset");
                
                // Update weekly budgets in chunks to avoid memory issues
                $updatedWeeklyBudgets = 0;
                DB::table('client_unit_weekly_budgets')
                    ->chunkById(1000, function ($weeklyBudgets) use (&$updatedWeeklyBudgets) {
                        foreach ($weeklyBudgets as $weeklyBudget) {
                            DB::table('client_unit_weekly_budgets')
                                ->where('id', $weeklyBudget->id)
                                ->update([
                                    'total_weekly_utilisation' => 0,
                                    'percent_utilisation' => 0,
                                    'weekly_unutilised_fund' => $weeklyBudget->total_weekly_allocation,
                                    'updated_at' => now()
                                ]);
                            $updatedWeeklyBudgets++;
                        }
                    });
                
                $this->info("✅ Reset utilisation for {$updatedWeeklyBudgets} weekly budgets");
                
                // Step 0c: Reset budget utilisation flags based on reset-all option
                if ($resetAll) {
                    // Reset processing flags for ALL bookings (or filtered by units)
                    $bookingResetQuery = DB::table('bookings');
                    if ($unitIds !== null) {
                        $bookingResetQuery->whereIn('unitId', $unitIds);
                    }
                    
                    $bookingResetCount = $bookingResetQuery->update([
                        'budget_utilisation_updated' => false,
                        'updated_at' => now()
                    ]);
                    
                    if ($unitIds !== null) {
                        $this->info("✅ Reset processing flags for {$bookingResetCount} bookings in specified units (RESET-ALL mode)");
                    } else {
                        $this->info("✅ Reset all processing flags for {$bookingResetCount} bookings (RESET-ALL mode)");
                    }
                } else {
                    // Reset budget utilisation flags for all bookings (or filtered by units)
                    $bookingResetQuery = DB::table('bookings');
                    if ($unitIds !== null) {
                        $bookingResetQuery->whereIn('unitId', $unitIds);
                    }
                    
                    $bookingResetCount = $bookingResetQuery->update([
                        'budget_utilisation_updated' => false,
                        'updated_at' => now()
                    ]);
                    
                    if ($unitIds !== null) {
                        $this->info("✅ Reset budget_utilisation_updated flag for {$bookingResetCount} bookings in specified units");
                    } else {
                        $this->info("✅ Reset budget_utilisation_updated flag for {$bookingResetCount} bookings");
                    }
                }
                
                // Step 0d: Trigger recalculation for all annual budgets
                $this->info("Triggering recalculation for all annual budgets...");
                $this->triggerRecalculationForAllBudgets();
                
            } catch (Exception $e) {
                throw new Exception("Failed to reset weekly utilisation data: " . $e->getMessage());
            }
        }
        
        $this->info("✅ Step 0 completed: Weekly utilisation data reset");
    }
    
    /**
     * Trigger recalculation for all annual budgets
     */
    private function triggerRecalculationForAllBudgets()
    {
        try {
            // Get all annual budgets with their weekly budgets
            $annualBudgets = DB::table('client_unit_annual_budgets')
                ->select('id', 'client_unit_id', 'financial_year')
                ->get();
            
            $progressBar = $this->output->createProgressBar($annualBudgets->count());
            $progressBar->setFormat('Recalculating: %current%/%max% [%bar%] %percent%%');
            
            foreach ($annualBudgets as $annualBudget) {
                try {
                    // Get all week numbers for this annual budget
                    $weekNumbers = DB::table('client_unit_weekly_budgets')
                        ->where('client_unit_annual_budget_id', $annualBudget->id)
                        ->pluck('week_number')
                        ->unique();
                    
                    // Trigger recalculation for each week
                    foreach ($weekNumbers as $weekNumber) {
                        $controller = new \App\Http\Controllers\ClientPortal\BudgetAllocationController();
                        $reflection = new \ReflectionClass($controller);
                        $method = $reflection->getMethod('recalculateConsolidatedFunds');
                        $method->setAccessible(true);
                        $method->invoke($controller, $annualBudget->id, $weekNumber);
                    }
                    
                } catch (Exception $e) {
                    Log::warning('Failed to recalculate for annual budget', [
                        'annual_budget_id' => $annualBudget->id,
                        'error' => $e->getMessage()
                    ]);
                }
                
                $progressBar->advance();
            }
            
            $progressBar->finish();
            $this->newLine();
            $this->info("✅ Recalculation triggered for {$annualBudgets->count()} annual budgets");
            
        } catch (Exception $e) {
            Log::error('Failed to trigger recalculation for all budgets', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Step 1: Update bookings.initial_cost and bookings.unit_final_cost for all existing bookings
     */
    private function updateInitialCosts(int $batchSize, bool $dryRun, array $unitIds = null)
    {
        // Process bookings with optional unit filtering
        $bookingQuery = Booking::query();
        if ($unitIds !== null) {
            $bookingQuery->whereIn('unitId', $unitIds);
        }
        
        $totalBookings = $bookingQuery->count();
        
        if ($unitIds !== null) {
            $this->info("Found {$totalBookings} bookings in specified units to update initial_cost and unit_final_cost");
        } else {
            $this->info("Found {$totalBookings} bookings to update initial_cost and unit_final_cost");
        }
        
        if ($totalBookings === 0) {
            $this->info("No bookings found. Skipping Step 1.");
            return;
        }
        
        if ($dryRun) {
            // Fast dry run - show what would be done with unit filtering
            $nullInitialCostQuery = clone $bookingQuery;
            $nullInitialCostCount = $nullInitialCostQuery->whereNull('initial_cost')->count();
            
            $nullFinalCostQuery = clone $bookingQuery;
            $nullFinalCostCount = $nullFinalCostQuery->whereNull('unit_final_cost')->count();
            
            $this->info("🔍 DRY RUN: Found {$totalBookings} total bookings" . ($unitIds ? " in specified units" : ""));
            $this->info("🚫 DRY RUN: Found {$nullInitialCostCount} bookings without initial_cost");
            $this->info("🚫 DRY RUN: Found {$nullFinalCostCount} bookings without unit_final_cost");
            $this->info("🔄 DRY RUN: Would calculate initial_cost using calculateCostBookingNumberonly method");
            $this->info("🔄 DRY RUN: Would calculate unit_final_cost using finalcostCalculation method");
            $this->info("🚩 DRY RUN: Would reset budget_utilisation_updated flag for processed bookings");
            $this->info("⚡ DRY RUN: Skipping per-booking calculations for speed");
            $this->info("📊 Step 1 Results (DRY RUN):");
            $this->info("   - Would process: {$totalBookings} bookings");
            $this->info("   - Would update: ~{$totalBookings} bookings (estimated)");
            return;
        }
        
        $processed = 0;
        $updated = 0;
        $errors = 0;
        
        $progressBar = $this->output->createProgressBar($totalBookings);
        $progressBar->setFormat('Processing: %current%/%max% [%bar%] %percent%% - Updated: %message%');
        $progressBar->setMessage($updated);
        
        // Create a booking controller instance to access calculation methods
        $bookingController = new BookingController();
        
        $bookingQuery->chunk($batchSize, function ($bookings) use (&$processed, &$updated, &$errors, $progressBar, $bookingController) {
            foreach ($bookings as $booking) {
                try {
                    // Calculate initial_cost using calculateCostBookingNumberonly method
                    $initialCost = 0;
                    try {
                        $reflection = new \ReflectionClass($bookingController);
                        $method = $reflection->getMethod('calculateCostBookingNumberonly');
                        $method->setAccessible(true);
                        
                        $initialCostString = $method->invoke($bookingController, $booking);
                        $initialCost = (float) str_replace([',', '£', ' '], '', $initialCostString);
                    } catch (Exception $e) {
                        Log::warning('Failed to calculate initial_cost using calculateCostBookingNumberonly', [
                            'booking_id' => $booking->bookingId,
                            'error' => $e->getMessage()
                        ]);
                        // Leave initial_cost as 0 if calculation fails
                    }
                    
                    // Calculate unit_final_cost using finalcostCalculation method
                    $unitFinalCostString = $bookingController->finalcostCalculation($booking);
                    $unitFinalCost = (float) str_replace([',', '£', ' '], '', $unitFinalCostString);
                    
                    // Update the booking with both calculated costs and reset budget flag
                    $booking->update([
                        'initial_cost' => $initialCost > 0 ? $initialCost : null,
                        'unit_final_cost' => $unitFinalCost > 0 ? $unitFinalCost : null,
                        'budget_utilisation_updated' => false
                    ]);
                    
                    // Log the cost update
                    $this->logBudgetUpdateActivity($booking, [
                        'status' => 'updated',
                        'cost_source' => 'step1_dual_calculation',
                        'final_cost' => $unitFinalCost > 0 ? $unitFinalCost : $initialCost,
                        'unit_final_cost' => $unitFinalCost > 0 ? $unitFinalCost : null,
                        'adjustment_type' => null
                    ]);
                    
                    $updated++;
                    $progressBar->setMessage($updated);
                    
                } catch (Exception $e) {
                    $errors++;
                    
                    // Log the error in our tracking table
                    try {
                        $this->logBudgetUpdateActivity($booking, [
                            'status' => 'failed',
                            'cost_source' => 'step1_cost_calculation',
                            'error_message' => $e->getMessage()
                        ]);
                    } catch (Exception $logError) {
                        // If logging fails, just continue
                    }
                    
                    // Only log errors for live runs to improve performance
                    Log::error('Failed to update costs for booking in Step 1', [
                        'booking_id' => $booking->bookingId,
                        'error' => $e->getMessage()
                    ]);
                }
                
                $processed++;
                $progressBar->advance();
            }
        });
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info("Step 1 Results:");
        $this->info("- Processed: {$processed} bookings");
        $this->info("- Updated: {$updated} bookings");
        
        if ($errors > 0) {
            $this->warn("- Errors: {$errors} bookings (check logs for details)");
        }
    }
    
    /**
     * Step 2: Update weekly budget utilisation for eligible bookings
     */
    private function updateWeeklyBudgetUtilisation(int $batchSize, bool $dryRun, array $unitIds = null)
    {
        // Find bookings that need budget utilisation update with optional unit filtering
        $baseQuery = Booking::whereIn('unitStatus', [1, 4]);
        if ($unitIds !== null) {
            $baseQuery->whereIn('unitId', $unitIds);
        }
        
        // Get bookings that are either:
        // 1. Not processed yet (budget_utilisation_updated = false)
        // 2. Processed but without unit_final_cost (need to check if they now have unit_final_cost)
        $eligibleBookingsQuery = $baseQuery->where(function($query) {
            $query->where('budget_utilisation_updated', false)
                  ->orWhere(function($subQuery) {
                      // Already processed but check if they now have unit_final_cost and weren't processed with it
                      $subQuery->where('budget_utilisation_updated', true)
                               ->whereNotNull('unit_final_cost')
                               ->where('unit_final_cost', '>', 0)
                               ->whereNotExists(function($logQuery) {
                                   $logQuery->select(DB::raw(1))
                                            ->from('weekly_budget_update_script_logs')
                                            ->whereColumn('weekly_budget_update_script_logs.booking_id', 'bookings.bookingId')
                                            ->where('weekly_budget_update_script_logs.status', 'success')
                                            ->whereNotNull('weekly_budget_update_script_logs.unit_final_cost')
                                            ->where('weekly_budget_update_script_logs.unit_final_cost', '>', 0);
                               });
                  });
        });
        
        $eligibleBookings = $eligibleBookingsQuery->count();
            
        if ($unitIds !== null) {
            $this->info("Found {$eligibleBookings} eligible bookings in specified units for budget utilisation update");
        } else {
            $this->info("Found {$eligibleBookings} eligible bookings for budget utilisation update");
        }
        
        if ($eligibleBookings === 0) {
            $this->info("No eligible bookings found. Skipping Step 2.");
            return;
        }
        
        if ($dryRun) {
            // Fast dry run - show summary with unit filtering
            $withUnitFinalCostQuery = Booking::where('budget_utilisation_updated', false)
                ->whereIn('unitStatus', [1, 4])
                ->whereNotNull('unit_final_cost')
                ->where('unit_final_cost', '>', 0);
            
            $withInitialCostQuery = Booking::where('budget_utilisation_updated', false)
                ->whereIn('unitStatus', [1, 4])
                ->where(function($query) {
                    $query->whereNull('unit_final_cost')
                          ->orWhere('unit_final_cost', '<=', 0);
                })
                ->whereNotNull('initial_cost')
                ->where('initial_cost', '>', 0);
            
            if ($unitIds !== null) {
                $withUnitFinalCostQuery->whereIn('unitId', $unitIds);
                $withInitialCostQuery->whereIn('unitId', $unitIds);
            }
            
            $withUnitFinalCost = $withUnitFinalCostQuery->count();
            $withInitialCost = $withInitialCostQuery->count();

            $this->info("🔍 DRY RUN: Found {$eligibleBookings} eligible bookings for budget utilisation update" . ($unitIds ? " in specified units" : ""));
            $this->info("💰 DRY RUN: {$withUnitFinalCost} bookings would use unit_final_cost");
            $this->info("💱 DRY RUN: {$withInitialCost} bookings would use fallback costs (finalcostCalculation or initial_cost)");
            $this->info("⚡ DRY RUN: Skipping per-booking processing for speed");
            $this->info("📊 Step 2 Results (DRY RUN):");
            $this->info("   - Would process: {$eligibleBookings} eligible bookings");
            $this->info("   - Would update: ~{$eligibleBookings} bookings (estimated)");
            return;
        }
        
        $processed = 0;
        $updated = 0;
        $errors = 0;
        
        $progressBar = $this->output->createProgressBar($eligibleBookings);
        $progressBar->setFormat('Processing: %current%/%max% [%bar%] %percent%% - Updated: %message%');
        $progressBar->setMessage($updated);
        
        $bookingQuery = Booking::where('budget_utilisation_updated', false)
            ->whereIn('unitStatus', [1, 4]);
        
        if ($unitIds !== null) {
            $bookingQuery->whereIn('unitId', $unitIds);
        }
        
        $bookingQuery->chunk($batchSize, function ($bookings) use (&$processed, &$updated, &$errors, $progressBar) {
                
                foreach ($bookings as $booking) {
                    try {
                        // Determine the cost to use for budget utilisation
                        $costResult = $this->determineCostForBudgetUtilisation($booking);
                        $costToUse = $costResult['cost'];
                        $costSource = $costResult['source'];
                        
                        if ($costToUse <= 0) {
                            // Log the skipped booking
                            $this->logBudgetUpdateActivity($booking, [
                                'status' => 'skipped',
                                'cost_source' => $costSource,
                                'final_cost' => $costToUse,
                                'error_message' => $costToUse < 0 ? 'Negative cost detected' : 'Zero cost detected'
                            ]);
                            
                            $processed++;
                            $progressBar->advance();
                            continue;
                        }
                        
                        // Check if this booking has already been processed with unit_final_cost to avoid duplication
                        $alreadyProcessedWithFinalCost = false;
                        
                        // Check if it was processed with unit_final_cost (check both booking status and logs)
                        $hasUnitFinalCost = !is_null($booking->unit_final_cost) && $booking->unit_final_cost > 0;
                        
                        // Check logs to see if it was processed with unit_final_cost
                        $hasLoggedUnitFinalCost = DB::table('weekly_budget_update_script_logs')
                            ->where('booking_id', $booking->bookingId)
                            ->where('status', 'success')
                            ->whereNotNull('unit_final_cost')
                            ->where('unit_final_cost', '>', 0)
                            ->exists();
                        
                        // If booking is marked as processed AND has valid unit_final_cost, or if logs show successful processing with unit_final_cost
                        if (($booking->budget_utilisation_updated && $hasUnitFinalCost) || $hasLoggedUnitFinalCost) {
                            $alreadyProcessedWithFinalCost = true;
                        }
                        
                        if ($alreadyProcessedWithFinalCost) {
                            // Log as skipped to avoid duplication
                            $this->logBudgetUpdateActivity($booking, [
                                'status' => 'skipped',
                                'cost_source' => $costSource,
                                'final_cost' => $costToUse,
                                'error_message' => 'Already processed with unit_final_cost - avoiding duplication'
                            ]);
                            
                            $processed++;
                            $progressBar->advance();
                            continue;
                        }
                        
                        DB::beginTransaction();
                        
                        try {
                            // Determine adjustment type based on existing processing
                            $adjustmentType = 'addition';
                            
                            // Check if this is an update scenario
                            $existingLog = DB::table('weekly_budget_update_script_logs')
                                ->where('booking_id', $booking->bookingId)
                                ->where('status', 'success')
                                ->orderBy('created_at', 'desc')
                                ->first();
                            
                            if ($existingLog && $existingLog->unit_final_cost > 0 && $existingLog->unit_final_cost != $costToUse) {
                                $adjustmentType = 'updation';
                            }
                            
                            // Call the helper function to update weekly utilisation
                            updateWeeklyUtilisedFund(
                                null, // weeklyBudgetId - let the function find it from booking
                                $costToUse,
                                $adjustmentType,
                                'App\Models\Booking',
                                $booking->bookingId,
                                '[SCRIPT BASED] Budget utilization update for booking ID: ' . $booking->bookingId .
                                ' | Unit: ' . ($booking->unit->name ?? 'N/A') .
                                ' | Staff: ' . ($booking->staff->forname ?? 'N/A') . ' ' . ($booking->staff->surname ?? '') .
                                ' | Date: ' . $booking->date .
                                ' | Shift: ' . ($booking->shift->name ?? 'N/A') .
                                ' | Category: ' . ($booking->category->name ?? 'N/A') .
                                ' | Cost Source: ' . $costSource .
                                ' | Adjustment Type: ' . $adjustmentType .
                                ' | Cost: £' . number_format($costToUse, 2)
                            );
                            
                            // Mark the booking as processed
                            $booking->update(['budget_utilisation_updated' => true]);
                            
                            // Log the successful update
                            $this->logBudgetUpdateActivity($booking, [
                                'status' => 'success',
                                'cost_source' => $costSource,
                                'final_cost' => $costToUse,
                                'adjustment_type' => $adjustmentType
                            ]);
                            
                            DB::commit();
                            $updated++;
                            
                        } catch (Exception $e) {
                            DB::rollback();
                            
                            // Log the failed update
                            $this->logBudgetUpdateActivity($booking, [
                                'status' => 'failed',
                                'cost_source' => $costSource,
                                'final_cost' => $costToUse,
                                'error_message' => $e->getMessage()
                            ]);
                            
                            throw $e;
                        }
                        
                        $progressBar->setMessage($updated);
                        
                    } catch (Exception $e) {
                        $errors++;
                        Log::error('Failed to update weekly budget utilisation for booking', [
                            'booking_id' => $booking->bookingId,
                            'error' => $e->getMessage()
                        ]);
                        
                        // Log the error in our tracking table
                        try {
                            $this->logBudgetUpdateActivity($booking, [
                                'status' => 'failed',
                                'error_message' => $e->getMessage()
                            ]);
                        } catch (Exception $logError) {
                            Log::error('Failed to log budget update activity', [
                                'booking_id' => $booking->bookingId,
                                'original_error' => $e->getMessage(),
                                'log_error' => $logError->getMessage()
                            ]);
                        }
                    }
                    
                    $processed++;
                    $progressBar->advance();
                }
            });
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info("Step 2 Results:");
        $this->info("- Processed: {$processed} eligible bookings");
        $this->info("- Updated: {$updated} bookings");
        
        if ($errors > 0) {
            $this->warn("- Errors: {$errors} bookings (check logs for details)");
        }
    }
    
    /**
     * Calculate booking cost using the finalcostCalculation method
     */
    private function calculateBookingCost(Booking $booking, BookingController $controller)
    {
        try {
            // Use the finalcostCalculation method directly
            $costString = $controller->finalcostCalculation($booking);
            
            // The method returns a formatted string, extract the numeric value
            $cost = (float) str_replace([',', '£', ' '], '', $costString);
            
            return $cost;
            
        } catch (Exception $e) {
            Log::warning('Failed to calculate booking cost using finalcostCalculation', [
                'booking_id' => $booking->bookingId,
                'error' => $e->getMessage()
            ]);
            
            // Try fallback to calculateCostBookingNumberonly method if available
            try {
                $reflection = new \ReflectionClass($controller);
                $method = $reflection->getMethod('calculateCostBookingNumberonly');
                $method->setAccessible(true);
                
                $costString = $method->invoke($controller, $booking);
                $cost = (float) str_replace([',', '£', ' '], '', $costString);
                
                return $cost;
            } catch (Exception $fallbackError) {
                Log::error('Both cost calculation methods failed', [
                    'booking_id' => $booking->bookingId,
                    'primary_error' => $e->getMessage(),
                    'fallback_error' => $fallbackError->getMessage()
                ]);
            }
            
            // Return 0 as final fallback
            return 0.00;
        }
    }
    
    /**
     * Determine which cost to use for budget utilisation update
     * Priority: unit_final_cost > finalcostCalculation > initial_cost
     * 
     * @param Booking $booking
     * @return array ['cost' => float, 'source' => string]
     */
    private function determineCostForBudgetUtilisation(Booking $booking)
    {
        // Priority 1: Use unit_final_cost if it's non-null and greater than 0
        if (!is_null($booking->unit_final_cost) && $booking->unit_final_cost > 0) {
            return [
                'cost' => (float) $booking->unit_final_cost,
                'source' => 'unit_final_cost'
            ];
        }
        
        // Priority 2: Use finalcostCalculation method if unit_final_cost is not available
        try {
            $bookingController = new BookingController();
            $calculatedCostString = $bookingController->finalcostCalculation($booking);
            $calculatedCost = (float) str_replace([',', '£', ' '], '', $calculatedCostString);
            
            if ($calculatedCost > 0) {
                return [
                    'cost' => $calculatedCost,
                    'source' => 'finalcostCalculation'
                ];
            }
        } catch (Exception $e) {
            Log::warning('Failed to calculate final cost for booking', [
                'booking_id' => $booking->bookingId,
                'error' => $e->getMessage()
            ]);
        }
        
        // Priority 3: Fallback to initial_cost
        $initialCost = (float) ($booking->initial_cost ?? 0);
        return [
            'cost' => $initialCost,
            'source' => $initialCost > 0 ? 'initial_cost' : 'none_available'
        ];
    }
    
    /**
     * Show a quick summary without detailed processing (very fast)
     */
    private function showQuickSummary(array $unitIds = null)
    {
        $this->info("=== QUICK SUMMARY (Read-only Overview) ===\n");
        
        if ($unitIds !== null) {
            $this->info("🎯 Unit filtering enabled for units: " . implode(', ', $unitIds) . "\n");
        }
        
        // Count total bookings with optional unit filtering
        $totalBookingsQuery = Booking::query();
        if ($unitIds !== null) {
            $totalBookingsQuery->whereIn('unitId', $unitIds);
        }
        $totalBookings = $totalBookingsQuery->count();
        
        $this->info("📊 Total Bookings" . ($unitIds ? " (filtered)" : "") . ": {$totalBookings}");
        
        // Count bookings with null initial_cost
        $nullInitialCostQuery = clone $totalBookingsQuery;
        $nullInitialCost = $nullInitialCostQuery->whereNull('initial_cost')->count();
        $nonNullInitialCost = $totalBookings - $nullInitialCost;
        
        $this->info("💰 Bookings with initial_cost: {$nonNullInitialCost}");
        $this->info("🚫 Bookings without initial_cost: {$nullInitialCost}");
        
        // Count bookings eligible for budget utilisation update
        $eligibleQuery = Booking::whereIn('unitStatus', [1, 4])
            ->where('budget_utilisation_updated', false);
        if ($unitIds !== null) {
            $eligibleQuery->whereIn('unitId', $unitIds);
        }
        $eligibleForBudgetUpdate = $eligibleQuery->count();
        
        $this->info("🎯 Bookings eligible for budget utilisation update: {$eligibleForBudgetUpdate}");
        
        // Count existing weekly fund logs
        $weeklyLogs = DB::table('weekly_utilised_fund_logs')->count();
        $this->info("📝 Existing weekly fund logs: {$weeklyLogs}");
        
        // Count weekly budgets
        $weeklyBudgets = DB::table('client_unit_weekly_budgets')->count();
        $this->info("📅 Total weekly budgets: {$weeklyBudgets}");
        
        // Show what would happen
        $this->warn("\n🔄 ACTIONS THAT WOULD BE PERFORMED:");
        $this->line("  • Reset {$weeklyLogs} weekly fund logs");
        $this->line("  • Reset {$weeklyBudgets} weekly budget utilizations");
        if ($unitIds !== null) {
            $this->line("  • Update budget utilisation for {$eligibleForBudgetUpdate} bookings in specified units");
        } else {
            $this->line("  • Update budget utilisation for {$eligibleForBudgetUpdate} bookings");
        }
        
        $this->info("\n⚡ For detailed processing, run without --summary-only");
        $this->info("🚀 For safe testing, add --dry-run");
    }
    
    /**
     * Log budget update activity in the tracking table
     * 
     * @param Booking $booking
     * @param array $data
     * @return int|null Log ID if successful
     */
    private function logBudgetUpdateActivity(Booking $booking, array $data)
    {
        try {
            // Get the weekly budget if available
            $weeklyBudgetId = null;
            $totalWeeklyUtilisation = null;
            
            try {
                $weeklyBudget = ClientUnitWeeklyBudget::where('client_unit_id', $booking->unitId)
                    ->where('week_number', date('W', strtotime($booking->date)))
                    ->first();
                
                if ($weeklyBudget) {
                    $weeklyBudgetId = $weeklyBudget->id;
                    $totalWeeklyUtilisation = $weeklyBudget->total_weekly_utilisation;
                }
            } catch (Exception $e) {
                // Continue without weekly budget info if we can't find it
                Log::info('Could not find weekly budget for logging', [
                    'booking_id' => $booking->bookingId,
                    'unit_id' => $booking->unitId,
                    'week_number' => date('W', strtotime($booking->date)),
                    'error' => $e->getMessage()
                ]);
            }
            
            // Prepare booking details snapshot - ensure relationships are loaded
            $booking->loadMissing(['unit', 'staff', 'shift', 'category']);
            
            $bookingDetails = [
                'booking_id' => $booking->bookingId,
                'date' => $booking->date,
                'unit_name' => $booking->unit->name ?? 'N/A',
                'staff_name' => ($booking->staff->forname ?? 'N/A') . ' ' . ($booking->staff->surname ?? ''),
                'shift_name' => $booking->shift->name ?? 'N/A',
                'category_name' => $booking->category->name ?? 'N/A',
                'unit_status' => $booking->unitStatus,
                'staff_status' => $booking->staffStatus
            ];
            
            // Insert log entry
            $logId = DB::table('weekly_budget_update_script_logs')->insertGetId([
                'booking_id' => $booking->bookingId,
                'booking_details' => json_encode($bookingDetails),
                'initial_value' => $booking->initial_cost,
                'unit_final_cost' => $data['final_cost'] ?? null,
                'status' => $data['status'] ?? 'unknown',
                'total_weekly_utilisation' => $totalWeeklyUtilisation,
                'client_unit_weekly_budget_id' => $weeklyBudgetId,
                'adjustment_type' => $data['adjustment_type'] ?? 'addition', // Default to 'addition' if not specified
                'error_log' => $data['error_message'] ?? null,
                'processed_count' => 1,
                'last_processed_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            Log::info('Successfully logged budget update activity', [
                'booking_id' => $booking->bookingId,
                'log_id' => $logId,
                'status' => $data['status'] ?? 'unknown'
            ]);
            
            return $logId;
            
        } catch (Exception $e) {
            Log::error('Failed to log budget update activity', [
                'booking_id' => $booking->bookingId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);
            return null;
        }
    }

    /**
     * Calculate and update costs for a specific booking by ID (enhanced version)
     * 
     * @param int $bookingId
     * @return array
     */
    public static function updateBookingCostsById(int $bookingId)
    {
        try {
            // Find the booking by ID
            $booking = Booking::with(['unit', 'staff', 'shift', 'category'])->find($bookingId);
            
            if (!$booking) {
                return [
                    'success' => false,
                    'message' => "Booking not found for ID: {$bookingId}",
                    'error_details' => 'No booking record exists with this ID'
                ];
            }
            
            $result = [
                'success' => true,
                'message' => "Successfully processed booking ID: {$bookingId}",
                'booking_id' => $bookingId
            ];
            
            $command = new self();
            
            // First, calculate and update unit_final_cost using finalcostCalculation
            try {
                $bookingController = new BookingController();
                $calculatedCostString = $bookingController->finalcostCalculation($booking);
                $calculatedCost = (float) str_replace([',', '£', ' '], '', $calculatedCostString);
                
                // Only update unit_final_cost if the calculation returned a valid result (> 0)
                if ($calculatedCost > 0) {
                    $booking->update([
                        'unit_final_cost' => $calculatedCost,
                        'budget_utilisation_updated' => false // Reset for re-processing
                    ]);
                    
                    $result['calculated_cost'] = $calculatedCost;
                    $result['message'] .= " and updated unit_final_cost to £" . number_format($calculatedCost, 2);
                } else {
                    $result['calculation_note'] = 'finalcostCalculation returned 0 (booking not eligible for calculation)';
                    $result['message'] .= " but unit_final_cost not updated (finalcostCalculation returned 0)";
                }
                
            } catch (Exception $e) {
                Log::warning('Failed to calculate unit_final_cost', [
                    'booking_id' => $bookingId,
                    'error' => $e->getMessage()
                ]);
                $result['calculation_error'] = $e->getMessage();
                $result['message'] .= " but unit_final_cost calculation failed";
            }
            
            // Now get cost information with priority logic (should use the newly calculated unit_final_cost)
            $costResult = $command->determineCostForBudgetUtilisation($booking->fresh()); // Fresh to get updated data
            $finalCost = $costResult['cost'];
            $costSource = $costResult['source'];
            
            $result['final_cost'] = $finalCost;
            $result['cost_source'] = $costSource;
            
            // Show detailed cost breakdown based on source
            if ($costSource === 'unit_final_cost') {
                $result['unit_final_cost'] = $booking->fresh()->unit_final_cost;
            } elseif ($costSource === 'finalcostCalculation') {
                $bookingController = new BookingController();
                $calculatedCostString = $bookingController->finalcostCalculation($booking);
                $result['calculated_cost'] = (float) str_replace([',', '£', ' '], '', $calculatedCostString);
            } elseif ($costSource === 'initial_cost') {
                $result['fallback_cost'] = $booking->initial_cost;
            }
            
            // Check if this booking has already been processed to avoid duplication
            $alreadyProcessed = false;
            $processingReason = '';
            
            // Check if it was processed with unit_final_cost (check both booking status and logs)
            $hasUnitFinalCost = !is_null($booking->unit_final_cost) && $booking->unit_final_cost > 0;
            
            // Check logs to see if it was processed with unit_final_cost
            $hasLoggedUnitFinalCost = DB::table('weekly_budget_update_script_logs')
                ->where('booking_id', $booking->bookingId)
                ->where('status', 'success')
                ->whereNotNull('unit_final_cost')
                ->where('unit_final_cost', '>', 0)
                ->exists();
            
            // If booking is marked as processed AND has valid unit_final_cost, or if logs show successful processing with unit_final_cost
            if (($booking->budget_utilisation_updated && $hasUnitFinalCost) || $hasLoggedUnitFinalCost) {
                $alreadyProcessed = true;
                $processingReason = 'Already processed with unit_final_cost - avoiding duplication';
                $result['duplicate_prevention'] = true;
            }
            
            // Update the booking's budget utilisation if eligible and not already processed
            if (in_array($booking->unitStatus, [1, 4]) && $finalCost > 0 && !$alreadyProcessed) {
                DB::beginTransaction();
                
                try {
                    // Determine adjustment type based on existing cost
                    $adjustmentType = 'addition';
                    $existingCost = 0;
                    
                    // Check if booking already has utilisation in the system
                    $existingLog = DB::table('weekly_budget_update_script_logs')
                        ->where('booking_id', $booking->bookingId)
                        ->where('status', 'success')
                        ->orderBy('created_at', 'desc')
                        ->first();
                    
                    if ($existingLog && $existingLog->unit_final_cost > 0) {
                        $existingCost = $existingLog->unit_final_cost;
                        if ($finalCost != $existingCost) {
                            $adjustmentType = 'updation';
                            $result['message'] .= " (updating from £" . number_format($existingCost, 2) . " to £" . number_format($finalCost, 2) . ")";
                        } else {
                            // Same cost, skip to avoid duplication
                            $alreadyProcessed = true;
                            $processingReason = 'Same cost already processed - avoiding duplication';
                        }
                    }
                    
                    if (!$alreadyProcessed) {
                        // Call the helper function to update weekly utilisation
                        updateWeeklyUtilisedFund(
                            null, // weeklyBudgetId - let the function find it from booking
                            $finalCost,
                            $adjustmentType,
                            'App\Models\Booking',
                            $booking->bookingId,
                            '[MANUAL BOOKING UPDATE] Budget utilization update for booking ID: ' . $booking->bookingId .
                            ' | Cost Source: ' . $costSource .
                            ' | Adjustment Type: ' . $adjustmentType .
                            ' | Cost: £' . number_format($finalCost, 2) .
                            ($existingCost > 0 ? ' | Previous Cost: £' . number_format($existingCost, 2) : '')
                        );
                        
                        // Mark the booking as processed
                        $booking->update(['budget_utilisation_updated' => true]);
                        
                        // Log the successful update
                        $logId = $command->logBudgetUpdateActivity($booking->fresh(), [
                            'status' => 'success',
                            'cost_source' => $costSource,
                            'final_cost' => $finalCost,
                            'adjustment_type' => $adjustmentType
                        ]);
                        
                        if ($logId) {
                            $result['log_id'] = $logId;
                        }
                        
                        DB::commit();
                        $result['message'] .= ' and updated budget utilisation';
                    } else {
                        // Additional check for same cost scenario - mark as processed inside transaction
                        $result['message'] .= ' but skipped budget utilisation update (same cost already processed)';
                    }
                    
                } catch (Exception $e) {
                    DB::rollback();
                    
                    // Log the failed update
                    $command->logBudgetUpdateActivity($booking->fresh(), [
                        'status' => 'failed',
                        'cost_source' => $costSource,
                        'final_cost' => $finalCost,
                        'error_message' => $e->getMessage()
                    ]);
                    
                    $result['message'] .= ' but failed to update budget utilisation';
                    $result['error_details'] = $e->getMessage();
                }
            } else {
                // Log as skipped
                $skipReason = '';
                if (!in_array($booking->unitStatus, [1, 4])) {
                    $skipReason = 'Booking not eligible (unitStatus not 1 or 4)';
                } elseif ($finalCost <= 0) {
                    $skipReason = 'Zero or negative cost';
                } elseif ($alreadyProcessed) {
                    $skipReason = $processingReason;
                }
                    
                $command->logBudgetUpdateActivity($booking->fresh(), [
                    'status' => 'skipped',
                    'cost_source' => $costSource,
                    'final_cost' => $finalCost,
                    'error_message' => $skipReason
                ]);
                
                $result['message'] .= ' but skipped budget utilisation (' . $skipReason . ')';
                
                // If skipped due to duplication, add a specific note
                if ($alreadyProcessed) {
                    $result['duplicate_prevention'] = true;
                    $result['message'] .= ' - DUPLICATE PREVENTED';
                }
            }
            
            return $result;
            
        } catch (Exception $e) {
            Log::error('Failed to update costs for booking ID', [
                'booking_id' => $bookingId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => "Error processing booking ID {$bookingId}",
                'error_details' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Resolve unit IDs from command options (unit-id or client-id)
     * 
     * @return array|null Array of unit IDs to filter by, or null for no filtering
     */
    private function resolveUnitIds()
    {
        $unitIds = $this->option('unit-id');
        $clientId = $this->option('client-id');
        
        // If specific unit IDs are provided
        if (!empty($unitIds)) {
            $resolvedUnitIds = [];
            
            foreach ($unitIds as $unitIdInput) {
                // Handle comma-separated values within a single option
                if (strpos($unitIdInput, ',') !== false) {
                    $commaSeparated = array_map('trim', explode(',', $unitIdInput));
                    $resolvedUnitIds = array_merge($resolvedUnitIds, $commaSeparated);
                } else {
                    $resolvedUnitIds[] = trim($unitIdInput);
                }
            }
            
            // Convert to integers and remove duplicates
            $resolvedUnitIds = array_unique(array_map('intval', array_filter($resolvedUnitIds)));
            
            $this->info("🎯 Filtering by unit IDs: " . implode(', ', $resolvedUnitIds));
            return $resolvedUnitIds;
        }
        
        // If client ID is provided, get all units for that client
        if (!empty($clientId)) {
            try {
                $clientUnits = DB::table('client_units')
                    ->where('clientId', (int) $clientId)
                    ->whereNull('deleted_at')
                    ->pluck('clientUnitId')
                    ->toArray();
                
                if (empty($clientUnits)) {
                    $this->warn("⚠️  No units found for client ID: {$clientId}");
                    return [];
                }
                
                $this->info("🎯 Filtering by client ID {$clientId} - found units: " . implode(', ', $clientUnits));
                return $clientUnits;
                
            } catch (Exception $e) {
                $this->error("❌ Error resolving units for client ID {$clientId}: " . $e->getMessage());
                return [];
            }
        }
        
        // No filtering
        return null;
    }
}
