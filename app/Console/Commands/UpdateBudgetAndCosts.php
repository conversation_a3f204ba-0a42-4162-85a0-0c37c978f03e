<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Models\ClientUnitWeeklyBudget;
use App\Models\WeeklyUtilisedFundLog;
use App\Http\Controllers\UnitPortal\BookingController;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class UpdateBudgetAndCosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'budget:update-costs 
                            {--batch-size=100 : Number of bookings to process in each batch}
                            {--dry-run : Run without making actual changes}
                            {--summary-only : Show only high-level summary without detailed processing}
                            {--reset-all : Reset all processing flags to reprocess everything}
                            {--booking-id= : Calculate and update initial cost for a specific booking ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update booking initial costs and weekly budget utilisation for all existing bookings, or calculate cost for a specific booking ID';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Starting Budget and Cost Update Process...');
        
        $dryRun = $this->option('dry-run');
        $summaryOnly = $this->option('summary-only');
        $resetAll = $this->option('reset-all');
        $bookingId = $this->option('booking-id');
        $batchSize = (int) $this->option('batch-size');
        
        // Handle single booking ID calculation
        if ($bookingId) {
            $this->info("Calculating initial cost for booking ID: {$bookingId}");
            $result = self::updateInitialCostByBookingId((int) $bookingId);
            
            if ($result['success']) {
                $this->info("✅ " . $result['message']);
                $this->info("💰 Calculated initial cost: £" . number_format($result['cost'], 2));
            } else {
                $this->error("❌ " . $result['message']);
            }
            
            return $result['success'] ? 0 : 1;
        }
        
        // If summary-only is specified, force dry-run mode
        if ($summaryOnly) {
            $dryRun = true;
            $this->warn('SUMMARY-ONLY MODE: Showing high-level overview only (fast)');
        } elseif ($dryRun) {
            $this->warn('DRY RUN MODE: No actual changes will be made.');
        }
        
        if ($resetAll && !$dryRun) {
            $this->warn('RESET-ALL MODE: Will reset all processing flags to reprocess everything.');
        }
        
        try {
            // For summary-only, show quick overview
            if ($summaryOnly) {
                $this->showQuickSummary();
                return 0;
            }
            
            // Step 0: Reset weekly utilisation data to avoid duplication
            $this->info("\n=== STEP 0: Resetting weekly utilisation data ===");
            $this->resetWeeklyUtilisationData($dryRun, $resetAll);
            
            // Step 1: Update bookings.initial_cost
            $this->info("\n=== STEP 1: Updating initial_cost for all bookings ===");
            $this->updateInitialCosts($batchSize, $dryRun);
            
            // Step 2: Update weekly budget utilisation
            $this->info("\n=== STEP 2: Updating weekly budget utilisation ===");
            $this->updateWeeklyBudgetUtilisation($batchSize, $dryRun);
            
            $this->info("\n✅ Budget and Cost Update Process completed successfully!");
            
        } catch (Exception $e) {
            $this->error("\n❌ Error during budget update: " . $e->getMessage());
            Log::error('Budget update command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
        
        return 0;
    }
    
    /**
     * Step 0: Reset weekly utilisation data to avoid duplication
     */
    private function resetWeeklyUtilisationData(bool $dryRun, bool $resetAll = false)
    {
        $this->info("Resetting weekly utilisation data to avoid duplication...");
        
        if ($resetAll && !$dryRun) {
            $this->warn("RESET-ALL: Resetting processing flags for ALL bookings...");
        }
        
        if ($dryRun) {
            // Fast dry run - just show what would be reset without detailed processing
            $logCount = DB::table('weekly_utilised_fund_logs')->count();
            $weeklyBudgetCount = DB::table('client_unit_weekly_budgets')->count();
            $unprocessedBookingCount = DB::table('bookings')->where('is_update_initial_cost_processed', false)->count();
            $annualBudgetCount = DB::table('client_unit_annual_budgets')->count();
            
            $this->info("🔍 DRY RUN: Found data to reset:");
            $this->info("   - {$logCount} weekly fund logs would be cleared");
            $this->info("   - {$weeklyBudgetCount} weekly budgets would be reset");
            $this->info("   - {$unprocessedBookingCount} unprocessed bookings would have budget flags reset");
            $this->info("   - {$annualBudgetCount} annual budgets would trigger recalculation");
            $this->info("⚡ DRY RUN: Skipping actual reset operations for speed");
            return;
        }
        
        if (!$dryRun) {
            try {
                // Step 0a: Truncate weekly_utilised_fund_logs table
                $logCount = DB::table('weekly_utilised_fund_logs')->count();
                $this->info("Found {$logCount} existing fund logs to clear");
                
                DB::table('weekly_utilised_fund_logs')->truncate();
                $this->info("✅ Cleared weekly_utilised_fund_logs table");
                
                // Step 0b: Reset total_weekly_utilisation to 0 for all weekly budgets
                $weeklyBudgetCount = DB::table('client_unit_weekly_budgets')->count();
                $this->info("Found {$weeklyBudgetCount} weekly budgets to reset");
                
                // Update weekly budgets in chunks to avoid memory issues
                $updatedWeeklyBudgets = 0;
                DB::table('client_unit_weekly_budgets')
                    ->chunkById(1000, function ($weeklyBudgets) use (&$updatedWeeklyBudgets) {
                        foreach ($weeklyBudgets as $weeklyBudget) {
                            DB::table('client_unit_weekly_budgets')
                                ->where('id', $weeklyBudget->id)
                                ->update([
                                    'total_weekly_utilisation' => 0,
                                    'percent_utilisation' => 0,
                                    'weekly_unutilised_fund' => $weeklyBudget->total_weekly_allocation,
                                    'updated_at' => now()
                                ]);
                            $updatedWeeklyBudgets++;
                        }
                    });
                
                $this->info("✅ Reset utilisation for {$updatedWeeklyBudgets} weekly budgets");
                
                // Step 0c: Reset budget utilisation flags based on reset-all option
                if ($resetAll) {
                    // Reset processing flags for ALL bookings
                    $bookingResetCount = DB::table('bookings')
                        ->update([
                            'budget_utilisation_updated' => false,
                            'is_update_initial_cost_processed' => false,
                            'updated_at' => now()
                        ]);
                    $this->info("✅ Reset all processing flags for {$bookingResetCount} bookings (RESET-ALL mode)");
                } else {
                    // Reset budget utilisation flags only for unprocessed bookings
                    $bookingResetCount = DB::table('bookings')
                        ->where('is_update_initial_cost_processed', false) // Only reset unprocessed bookings
                        ->update([
                            'budget_utilisation_updated' => false,
                            'updated_at' => now()
                        ]);
                    $this->info("✅ Reset budget_utilisation_updated flag for {$bookingResetCount} unprocessed bookings");
                }
                
                // Step 0d: Trigger recalculation for all annual budgets
                $this->info("Triggering recalculation for all annual budgets...");
                $this->triggerRecalculationForAllBudgets();
                
            } catch (Exception $e) {
                throw new Exception("Failed to reset weekly utilisation data: " . $e->getMessage());
            }
        }
        
        $this->info("✅ Step 0 completed: Weekly utilisation data reset");
    }
    
    /**
     * Trigger recalculation for all annual budgets
     */
    private function triggerRecalculationForAllBudgets()
    {
        try {
            // Get all annual budgets with their weekly budgets
            $annualBudgets = DB::table('client_unit_annual_budgets')
                ->select('id', 'client_unit_id', 'financial_year')
                ->get();
            
            $progressBar = $this->output->createProgressBar($annualBudgets->count());
            $progressBar->setFormat('Recalculating: %current%/%max% [%bar%] %percent%%');
            
            foreach ($annualBudgets as $annualBudget) {
                try {
                    // Get all week numbers for this annual budget
                    $weekNumbers = DB::table('client_unit_weekly_budgets')
                        ->where('client_unit_annual_budget_id', $annualBudget->id)
                        ->pluck('week_number')
                        ->unique();
                    
                    // Trigger recalculation for each week
                    foreach ($weekNumbers as $weekNumber) {
                        $controller = new \App\Http\Controllers\ClientPortal\BudgetAllocationController();
                        $reflection = new \ReflectionClass($controller);
                        $method = $reflection->getMethod('recalculateConsolidatedFunds');
                        $method->setAccessible(true);
                        $method->invoke($controller, $annualBudget->id, $weekNumber);
                    }
                    
                } catch (Exception $e) {
                    Log::warning('Failed to recalculate for annual budget', [
                        'annual_budget_id' => $annualBudget->id,
                        'error' => $e->getMessage()
                    ]);
                }
                
                $progressBar->advance();
            }
            
            $progressBar->finish();
            $this->newLine();
            $this->info("✅ Recalculation triggered for {$annualBudgets->count()} annual budgets");
            
        } catch (Exception $e) {
            Log::error('Failed to trigger recalculation for all budgets', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Step 1: Update bookings.initial_cost for all existing bookings
     */
    private function updateInitialCosts(int $batchSize, bool $dryRun)
    {
        // Only process bookings that haven't been processed yet
        $totalBookings = Booking::where('is_update_initial_cost_processed', false)->count();
        $this->info("Found {$totalBookings} unprocessed bookings to update initial_cost");
        
        if ($totalBookings === 0) {
            $this->info("All bookings have already been processed for initial_cost. Skipping Step 1.");
            return;
        }
        
        if ($dryRun) {
            // Fast dry run - just show what would be done without processing each booking
            $notProcessedCount = Booking::where('is_update_initial_cost_processed', false)->count();
            $nullInitialCostCount = Booking::whereNull('initial_cost')->count();
            $this->info("🔍 DRY RUN: Found {$notProcessedCount} bookings not yet processed for initial_cost");
            $this->info("🚫 DRY RUN: Found {$nullInitialCostCount} bookings without initial_cost");
            $this->info("🔄 DRY RUN: Would calculate initial_cost for unprocessed bookings");
            $this->info("🚩 DRY RUN: Would reset budget_utilisation_updated flag for processed bookings");
            $this->info("⚡ DRY RUN: Skipping per-booking calculations for speed");
            $this->info("📊 Step 1 Results (DRY RUN):");
            $this->info("   - Would process: {$notProcessedCount} unprocessed bookings");
            $this->info("   - Would update: ~{$notProcessedCount} bookings (estimated)");
            return;
        }
        
        $processed = 0;
        $updated = 0;
        $errors = 0;
        
        $progressBar = $this->output->createProgressBar($totalBookings);
        $progressBar->setFormat('Processing: %current%/%max% [%bar%] %percent%% - Updated: %message%');
        $progressBar->setMessage($updated);
        
        // Create a booking controller instance to access calculation methods
        $bookingController = new BookingController();
        
        Booking::where('is_update_initial_cost_processed', false)
            ->chunk($batchSize, function ($bookings) use (&$processed, &$updated, &$errors, $progressBar, $bookingController) {
                foreach ($bookings as $booking) {
                    try {
                        // Calculate the initial cost using the existing method
                        $initialCost = $this->calculateBookingCost($booking, $bookingController);
                        
                        // Update the booking with calculated cost and mark as processed
                        $booking->update([
                            'initial_cost' => $initialCost,
                            'is_update_initial_cost_processed' => true,
                            'budget_utilisation_updated' => false
                        ]);
                        
                        $updated++;
                        $progressBar->setMessage($updated);
                        
                    } catch (Exception $e) {
                        $errors++;
                        // Only log errors for live runs to improve performance
                        Log::error('Failed to update initial cost for booking', [
                            'booking_id' => $booking->bookingId,
                            'error' => $e->getMessage()
                        ]);
                    }
                    
                    $processed++;
                    $progressBar->advance();
                }
            });
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info("Step 1 Results:");
        $this->info("- Processed: {$processed} bookings");
        $this->info("- Updated: {$updated} bookings");
        
        if ($errors > 0) {
            $this->warn("- Errors: {$errors} bookings (check logs for details)");
        }
    }
    
    /**
     * Step 2: Update weekly budget utilisation for eligible bookings
     */
    private function updateWeeklyBudgetUtilisation(int $batchSize, bool $dryRun)
    {
        // Find bookings that need budget utilisation update - only unprocessed bookings
        $eligibleBookings = Booking::where('budget_utilisation_updated', false)
            ->whereIn('unitStatus', [1, 4]) // Only confirmed or new bookings
            ->count();
            
        $this->info("Found {$eligibleBookings} eligible unprocessed bookings for budget utilisation update");
        
        if ($eligibleBookings === 0) {
            $this->info("No eligible bookings found. Skipping Step 2.");
            return;
        }
        
        if ($dryRun) {
            // Fast dry run - just show summary without processing individual bookings
            $withUnitCost = Booking::where('budget_utilisation_updated', false)
                ->whereIn('unitStatus', [1, 4])
                ->whereNotNull('unit_cost')
                ->where('unit_cost', '>', 0)
                ->count();
            
            $withInitialCost = Booking::where('budget_utilisation_updated', false)
                ->whereIn('unitStatus', [1, 4])
                ->where(function($query) {
                    $query->whereNull('unit_cost')
                          ->orWhere('unit_cost', '<=', 0);
                })
                ->whereNotNull('initial_cost')
                ->where('initial_cost', '>', 0)
                ->count();

            $this->info("🔍 DRY RUN: Found {$eligibleBookings} eligible unprocessed bookings for budget utilisation update");
            $this->info("💰 DRY RUN: {$withUnitCost} bookings would use unit_cost");
            $this->info("💱 DRY RUN: {$withInitialCost} bookings would use initial_cost");
            $this->info("⚡ DRY RUN: Skipping per-booking processing for speed");
            $this->info("📊 Step 2 Results (DRY RUN):");
            $this->info("   - Would process: {$eligibleBookings} eligible bookings");
            $this->info("   - Would update: ~{$eligibleBookings} bookings (estimated)");
            return;
        }
        
        $processed = 0;
        $updated = 0;
        $errors = 0;
        
        $progressBar = $this->output->createProgressBar($eligibleBookings);
        $progressBar->setFormat('Processing: %current%/%max% [%bar%] %percent%% - Updated: %message%');
        $progressBar->setMessage($updated);
        
        Booking::where('budget_utilisation_updated', false)
            ->whereIn('unitStatus', [1, 4])
            ->chunk($batchSize, function ($bookings) use (&$processed, &$updated, &$errors, $progressBar) {
                
                foreach ($bookings as $booking) {
                    try {
                        // Determine the cost to use for budget utilisation
                        $costToUse = $this->determineCostForBudgetUtilisation($booking);
                        
                        if ($costToUse <= 0) {
                            // Only log warnings for significant issues in live mode, skip zero costs
                            if ($costToUse < 0) {
                                Log::warning('Booking has negative cost', [
                                    'booking_id' => $booking->bookingId,
                                    'calculated_cost' => $costToUse
                                ]);
                            }
                            $processed++;
                            $progressBar->advance();
                            continue;
                        }
                        
                        DB::beginTransaction();
                        
                        try {
                            // Call the helper function to update weekly utilisation
                            updateWeeklyUtilisedFund(
                                null, // weeklyBudgetId - let the function find it from booking
                                $costToUse,
                                'addition',
                                'App\Models\Booking',
                                $booking->bookingId,
                                null // Simplified description to reduce logging overhead
                            );
                            
                            // Mark the booking as processed
                            $booking->update(['budget_utilisation_updated' => true]);
                            
                            DB::commit();
                            $updated++;
                            
                        } catch (Exception $e) {
                            DB::rollback();
                            throw $e;
                        }
                        
                        $progressBar->setMessage($updated);
                        
                    } catch (Exception $e) {
                        $errors++;
                        Log::error('Failed to update weekly budget utilisation for booking', [
                            'booking_id' => $booking->bookingId,
                            'error' => $e->getMessage()
                        ]);
                    }
                    
                    $processed++;
                    $progressBar->advance();
                }
            });
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info("Step 2 Results:");
        $this->info("- Processed: {$processed} eligible bookings");
        $this->info("- Updated: {$updated} bookings");
        
        if ($errors > 0) {
            $this->warn("- Errors: {$errors} bookings (check logs for details)");
        }
    }
    
    /**
     * Calculate booking cost using the existing controller method
     */
    private function calculateBookingCost(Booking $booking, BookingController $controller)
    {
        try {
            // Use reflection to access the private method
            $reflection = new \ReflectionClass($controller);
            $method = $reflection->getMethod('calculateCostBookingNumberonly');
            $method->setAccessible(true);
            
            $costString = $method->invoke($controller, $booking);
            
            // The method returns a formatted string, extract the numeric value
            $cost = (float) str_replace([',', '£', ' '], '', $costString);
            
            return $cost;
            
        } catch (Exception $e) {
            // Only log critical calculation errors, not every failure to reduce log noise
            if (strpos($e->getMessage(), 'Critical') !== false || strpos($e->getMessage(), 'Fatal') !== false) {
                Log::error('Critical booking cost calculation failure', [
                    'booking_id' => $booking->bookingId,
                    'error' => $e->getMessage()
                ]);
            }
            
            // Return 0 as fallback
            return 0.00;
        }
    }
    
    /**
     * Determine which cost to use for budget utilisation update
     */
    private function determineCostForBudgetUtilisation(Booking $booking)
    {
        // Use unit_cost if it's non-null and greater than 0
        if (!is_null($booking->unit_cost) && $booking->unit_cost > 0) {
            return (float) $booking->unit_cost;
        }
        
        // Otherwise, use initial_cost
        return (float) ($booking->initial_cost ?? 0);
    }
    
    /**
     * Show a quick summary without detailed processing (very fast)
     */
    private function showQuickSummary()
    {
        $this->info("=== QUICK SUMMARY (Read-only Overview) ===\n");
        
        // Count total bookings
        $totalBookings = Booking::count();
        $this->info("📊 Total Bookings: {$totalBookings}");
        
        // Count bookings with null initial_cost
        $nullInitialCost = Booking::whereNull('initial_cost')->count();
        $nonNullInitialCost = $totalBookings - $nullInitialCost;
        
        $this->info("💰 Bookings with initial_cost: {$nonNullInitialCost}");
        $this->info("🚫 Bookings without initial_cost: {$nullInitialCost}");
        
        // Count bookings eligible for budget utilisation update (only unprocessed)
        $eligibleForBudgetUpdate = Booking::whereIn('unitStatus', [1, 4])
            ->where('budget_utilisation_updated', false)
            ->where('is_update_initial_cost_processed', false) // Only unprocessed bookings
            ->count();
        $this->info("🎯 Unprocessed bookings eligible for budget utilisation update: {$eligibleForBudgetUpdate}");
        
        // Count existing weekly fund logs
        $weeklyLogs = DB::table('weekly_utilised_fund_logs')->count();
        $this->info("📝 Existing weekly fund logs: {$weeklyLogs}");
        
        // Count weekly budgets
        $weeklyBudgets = DB::table('client_unit_weekly_budgets')->count();
        $this->info("📅 Total weekly budgets: {$weeklyBudgets}");
        
        // Show what would happen
        $this->warn("\n🔄 ACTIONS THAT WOULD BE PERFORMED:");
        $this->line("  • Reset {$weeklyLogs} weekly fund logs");
        $this->line("  • Reset {$weeklyBudgets} weekly budget utilizations");
        $this->line("  • Update budget utilisation for {$eligibleForBudgetUpdate} bookings");
        
        $this->info("\n⚡ For detailed processing, run without --summary-only");
        $this->info("🚀 For safe testing, add --dry-run");
    }
    
    /**
     * Calculate initial cost for a specific booking by ID
     * 
     * @param int $bookingId
     * @return float
     */
    public static function calculateInitialCostByBookingId(int $bookingId)
    {
        try {
            // Find the booking by ID
            $booking = Booking::find($bookingId);
            
            if (!$booking) {
                Log::warning("Booking not found for ID: {$bookingId}");
                return 0.00;
            }
            
            // Create a booking controller instance
            $bookingController = new BookingController();
            
            // Use reflection to access the private method
            $reflection = new \ReflectionClass($bookingController);
            $method = $reflection->getMethod('calculateCostBookingNumberonly');
            $method->setAccessible(true);
            
            $costString = $method->invoke($bookingController, $booking);
            
            // The method returns a formatted string, extract the numeric value
            $cost = (float) str_replace([',', '£', ' '], '', $costString);
            
            return $cost;
            
        } catch (Exception $e) {
            Log::error('Failed to calculate initial cost for booking ID', [
                'booking_id' => $bookingId,
                'error' => $e->getMessage()
            ]);
            
            // Return 0 as fallback
            return 0.00;
        }
    }

    /**
     * Calculate and update initial cost for a specific booking by ID
     * 
     * @param int $bookingId
     * @return array
     */
    public static function updateInitialCostByBookingId(int $bookingId)
    {
        try {
            // Find the booking by ID
            $booking = Booking::find($bookingId);
            
            if (!$booking) {
                return [
                    'success' => false,
                    'message' => "Booking not found for ID: {$bookingId}",
                    'cost' => 0.00
                ];
            }
            
            // Calculate the initial cost
            $initialCost = self::calculateInitialCostByBookingId($bookingId);
            
            // Update the booking
            $booking->update([
                'initial_cost' => $initialCost,
                'is_update_initial_cost_processed' => true,
                'budget_utilisation_updated' => false
            ]);
            
            return [
                'success' => true,
                'message' => "Successfully updated initial cost for booking ID: {$bookingId}",
                'cost' => $initialCost,
                'booking_id' => $bookingId
            ];
            
        } catch (Exception $e) {
            Log::error('Failed to update initial cost for booking ID', [
                'booking_id' => $bookingId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => "Error updating initial cost for booking ID {$bookingId}: " . $e->getMessage(),
                'cost' => 0.00
            ];
        }
    }
}
