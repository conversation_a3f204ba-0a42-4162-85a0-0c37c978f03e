<?php

if (!function_exists('s3_url')) {
    function s3_url($path)
    {
        // Assuming you are using <PERSON><PERSON>'s built-in Filesystem configuration
        return \Storage::disk('s3')->url($path);
    }
}
if (!function_exists('getFullNameAttribute')) {
    function getFullNameAttribute($forname, $surname)
    {
        return $forname . " " . $surname;
    }
}

if (!function_exists('updateWeeklyUtilisedFund')) {
    /**
     * Update the weekly utilised fund for a specific client unit weekly budget
     * 
     * @param int|null $weeklyBudgetId The ID of the client_unit_weekly_budget (can be null)
     * @param float $adjustmentAmount The amount to adjust (positive number)
     * @param string $adjustmentType Either 'addition' or 'deduction'
     * @param string|null $sourceType Optional source type (e.g., 'App\Models\Booking')
     * @param int|null $sourceId Optional source ID (e.g., booking ID)
     * @param string|null $description Optional description for the adjustment
     * @return bool True if successful, false otherwise
     * @throws Exception If validation fails or update fails
     */
    function updateWeeklyUtilisedFund(
        ?int $weeklyBudgetId,
        float $adjustmentAmount,
        string $adjustmentType,
        ?string $sourceType = null,
        ?int $sourceId = null,
        ?string $description = null
    ) {
        // Validate inputs
        if (!in_array($adjustmentType, ['addition', 'deduction', 'updation'])) {
            throw new \InvalidArgumentException('Adjustment type must be either "addition", "deduction", or "updation"');
        }
        
        // For updation type, validate required parameters
        if ($adjustmentType === 'updation') {
            if ($sourceType !== 'App\Models\Booking' || !$sourceId) {
                throw new \InvalidArgumentException('For updation type, source_type must be "App\Models\Booking" and source_id must be provided');
            }
        } else {
            // For addition and deduction, amount must be positive
            if ($adjustmentAmount < 0) {
                throw new \InvalidArgumentException('Adjustment amount must be a positive number for addition and deduction types');
            }
        }
        
        try {
            \Illuminate\Support\Facades\DB::beginTransaction();
            
            // If weeklyBudgetId is null or not found, try to find it from the booking
            $weeklyBudget = null;
            if ($weeklyBudgetId) {
                $weeklyBudget = \App\Models\ClientUnitWeeklyBudget::find($weeklyBudgetId);
            }
            
            // If weekly budget not found and we have booking source info, try to find it
            if (!$weeklyBudget && $sourceType === 'App\Models\Booking' && $sourceId) {
                \Illuminate\Support\Facades\Log::info('Weekly budget not found, attempting to find from booking', [
                    'weekly_budget_id' => $weeklyBudgetId,
                    'source_type' => $sourceType,
                    'source_id' => $sourceId
                ]);
                
                // Get the booking
                $booking = \App\Models\Booking::find($sourceId);
                if (!$booking) {
                    throw new \InvalidArgumentException("Booking with ID {$sourceId} not found");
                }
                
                // Get unit ID from booking
                $unitId = $booking->unitId;
                
                // Calculate financial year (April to March)
                $bookingDate = \Illuminate\Support\Carbon::parse($booking->date);
                $financialYear = $bookingDate->month >= 4 ? $bookingDate->year : $bookingDate->year - 1;
                
                \Illuminate\Support\Facades\Log::info('Looking for weekly budget from booking', [
                    'booking_id' => $sourceId,
                    'unit_id' => $unitId,
                    'booking_date' => $booking->date,
                    'financial_year' => $financialYear
                ]);
                
                // Get the annual budget for this unit and financial year
                $clientUnitAnnualBudget = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $unitId)
                    ->where('financial_year', $financialYear)
                    ->first();
                
                if (!$clientUnitAnnualBudget) {
                    throw new \InvalidArgumentException("No annual budget found for unit {$unitId} in financial year {$financialYear}");
                }
                
                // Find the weekly budget where the booking date falls between week_start_date and week_end_date
                $bookingDateString = $bookingDate->format('Y-m-d');
                
                $weeklyBudget = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
                    ->whereDate('week_start_date', '<=', $bookingDateString)
                    ->whereDate('week_end_date', '>=', $bookingDateString)
                    ->first();
                
                if (!$weeklyBudget) {
                    // Log available weekly budgets for debugging
                    $availableWeeks = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
                        ->orderBy('week_start_date')
                        ->get(['id', 'week_number', 'week_start_date', 'week_end_date'])
                        ->toArray();
                    
                    \Illuminate\Support\Facades\Log::warning('No weekly budget found for booking date', [
                        'unit_id' => $unitId,
                        'booking_id' => $sourceId,
                        'booking_date' => $bookingDateString,
                        'financial_year' => $financialYear,
                        'annual_budget_id' => $clientUnitAnnualBudget->id,
                        'available_weekly_budgets' => $availableWeeks
                    ]);
                    
                    throw new \InvalidArgumentException("No weekly budget found for booking date {$bookingDateString} in unit {$unitId}");
                }
                
                \Illuminate\Support\Facades\Log::info('Found weekly budget from booking lookup', [
                    'weekly_budget_id' => $weeklyBudget->id,
                    'week_start_date' => $weeklyBudget->week_start_date,
                    'week_end_date' => $weeklyBudget->week_end_date
                ]);
            }
            
            // If we still don't have a weekly budget, throw an error
            if (!$weeklyBudget) {
                throw new \InvalidArgumentException('Weekly budget not found and cannot be determined from source information');
            }
            
            // Handle updation type - calculate difference and update booking
            $actualAdjustmentAmount = $adjustmentAmount;
            $logDescription = $description;
            $booking = null;
            
            if ($adjustmentType === 'updation') {
                // Get the booking to retrieve previous cost
                $booking = \App\Models\Booking::find($sourceId);
                if (!$booking) {
                    throw new \InvalidArgumentException("Booking with ID {$sourceId} not found for updation");
                }
                
                // Get previous cost (treat null as 0)
                $previousCost = (float) ($booking->initial_cost ?? 0);
                $newCost = $adjustmentAmount; // For updation, adjustmentAmount is the new cost value
                
                // Calculate the actual difference to apply
                $actualAdjustmentAmount = $newCost - $previousCost;
                
                // Create enhanced description for updation
                $logDescription = sprintf(
                    "[BOOKING AMOUNT UPDATE] Booking cost updated from £%s to £%s for booking ID: %d (difference: £%s)",
                    number_format($previousCost, 2),
                    number_format($newCost, 2),
                    $sourceId,
                    $actualAdjustmentAmount >= 0 ? '+' . number_format($actualAdjustmentAmount, 2) : number_format($actualAdjustmentAmount, 2)
                );
                
                // Update the booking's initial_cost to the new value
                $booking->initial_cost = $newCost;
                $booking->save();
                
                \Illuminate\Support\Facades\Log::info('Booking initial_cost updated for updation', [
                    'booking_id' => $sourceId,
                    'previous_cost' => $previousCost,
                    'new_cost' => $newCost,
                    'difference' => $actualAdjustmentAmount
                ]);
            }
            
            // Calculate the new utilisation amount
            $currentUtilisation = $weeklyBudget->total_weekly_utilisation ?? 0;
            
            if ($adjustmentType === 'updation') {
                // For updation, add the calculated difference (can be positive or negative)
                $newUtilisation = $currentUtilisation + $actualAdjustmentAmount;
            } else {
                // For addition and deduction, use original logic
                $newUtilisation = $adjustmentType === 'addition' 
                    ? $currentUtilisation + $actualAdjustmentAmount
                    : $currentUtilisation - $actualAdjustmentAmount;
            }
            
            // Ensure utilisation doesn't go below 0
            $newUtilisation = max(0, $newUtilisation);
            
            // Update the weekly budget utilisation
            $weeklyBudget->total_weekly_utilisation = $newUtilisation;
            
            // Recalculate percent utilisation
            if ($weeklyBudget->total_weekly_allocation > 0) {
                $weeklyBudget->percent_utilisation = ($newUtilisation / $weeklyBudget->total_weekly_allocation) * 100;
            } else {
                $weeklyBudget->percent_utilisation = 0;
            }
            
            // Recalculate weekly unutilised fund
            $weeklyBudget->weekly_unutilised_fund = $weeklyBudget->total_weekly_allocation - $newUtilisation;
            
            $weeklyBudget->save();
            
            // Log the adjustment
            $logRecord = \App\Models\WeeklyUtilisedFundLog::create([
                'client_unit_weekly_budget_id' => $weeklyBudget->id,
                'adjustment_amount' => $actualAdjustmentAmount, // Use the actual amount applied
                'adjustment_type' => $adjustmentType,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'description' => $logDescription, // Use the enhanced description for updation
            ]);
            
            // Debug logging to track log creation
            \Illuminate\Support\Facades\Log::info('Weekly utilised fund log created', [
                'log_id' => $logRecord->id,
                'client_unit_weekly_budget_id' => $weeklyBudget->id,
                'adjustment_amount' => $actualAdjustmentAmount,
                'adjustment_type' => $adjustmentType,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'description' => $logDescription,
            ]);
            
            // Trigger recalculation for all dependent values
            // We need to call the recalculation method from BudgetAllocationController
            $controller = new \App\Http\Controllers\ClientPortal\BudgetAllocationController();
            $reflection = new \ReflectionClass($controller);
            $method = $reflection->getMethod('recalculateConsolidatedFunds');
            $method->setAccessible(true);
            $method->invoke($controller, $weeklyBudget->client_unit_annual_budget_id, $weeklyBudget->week_number);
            
            \Illuminate\Support\Facades\DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollback();
            \Illuminate\Support\Facades\Log::error('Error updating weekly utilised fund: ' . $e->getMessage(), [
                'weekly_budget_id' => $weeklyBudget->id ?? $weeklyBudgetId,
                'adjustment_amount' => $adjustmentAmount,
                'actual_adjustment_amount' => $actualAdjustmentAmount ?? $adjustmentAmount,
                'adjustment_type' => $adjustmentType,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'description' => $logDescription ?? $description,
            ]);
            
            throw $e;
        }
    }
}