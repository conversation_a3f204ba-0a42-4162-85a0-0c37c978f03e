<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;

/**
 * Dashboard Permissions Helper
 * Manages permissions for dashboard components
 */
class DashboardPermissions
{
    /**
     * Default permissions for all dashboard components
     * Currently set to true for all permissions as requested
     */
    private static $defaultPermissions = [
        // Main dashboard permissions
        'view_dashboard' => true,
        'view_budget_summary' => true,
        'view_budget_charts' => true,
        'view_notes_reminders' => true,
        'view_smart_view' => true,
        'view_booking_reasons' => true,
        'view_expense_overview' => true,

        // Budget summary cards permissions
        'view_budget_card' => true,
        'view_expenses_card' => true,
        'view_balance_card' => true,
        'view_expense_percentage_card' => true,
        'view_balance_percentage_card' => true,

        // Chart permissions
        'view_budget_chart' => true,
        'view_expenses_chart' => true,
        'view_chart_data_table' => true,

        // Notes and reminders permissions
        'view_notes' => true,
        'add_notes' => true,
        'edit_notes' => true,
        'delete_notes' => true,
        'view_reminders' => true,
        'add_reminders' => true,
        'edit_reminders' => true,
        'delete_reminders' => true,

        // Filter permissions
        'use_month_filter' => true,
        'use_unit_filter' => true,
        'use_financial_year_filter' => true,
        'use_date_range_filter' => true,

        // Smart view permissions
        'view_smart_view_chart' => true,
        'change_smart_view_filter' => true,

        // Booking reasons permissions
        'view_booking_reasons_summary' => true,

        // Unit expense overview permissions
        'view_unit_expense_chart' => true,
    ];

    /**
     * Get permissions for a specific user and component
     * 
     * @param string|null $component Component name (optional)
     * @param int|null $userId User ID (optional, defaults to current user)
     * @return array
     */
    public static function getPermissions($component = null, $userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        // For now, return default permissions (all true)
        // In the future, this can be extended to check user roles, database permissions, etc.
        $permissions = self::$defaultPermissions;

        // Filter permissions by component if specified
        if ($component) {
            return self::getComponentPermissions($component, $permissions);
        }

        return $permissions;
    }

    /**
     * Get permissions for a specific component
     * 
     * @param string $component
     * @param array $allPermissions
     * @return array
     */
    private static function getComponentPermissions($component, $allPermissions)
    {
        $componentPermissions = [];

        switch ($component) {
            case 'budget_summary_cards':
                $componentPermissions = [
                    'view_budget_card' => $allPermissions['view_budget_card'],
                    'view_expenses_card' => $allPermissions['view_expenses_card'],
                    'view_balance_card' => $allPermissions['view_balance_card'],
                    'view_expense_percentage_card' => $allPermissions['view_expense_percentage_card'],
                    'view_balance_percentage_card' => $allPermissions['view_balance_percentage_card'],
                ];
                break;

            case 'budget_donut_chart':
                $componentPermissions = [
                    'view_chart' => $allPermissions['view_budget_chart'],
                    'view_data_table' => $allPermissions['view_chart_data_table'],
                ];
                break;

            case 'expenses_donut_chart':
                $componentPermissions = [
                    'view_chart' => $allPermissions['view_expenses_chart'],
                    'view_data_table' => $allPermissions['view_chart_data_table'],
                ];
                break;

            case 'notes_reminders':
                $componentPermissions = [
                    'view_notes' => $allPermissions['view_notes'],
                    'add_notes' => $allPermissions['add_notes'],
                    'edit_notes' => $allPermissions['edit_notes'],
                    'delete_notes' => $allPermissions['delete_notes'],
                    'view_reminders' => $allPermissions['view_reminders'],
                    'add_reminders' => $allPermissions['add_reminders'],
                    'edit_reminders' => $allPermissions['edit_reminders'],
                    'delete_reminders' => $allPermissions['delete_reminders'],
                ];
                break;

            case 'filter':
                $componentPermissions = [
                    'use_month_filter' => $allPermissions['use_month_filter'],
                    'use_unit_filter' => $allPermissions['use_unit_filter'],
                    'use_financial_year_filter' => $allPermissions['use_financial_year_filter'],
                    'use_date_range_filter' => $allPermissions['use_date_range_filter'],
                ];
                break;

            case 'smart_view_chart':
                $componentPermissions = [
                    'view_chart' => $allPermissions['view_smart_view_chart'],
                    'change_filter' => $allPermissions['change_smart_view_filter'],
                ];
                break;

            case 'booking_reasons':
                $componentPermissions = [
                    'view_booking_reasons' => $allPermissions['view_booking_reasons_summary'],
                ];
                break;

            case 'unit_expense_overview':
                $componentPermissions = [
                    'view_expense_overview' => $allPermissions['view_unit_expense_chart'],
                ];
                break;

            default:
                $componentPermissions = $allPermissions;
                break;
        }

        return $componentPermissions;
    }

    /**
     * Check if user has a specific permission
     * 
     * @param string $permission
     * @param int|null $userId
     * @return bool
     */
    public static function hasPermission($permission, $userId = null)
    {
        $permissions = self::getPermissions(null, $userId);
        return $permissions[$permission] ?? false;
    }

    /**
     * Get main dashboard permissions
     * 
     * @param int|null $userId
     * @return array
     */
    public static function getDashboardPermissions($userId = null)
    {
        $allPermissions = self::getPermissions(null, $userId);
        
        return [
            'view_budget_summary' => $allPermissions['view_budget_summary'],
            'view_budget_charts' => $allPermissions['view_budget_charts'],
            'view_notes_reminders' => $allPermissions['view_notes_reminders'],
            'view_smart_view' => $allPermissions['view_smart_view'],
            'view_booking_reasons' => $allPermissions['view_booking_reasons'],
            'view_expense_overview' => $allPermissions['view_expense_overview'],
        ];
    }

    /**
     * Future method for setting up role-based permissions
     * This can be extended when implementing actual permission management
     * 
     * @param int $userId
     * @param string $role
     * @return array
     */
    public static function getPermissionsByRole($userId, $role)
    {
        // This method can be implemented in the future to handle role-based permissions
        // For now, return default permissions
        return self::$defaultPermissions;
    }

    /**
     * Future method for checking database-stored permissions
     * This can be extended when implementing database-driven permissions
     * 
     * @param int $userId
     * @param string $component
     * @return array
     */
    public static function getPermissionsFromDatabase($userId, $component = null)
    {
        // This method can be implemented in the future to check permissions from database
        // For now, return default permissions
        return self::getPermissions($component, $userId);
    }
}
