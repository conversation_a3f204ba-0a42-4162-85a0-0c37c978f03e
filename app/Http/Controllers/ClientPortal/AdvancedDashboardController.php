<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Client;
use App\Models\ClientReminder;
use App\Models\ClientTodos;
use App\Models\ClientUnit;
use App\Models\ClientUnitBudget;
use App\Models\ClientUnitPayment;
use App\Models\ClientUnitSchedule;
use App\Models\Holiday;
use App\Models\StaffUnitPayment;
use App\Models\ClientUnitAnnualBudget;
use App\Models\ClientUnitWeeklyBudget;
use App\Helpers\DashboardPermissions;
use Carbon\Carbon;
use App\Models\TaxYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AdvancedDashboardController extends Controller
{
    /**
     * Display the advanced dashboard
     */
    public function index(Request $request)
    {
        // Get authenticated client ID
        $clientId = Auth::user()->portal_id;
        
        // Get dashboard permissions for the current user
        $permissions = DashboardPermissions::getDashboardPermissions();
        
        // Get client units for filter dropdown
        $units = ClientUnit::where('clientId', $clientId)
            ->where('status', 1)
            ->get();

        // Get current financial year
        $currentYear = date('Y');
        $currentMonth = date('n');
        $financialYear = $currentMonth >= 4 ? $currentYear : $currentYear - 1;

        return view('clientPortal.advanced-dashboard', compact(
            'clientId',
            'permissions',
            'units',
            'financialYear'
        ));
    }

    /**
     * Get budget summary data for the advanced dashboard
     */
    public function getBudgetSummary(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        $unitId = $request->input('unit_id');
        $financialYear = $request->input('financial_year');
        
        // Get month and year based on filter
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        // Get client units
        $unitsQuery = ClientUnit::where('clientId', $clientId)->where('status', 1);
        if ($unitId) {
            $unitsQuery->where('clientUnitId', $unitId);
        }
        $units = $unitsQuery->get();

        // Calculate budget and expenses
        $totalBudget = 0;
        $totalExpenses = 0;
        $unitData = [];

        foreach ($units as $unit) {
            // Get weekly budgets for the unit
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year, $financialYear);
            
            $unitBudget = $weeklyBudgets->sum('total_weekly_allocation');
            $unitExpenses = $weeklyBudgets->sum('total_weekly_utilisation');
            
            $totalBudget += $unitBudget;
            $totalExpenses += $unitExpenses;
            
            $unitData[] = [
                'name' => $unit->name,
                'budget' => $unitBudget,
                'expenses' => $unitExpenses,
                'balance' => $unitBudget - $unitExpenses
            ];
        }

        $balance = $totalBudget - $totalExpenses;
        $client = Client::find($clientId);

        return response()->json([
            'status' => 200,
            'budget' => $totalBudget,
            'expense' => $totalExpenses,
            'balance' => $balance,
            'units' => $unitData,
            'client' => $client
        ]);
    }

    /**
     * Get donut chart data for budget breakdown
     */
    public function getBudgetDonutGraph(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        $unitId = $request->input('unit_id');
        
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $unitsQuery = ClientUnit::where('clientId', $clientId)->where('status', 1);
        if ($unitId) {
            $unitsQuery->where('clientUnitId', $unitId);
        }
        $units = $unitsQuery->get();

        $unitBudgetData = [];
        $tableBudgetData = [];
        $totalBudget = 0;

        foreach ($units as $unit) {
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year);
            $unitBudget = $weeklyBudgets->sum('total_weekly_allocation');
            
            if ($unitBudget > 0) {
                $unitBudgetData[] = [
                    'name' => $unit->name,
                    'y' => $unitBudget
                ];
                
                $totalBudget += $unitBudget;
            }
        }

        // Calculate percentages for table
        foreach ($unitBudgetData as $data) {
            $percentage = $totalBudget > 0 ? round(($data['y'] / $totalBudget) * 100, 2) : 0;
            $tableBudgetData[] = [
                'name' => $data['name'],
                'amount' => $data['y'],
                'percentage' => $percentage
            ];
        }

        return response()->json([
            'status' => 200,
            'graph' => [
                'budgetGraph' => [
                    'data' => $unitBudgetData,
                    'title' => date("F", mktime(0, 0, 0, $month, 10)),
                    'table' => [
                        'rows' => $tableBudgetData,
                        'tfoot' => [
                            'amount' => $totalBudget,
                            'percentage' => 100
                        ]
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get expense donut chart data
     */
    public function getExpenseDonutGraph(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        $unitId = $request->input('unit_id');
        
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $unitsQuery = ClientUnit::where('clientId', $clientId)->where('status', 1);
        if ($unitId) {
            $unitsQuery->where('clientUnitId', $unitId);
        }
        $units = $unitsQuery->get();

        $unitExpenseData = [];
        $tableExpenseData = [];
        $totalExpenses = 0;

        foreach ($units as $unit) {
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year);
            $unitExpenses = $weeklyBudgets->sum('total_weekly_utilisation');
            
            if ($unitExpenses > 0) {
                $unitExpenseData[] = [
                    'name' => $unit->name,
                    'y' => $unitExpenses
                ];
                
                $totalExpenses += $unitExpenses;
            }
        }

        // Calculate percentages for table
        foreach ($unitExpenseData as $data) {
            $percentage = $totalExpenses > 0 ? round(($data['y'] / $totalExpenses) * 100, 2) : 0;
            $tableExpenseData[] = [
                'name' => $data['name'],
                'amount' => $data['y'],
                'percentage' => $percentage
            ];
        }

        return response()->json([
            'status' => 200,
            'graph' => [
                'expenseGraph' => [
                    'data' => $unitExpenseData,
                    'title' => date("F", mktime(0, 0, 0, $month, 10)),
                    'table' => [
                        'rows' => $tableExpenseData,
                        'tfoot' => [
                            'amount' => $totalExpenses,
                            'percentage' => 100
                        ]
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get bar chart data for budget vs expenses
     */
    public function getBarChartData(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $units = ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        
        $categories = [];
        $budgetData = [];
        $expenseData = [];

        foreach ($units as $unit) {
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year);
            
            $categories[] = $unit->name;
            $budgetData[] = $weeklyBudgets->sum('total_weekly_allocation');
            $expenseData[] = $weeklyBudgets->sum('total_weekly_utilisation');
        }

        return response()->json([
            'status' => 200,
            'categories' => $categories,
            'series' => [
                [
                    'name' => 'Budget',
                    'data' => $budgetData,
                    'color' => '#28a745'
                ],
                [
                    'name' => 'Expenses',
                    'data' => $expenseData,
                    'color' => '#dc3545'
                ]
            ]
        ]);
    }

    /**
     * Get booking reasons data
     */
    public function getBookingReasons(Request $request)
    {
        $clientId = $request->input('client_id', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $units = ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        $unitIds = $units->pluck('clientUnitId');

        // Get bookings for the period
        $bookings = Booking::whereIn('unitId', $unitIds)
            ->whereIn('unitStatus', [1, 4])
            ->whereMonth('date', $month)
            ->whereYear('date', $year)
            ->get();

        // Define reason mapping
        $reasonMap = [
            '1' => 'sickness',
            '2' => 'holiday',
            '3' => 'vacant',
            '4' => 'resident_admission',
            '5' => 'one_to_one',
            '6' => 'extra_staff',
            '7' => 'management'
        ];

        $reasons = [];
        foreach ($reasonMap as $key => $name) {
            $reasonBookings = $bookings->where('reason', $key);
            $reasons[$name] = [
                'count' => $reasonBookings->count(),
                'cost' => $reasonBookings->sum('initial_cost') ?: 0
            ];
        }

        // Others (bookings with no reason or different reason)
        $otherBookings = $bookings->whereNotIn('reason', array_keys($reasonMap));
        $reasons['others'] = [
            'count' => $otherBookings->count(),
            'cost' => $otherBookings->sum('initial_cost') ?: 0
        ];

        return response()->json([
            'status' => 200,
            'reasons' => $reasons
        ]);
    }

    /**
     * Get line chart data for expense overview
     */
    public function getLineChartData(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        
        $units = ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        
        // Get last 6 months data
        $months = [];
        $series = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');
        }

        foreach ($units as $unit) {
            $data = [];
            for ($i = 5; $i >= 0; $i--) {
                $date = Carbon::now()->subMonths($i);
                $weeklyBudgets = $this->getWeeklyBudgetsForUnit(
                    $unit->clientUnitId, 
                    $date->month, 
                    $date->year
                );
                $data[] = $weeklyBudgets->sum('total_weekly_utilisation');
            }
            
            $series[] = [
                'name' => $unit->name,
                'data' => $data
            ];
        }

        return response()->json([
            'status' => 200,
            'categories' => $months,
            'series' => $series
        ]);
    }

    /**
     * Helper method to get weekly budgets for a unit
     */
    private function getWeeklyBudgetsForUnit($unitId, $month, $year, $financialYear = null)
    {
        $query = ClientUnitWeeklyBudget::whereHas('annualBudget', function($q) use ($unitId, $financialYear) {
            $q->where('client_unit_id', $unitId);
            if ($financialYear) {
                $q->where('financial_year', $financialYear . '-' . ($financialYear + 1));
            }
        });

        // Filter by month if needed
        if ($month && $year) {
            $startDate = Carbon::create($year, $month, 1)->startOfMonth();
            $endDate = Carbon::create($year, $month, 1)->endOfMonth();
            
            $query->where(function($q) use ($startDate, $endDate) {
                $q->whereBetween('week_start_date', [$startDate, $endDate])
                  ->orWhereBetween('week_end_date', [$startDate, $endDate])
                  ->orWhere(function($q2) use ($startDate, $endDate) {
                      $q2->where('week_start_date', '<=', $startDate)
                         ->where('week_end_date', '>=', $endDate);
                  });
            });
        }

        return $query->get();
    }

    /**
     * Helper method to get month and year from filter
     */
    private function getMonthYearFromFilter($filter)
    {
        $now = Carbon::now();
        
        switch ($filter) {
            case 2: // Last month
                $date = $now->subMonth();
                break;
            case 4: // Next month
                $date = $now->addMonth();
                break;
            default: // This month
                $date = $now;
                break;
        }

        return [
            'month' => $date->month,
            'year' => $date->year
        ];
    }

    /**
     * Get client units for filter dropdown
     */
    public function getClientUnits(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        
        $units = ClientUnit::where('clientId', $clientId)
            ->where('status', 1)
            ->select('clientUnitId', 'name')
            ->get();

        return response()->json([
            'success' => true,
            'units' => $units
        ]);
    }
}
