<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Client;
use App\Models\ClientReminder;
use App\Models\ClientTodos;
use App\Models\ClientUnit;
use App\Models\ClientUnitBudget;
use App\Models\ClientUnitPayment;
use App\Models\ClientUnitSchedule;
use App\Models\Holiday;
use App\Models\StaffUnitPayment;
use App\Models\ClientUnitAnnualBudget;
use App\Models\ClientUnitWeeklyBudget;
use App\Helpers\DashboardPermissions;
use Carbon\Carbon;
use App\Models\TaxYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AdvancedDashboardController extends Controller
{
    /**
     * Display the advanced dashboard
     */
    public function index(Request $request)
    {
        // Get authenticated client ID
        $clientId = Auth::user()->portal_id;
        
        // Get dashboard permissions for the current user
        $permissions = DashboardPermissions::getDashboardPermissions();
        
        // Get client units for filter dropdown
        $units = ClientUnit::where('clientId', $clientId)
            ->where('status', 1)
            ->get();

        // Get current financial year
        $currentYear = date('Y');
        $currentMonth = date('n');
        $financialYear = $currentMonth >= 4 ? $currentYear : $currentYear - 1;

        return view('clientPortal.advanced-dashboard', compact(
            'clientId',
            'permissions',
            'units',
            'financialYear'
        ));
    }

    /**
     * Get budget summary data for the advanced dashboard
     */
    public function getBudgetSummary(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        $unitId = $request->input('unit_id');
        $financialYear = $request->input('financial_year', $this->getCurrentFinancialYear());

        // Get month and year based on filter
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        // Get client units
        $unitsQuery = ClientUnit::where('clientId', $clientId)->where('status', 1);
        if ($unitId) {
            $unitsQuery->where('clientUnitId', $unitId);
        }
        $units = $unitsQuery->get();

        // Calculate budget and expenses
        $totalBudget = 0;
        $totalExpenses = 0;
        $totalBalance = 0;
        $unitData = [];

        foreach ($units as $unit) {
            // Get weekly budgets for the unit
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year, $financialYear);

            $unitBudget = $weeklyBudgets->sum('total_weekly_allocation') ?: 0;
            $unitExpenses = $weeklyBudgets->sum('total_weekly_utilisation') ?: 0;
            $unitBalance = $weeklyBudgets->sum('balance_fund') ?: ($unitBudget - $unitExpenses);

            $totalBudget += $unitBudget;
            $totalExpenses += $unitExpenses;
            $totalBalance += $unitBalance;

            $unitData[] = [
                'name' => $unit->name,
                'budget' => round($unitBudget, 2),
                'expenses' => round($unitExpenses, 2),
                'balance' => round($unitBalance, 2),
                'utilisation_percent' => $unitBudget > 0 ? round(($unitExpenses / $unitBudget) * 100, 2) : 0
            ];
        }

        $client = Client::find($clientId);

        return response()->json([
            'status' => 200,
            'budget' => round($totalBudget, 2),
            'expense' => round($totalExpenses, 2),
            'balance' => round($totalBalance, 2),
            'utilisation_percent' => $totalBudget > 0 ? round(($totalExpenses / $totalBudget) * 100, 2) : 0,
            'units' => $unitData,
            'client' => $client,
            'filter_info' => [
                'month' => $month,
                'year' => $year,
                'financial_year' => $financialYear,
                'filter_name' => $this->getFilterName($filter)
            ]
        ]);
    }

    /**
     * Get donut chart data for budget breakdown
     */
    public function getBudgetDonutGraph(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        $unitId = $request->input('unit_id');
        $financialYear = $request->input('financial_year', $this->getCurrentFinancialYear());

        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $unitsQuery = ClientUnit::where('clientId', $clientId)->where('status', 1);
        if ($unitId) {
            $unitsQuery->where('clientUnitId', $unitId);
        }
        $units = $unitsQuery->get();

        $unitBudgetData = [];
        $tableBudgetData = [];
        $totalBudget = 0;

        foreach ($units as $unit) {
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year, $financialYear);
            $unitBudget = $weeklyBudgets->sum('total_weekly_allocation') ?: 0;

            if ($unitBudget > 0) {
                $unitBudgetData[] = [
                    'name' => $unit->name,
                    'y' => round($unitBudget, 2),
                    'color' => $this->getUnitColor($unit->clientUnitId)
                ];

                $totalBudget += $unitBudget;
            }
        }

        // Calculate percentages for table
        foreach ($unitBudgetData as $data) {
            $percentage = $totalBudget > 0 ? round(($data['y'] / $totalBudget) * 100, 2) : 0;
            $tableBudgetData[] = [
                'name' => $data['name'],
                'amount' => number_format($data['y'], 2),
                'percentage' => $percentage
            ];
        }

        return response()->json([
            'status' => 200,
            'graph' => [
                'budgetGraph' => [
                    'data' => $unitBudgetData,
                    'title' => date("F Y", mktime(0, 0, 0, $month, 10, $year)),
                    'table' => [
                        'rows' => $tableBudgetData,
                        'tfoot' => [
                            'amount' => number_format($totalBudget, 2),
                            'percentage' => 100
                        ]
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get expense donut chart data
     */
    public function getExpenseDonutGraph(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        $unitId = $request->input('unit_id');
        
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $unitsQuery = ClientUnit::where('clientId', $clientId)->where('status', 1);
        if ($unitId) {
            $unitsQuery->where('clientUnitId', $unitId);
        }
        $units = $unitsQuery->get();

        $unitExpenseData = [];
        $tableExpenseData = [];
        $totalExpenses = 0;

        foreach ($units as $unit) {
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year);
            $unitExpenses = $weeklyBudgets->sum('total_weekly_utilisation');
            
            if ($unitExpenses > 0) {
                $unitExpenseData[] = [
                    'name' => $unit->name,
                    'y' => $unitExpenses
                ];
                
                $totalExpenses += $unitExpenses;
            }
        }

        // Calculate percentages for table
        foreach ($unitExpenseData as $data) {
            $percentage = $totalExpenses > 0 ? round(($data['y'] / $totalExpenses) * 100, 2) : 0;
            $tableExpenseData[] = [
                'name' => $data['name'],
                'amount' => $data['y'],
                'percentage' => $percentage
            ];
        }

        return response()->json([
            'status' => 200,
            'graph' => [
                'expenseGraph' => [
                    'data' => $unitExpenseData,
                    'title' => date("F", mktime(0, 0, 0, $month, 10)),
                    'table' => [
                        'rows' => $tableExpenseData,
                        'tfoot' => [
                            'amount' => $totalExpenses,
                            'percentage' => 100
                        ]
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get bar chart data for budget vs expenses
     */
    public function getBarChartData(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $units = ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        
        $categories = [];
        $budgetData = [];
        $expenseData = [];

        foreach ($units as $unit) {
            $weeklyBudgets = $this->getWeeklyBudgetsForUnit($unit->clientUnitId, $month, $year);
            
            $categories[] = $unit->name;
            $budgetData[] = $weeklyBudgets->sum('total_weekly_allocation');
            $expenseData[] = $weeklyBudgets->sum('total_weekly_utilisation');
        }

        return response()->json([
            'status' => 200,
            'categories' => $categories,
            'series' => [
                [
                    'name' => 'Budget',
                    'data' => $budgetData,
                    'color' => '#28a745'
                ],
                [
                    'name' => 'Expenses',
                    'data' => $expenseData,
                    'color' => '#dc3545'
                ]
            ]
        ]);
    }

    /**
     * Get booking reasons data
     */
    public function getBookingReasons(Request $request)
    {
        $clientId = $request->input('client_id', Auth::user()->portal_id);
        $filter = $request->input('filter', 1);
        
        $monthYear = $this->getMonthYearFromFilter($filter);
        $month = $monthYear['month'];
        $year = $monthYear['year'];

        $units = ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        $unitIds = $units->pluck('clientUnitId');

        // Get bookings for the period
        $bookings = Booking::whereIn('unitId', $unitIds)
            ->whereIn('unitStatus', [1, 4])
            ->whereMonth('date', $month)
            ->whereYear('date', $year)
            ->get();

        // Define reason mapping
        $reasonMap = [
            '1' => 'sickness',
            '2' => 'holiday',
            '3' => 'vacant',
            '4' => 'resident_admission',
            '5' => 'one_to_one',
            '6' => 'extra_staff',
            '7' => 'management'
        ];

        $reasons = [];
        foreach ($reasonMap as $key => $name) {
            $reasonBookings = $bookings->where('reason', $key);
            $reasons[$name] = [
                'count' => $reasonBookings->count(),
                'cost' => $reasonBookings->sum('initial_cost') ?: 0
            ];
        }

        // Others (bookings with no reason or different reason)
        $otherBookings = $bookings->whereNotIn('reason', array_keys($reasonMap));
        $reasons['others'] = [
            'count' => $otherBookings->count(),
            'cost' => $otherBookings->sum('initial_cost') ?: 0
        ];

        return response()->json([
            'status' => 200,
            'reasons' => $reasons
        ]);
    }

    /**
     * Get line chart data for expense overview
     */
    public function getLineChartData(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        
        $units = ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        
        // Get last 6 months data
        $months = [];
        $series = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');
        }

        foreach ($units as $unit) {
            $data = [];
            for ($i = 5; $i >= 0; $i--) {
                $date = Carbon::now()->subMonths($i);
                $weeklyBudgets = $this->getWeeklyBudgetsForUnit(
                    $unit->clientUnitId, 
                    $date->month, 
                    $date->year
                );
                $data[] = $weeklyBudgets->sum('total_weekly_utilisation');
            }
            
            $series[] = [
                'name' => $unit->name,
                'data' => $data
            ];
        }

        return response()->json([
            'status' => 200,
            'categories' => $months,
            'series' => $series
        ]);
    }

    /**
     * Helper method to get weekly budgets for a unit
     */
    private function getWeeklyBudgetsForUnit($unitId, $month = null, $year = null, $financialYear = null)
    {
        $query = ClientUnitWeeklyBudget::whereHas('annualBudget', function($q) use ($unitId, $financialYear) {
            $q->where('client_unit_id', $unitId);
            if ($financialYear) {
                $q->where('financial_year', $financialYear);
            }
        })->with('annualBudget');

        // Filter by month if needed
        if ($month && $year) {
            $startDate = Carbon::create($year, $month, 1)->startOfMonth();
            $endDate = Carbon::create($year, $month, 1)->endOfMonth();

            $query->where(function($q) use ($startDate, $endDate) {
                $q->whereBetween('week_start_date', [$startDate, $endDate])
                  ->orWhereBetween('week_end_date', [$startDate, $endDate])
                  ->orWhere(function($q2) use ($startDate, $endDate) {
                      $q2->where('week_start_date', '<=', $startDate)
                         ->where('week_end_date', '>=', $endDate);
                  });
            });
        }

        return $query->get();
    }

    /**
     * Get current financial year based on date
     */
    private function getCurrentFinancialYear()
    {
        $currentDate = Carbon::now();
        $currentYear = $currentDate->year;

        // Financial year starts in April
        if ($currentDate->month >= 4) {
            return $currentYear . '-' . ($currentYear + 1);
        } else {
            return ($currentYear - 1) . '-' . $currentYear;
        }
    }

    /**
     * Get annual budget for a unit and financial year
     */
    private function getAnnualBudgetForUnit($unitId, $financialYear = null)
    {
        if (!$financialYear) {
            $financialYear = $this->getCurrentFinancialYear();
        }

        return ClientUnitAnnualBudget::where('client_unit_id', $unitId)
            ->where('financial_year', $financialYear)
            ->first();
    }

    /**
     * Helper method to get month and year from filter
     */
    private function getMonthYearFromFilter($filter)
    {
        $now = Carbon::now();

        switch ($filter) {
            case 2: // Last month
                $date = $now->subMonth();
                break;
            case 4: // Next month
                $date = $now->addMonth();
                break;
            default: // This month
                $date = $now;
                break;
        }

        return [
            'month' => $date->month,
            'year' => $date->year
        ];
    }

    /**
     * Get filter name for display
     */
    private function getFilterName($filter)
    {
        switch ($filter) {
            case 2:
                return 'Last Month';
            case 4:
                return 'Next Month';
            default:
                return 'This Month';
        }
    }

    /**
     * Get color for unit charts
     */
    private function getUnitColor($unitId)
    {
        $colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ];

        return $colors[$unitId % count($colors)];
    }

    /**
     * Get client units for filter dropdown
     */
    public function getClientUnits(Request $request)
    {
        $clientId = $request->input('client', Auth::user()->portal_id);
        
        $units = ClientUnit::where('clientId', $clientId)
            ->where('status', 1)
            ->select('clientUnitId', 'name')
            ->get();

        return response()->json([
            'success' => true,
            'units' => $units
        ]);
    }
}
