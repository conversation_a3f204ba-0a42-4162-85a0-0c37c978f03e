<?php

namespace App\Http\Controllers\ClientPortal;
use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\ClientUnit;
use App\Models\Incident;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IncidentLogController extends Controller
{
    public function incidentLog(Request $request)
    {
        $query = Incident::with(['booking.category']);
        $loggedInClientId = Auth::user()->portal_id;
        $unitsdropdown = ClientUnit::where('status', 1)
        ->where('clientId', $loggedInClientId)
        ->get();
        // Apply filters
        if ($request->filled('booking_id')) {
            $query->where('booking_id', $request->input('booking_id'));
        }

        if ($request->filled('unit')) {
            $query->where('unit', 'like', '%' . $request->input('unit') . '%');
        }

        if ($request->filled('date_range')) {
            $dates = explode(' to ', $request->input('date_range'));
            $query->whereBetween('date', [$dates[0], $dates[1]]);
        }
        if ($request->filled('priority')) {
            $query->where('priority', $request->input('priority'));
        }
    
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }
        $query->orderBy('id', 'desc');

        $incidents = $query->paginate(15);

        return view('clientPortal.incidentLog', compact('incidents', 'unitsdropdown'));
    }
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'bookingId' => 'required|exists:bookings,bookingId',
                'incidentDetails' => 'required|string',
                'status' => 'required|in:New,In Progress,Informed Manager,Informed Unit,Closed',
                'priority' => 'required|in:Low,Medium,High',
                'shiftDate' => 'required|date',
            ]);
    
            $incident = Incident::create([
                'booking_id' => $validatedData['bookingId'],
                'unit' => $request->input('unit'),
                'date' => $validatedData['shiftDate'],
                'shift' => $request->input('shift'),
                'staff' => $request->input('staffName'),
                'incident' => $validatedData['incidentDetails'],
                'status' => $validatedData['status'],
                'priority' => $validatedData['priority'],
            ]);
    
            return response()->json([
                'success' => true,
                'data' => $incident
            ]);
    
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors(),
            ], 422);
        }
    }
    public function addLog(Request $request, $incidentId)
    {
        $request->validate([
            'log' => 'required|string|max:1000',
        ]);

        $incident = Incident::find($incidentId);

        if (!$incident) {
            return response()->json(['success' => false, 'message' => 'Incident not found.']);
        }

        // Append the new log entry to the existing log
        $timestamp = now()->format('Y-m-d H:i:s');
        $userName = auth()->user()->name; 
        // $newLogEntry = "{$request->log}-({$timestamp})";
        $newLogEntry = "New log - {$request->log} - by {$userName} on {$timestamp}";
        $incident->log = $incident->log ? $incident->log . "\n" . $newLogEntry : $newLogEntry;
        $incident->save();

        return response()->json(['success' => true, 'message' => 'Log entry added successfully.']);
    }
    public function deleteIncident(Request $request, $id)
    {
        $incident = Incident::find($id);

        if (!$incident) {
            return response()->json(['success' => false, 'message' => 'Incident not found.']);
        }

        $incident->delete();

        return response()->json(['success' => true, 'message' => 'Incident deleted successfully.']);
    }
    public function updateIncident(Request $request, $id)
    {
        $incident = Incident::find($id);
    
        if (!$incident) {
            return response()->json(['success' => false, 'message' => 'Incident not found.']);
        }
    
        // Validate the request data
        $request->validate([
            'status' => 'required|in:New,In Progress,Informed Manager,Informed Unit,Closed',
        ]);
    
        // Log the changes
        $userName = auth()->user()->name; // Assuming the user has a `name` attribute
        // $logEntry = "Status changed from '{$incident->status}' to '{$request->input('status')}' on " . now()->format('Y-m-d H:i:s');
        $logEntry = "Status changed from '{$incident->status}' to '{$request->input('status')}' by {$userName} on " . now()->format('Y-m-d H:i:s');
    
        // Update the incident status
        $incident->status = $request->input('status');
        $incident->log = $incident->log ? $incident->log . "\n" . $logEntry : $logEntry; // Append log entry
        $incident->save();
    
        return response()->json(['success' => true, 'message' => 'Incident updated successfully.', 'data' => $incident]);
    }
    public function getIncident($id)
    {
        $incident = Incident::find($id);

        if (!$incident) {
            return response()->json(['success' => false, 'message' => 'Incident not found.']);
        }

        return response()->json(['success' => true, 'data' => $incident]);
    }
}
