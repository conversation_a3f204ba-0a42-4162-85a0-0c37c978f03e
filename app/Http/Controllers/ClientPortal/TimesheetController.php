<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Controllers\UnitPortal\UnitHelper;
use App\Models\ClientUnit;
use App\Models\Client;
use App\Models\Staff;
use App\Models\Timesheet;
use App\Models\TimesheetLog;
use App\Models\Shift;
use App\Models\Admin;
use App\Exports\TimesheetExport;
use Yajra\Datatables\Facades\Datatables;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Rap2hpoutre\FastExcel\FastExcel;
use App\Mail\SendPlainSMS as SendPlainSMSEvent;
use App\Models\StaffCategory;
use App\Models\StaffUnitPayment;
use App\Models\ClientUnitSchedule;
use App\Models\Payment;
use App\Models\Invoice;
use App\Models\BookingLog;
use App\Models\Driver;
use App\Http\Controllers\Payments\PaymentHelper;
use App\Models\Quotation;
use App\Models\TaxWeek;
use App\Models\TaxYear;
use App\Models\Booking;
use App\Models\ClientUnitPayment;
use Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use \Swift_Mailer;
use \Swift_SmtpTransport as SmtpTransport;
use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
class TimesheetController extends Controller
{
   public function ts_approvals()
{
    $taxyearId = TaxYear::where('default', 1)->first();
    $TaxYear = TaxYear::get();
    $TaxWeek = TaxWeek::selectRaw('MIN(taxWeekId) as taxWeekId, weekNumber')
        ->where('taxYearId', $taxyearId->taxYearId)
        ->groupBy('weekNumber')
        ->orderBy('weekNumber')
        ->get();

    $loggedId = auth()->user()->portal_id;
    $actionWeek = request('action_week');
    $filterWeek = request('filter_tax_week');
    $filterYear = request('filter_tax_year') ?: $taxyearId->taxYearFrom;
   
    $oneMonthAgo = Carbon::now()->subMonth()->startOfDay();
    $today = Carbon::now()->endOfDay();
    $startPrevWeek = Carbon::now()->subWeeks(7)->startOfWeek(Carbon::MONDAY)->toDateString();//2025-05-19
    $endPrevWeek = Carbon::now()->subWeek(7)->endOfWeek(Carbon::SUNDAY)->toDateString();//2025-05-25
   if (auth()->user()->portal_type == 'App\\Models\\Client') {
            $summaryQuery = Booking::from('bookings as bookings')
                ->leftJoin('timesheets as timesheets', 'timesheets.bookingId', '=', 'bookings.bookingId')
                ->join('client_units as client_units', 'client_units.clientUnitId', '=', 'bookings.unitId')
                ->leftJoin('quotations as quotations', 'quotations.timesheetId', '=', 'timesheets.timesheetId')
                ->whereIn('bookings.staffStatus', [2, 3])
                ->whereIn('bookings.unitStatus', [1, 4])
                ->where('client_units.clientId', $loggedId)
                ->whereNotNull('quotations.week')
                ->where('quotations.year',date('Y'))
                ->whereNotNull('bookings.staffId')
                ->whereNull('timesheets.invoice_status')
                ->where('quotations.year',date('Y'))
                ->whereBetween('bookings.date', [$startPrevWeek, $endPrevWeek])
                ->orWhereBetween('timesheets.isMovedFromPreviousWeek', [$startPrevWeek, $endPrevWeek])
                    
                ->select([
                    'quotations.week as week_no',
                    'client_units.clientUnitId as unit_id',
                    'client_units.enic_status as enic',
                    DB::raw('MAX(ng_bookings.date) as week_ending'),
                    'client_units.alias as unit_name',
                    DB::raw('COUNT(*) as current_week_ts'),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.verification_from_portal =0 
                                    AND ng_quotations.week IS NOT NULL 
                                THEN 1 ELSE 0 
                            END) as total_ts"),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.isMovedFromPreviousWeek IS NOT NULL 
                                THEN 1 ELSE 0 
                            END) as moved_from_previous"),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.verification_from_portal = 1 
                                THEN 1 ELSE 0 
                            END) as ts_approved"),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.isMovedFromPreviousWeek = 1 
                                THEN 1 ELSE 0 
                            END) as moved_to_next"),
                    DB::raw("MAX(ng_timesheets.invoice_status) as invoice_status"),
                ]);
    }



    // Filters
    if (request('filter_unit') && request('filter_unit') != 0) {
        $summaryQuery->where('bookings.unitId', request('filter_unit'));
    }

    if (request('filter_tax_week')) {
        $summaryQuery->where('quotations.week', $filterWeek);
    }

    $summaryResult = $summaryQuery
        ->groupBy('quotations.week', 'client_units.alias', 'client_units.clientUnitId')
        ->orderBy('quotations.week')
        ->get();

    $summaryData = $summaryResult->map(function ($item) {
        $weekEnding = Carbon::parse($item->week_ending)->endOfWeek(Carbon::SUNDAY);
        return [
            'Week No' => $item->week_no,
            'Enic' => $item->enic,
            'Weekending' => $weekEnding->format('d-F-Y'),
            'Unit' => $item->unit_name,
            'No of shifts current week' => $item->current_week_ts,
            'No of shifts from Previous week' => $item->moved_from_previous,
            'Total No of Ts to approve' => $item->total_ts,
            'No of Ts Approved' => $item->ts_approved,
            'unitId' => $item->unit_id,
            'No of shifts moved to next week' => $item->moved_to_next,
            'Invoice generated' => $item->invoice_status === 1 ? 'Generated' : 'Waiting',
            'Action' => $item->invoice_status === 1 ? 'Generated' : 'Waiting',
        ];
    });

    // Detail Timesheet View
    $selectedWeekTimesheets = collect();
    if($summaryResult->count() > 0){
       
        if ($actionWeek) {

            $query = Booking::with('category', 'unit','staff', 'shift')
                    ->leftJoin('timesheets as timesheets', 'timesheets.bookingId', '=', 'bookings.bookingId')
                    ->join('client_units as client_units', 'client_units.clientUnitId', '=', 'bookings.unitId')
                    ->leftJoin('quotations as quotations', 'quotations.timesheetId', '=', 'timesheets.timesheetId')
                    ->whereIn('bookings.staffStatus', [2,3])
                    ->whereIn('bookings.unitStatus', [1, 4])
                    ->whereNotNull('quotations.week')
                    ->where('quotations.week', $actionWeek)
                    ->whereNotNull('bookings.staffId')
                    ->whereNull('timesheets.invoice_status')
                    // ->whereNotNull('hourly_rate')
                    ->where('quotations.year',date('Y'))
                    ->orderBy('startTime', 'ASC');
            if (request('filter_unit') && request('filter_unit') !== '0') {
                $query->where('bookings.unitId', request('filter_unit'));
            }
            if (request('searchDate')) {
                [$startDate, $endDate] = explode(' - ', request('searchDate'));
                $startDate = Carbon::createFromFormat('d-m-Y', trim($startDate))->startOfDay();
                $endDate = Carbon::createFromFormat('d-m-Y', trim($endDate))->endOfDay();
                $query->whereBetween('date', [$startDate, $endDate]);
            }

            if (request('searchCategory')) {
                $query->where('categoryId', request('searchCategory'));
            }

            if (request('searchShift')) {
                $query->where('shiftId', request('searchShift'));
            }

            if (request('searchStaff')) {
                $query->where('staffId', request('searchStaff'));
            }

            $selectedWeekTimesheets = $query->get();

            foreach($selectedWeekTimesheets as $selectedWeekTimesheet){
                if($selectedWeekTimesheet->hourly_rate==NULL){
                    $hourly_rate = $this->get_hourly_rate($selectedWeekTimesheet);
                    $quotation = Quotation::where('bookingId',$selectedWeekTimesheet->bookingId)->first();
                    $quotation->hourly_rate=$hourly_rate;
                    $quotation->save();
                }
            }

        }
    }
    $staffIds = array_column($selectedWeekTimesheets->toArray(), 'staffId');

    $shifts = Shift::where('status', 1)->get();
    $payrolstaff = Admin::where('status', 1)->where('department_id', 3)->get();
    $units = ClientUnit::where('status', 1)->where('clientId', $loggedId)->orderBy('name', 'ASC')->get();
    $staffs = Staff::whereIn('staffId', $staffIds)->groupBy('staffId')
        ->orderBy('forname', 'ASC')
        ->select('staffs.*')
        ->get();
    $categories = StaffCategory::where('status', 1)->get();
    $schedule = ClientUnitSchedule::where('status', 1)->get();

    return view('clientPortal.ts-approvals', compact(
        'summaryData',
        'shifts',
        'units',
        'staffs',
        'categories',
        'payrolstaff',
        'schedule',
        'selectedWeekTimesheets',
        'actionWeek',
        'TaxYear',
        'TaxWeek'
    ));
}

    public function get_hourly_rate($booking){
        
        $taxyear = TaxYear::where('default',1)->first();
        $allClientPayments = ClientUnitPayment::where('clientUnitId', $booking->unitId)->where('tax_year_id', $taxyear->taxYearId)->get();
        $day = strtolower(date('D', strtotime($booking->date)));
        $shiftId = $booking->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 1);
        $filteredData = $clientPayment->first();
        //echo '<pre>'; print_r($filteredData); exit;
        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
        }
        if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day" . $selectColumn;
        } else if ($shiftId == 5) {
            $columnName = "twilight_" . strtolower($selectColumn);
        } else {
            $columnName = "night" . $selectColumn;
        }
        if (isset($filteredData[$columnName])) {
            $payAmountPerHr = $filteredData[$columnName];
        } else {
            $payAmountPerHr = 0;
        }
        return round($payAmountPerHr,2);
    }
//APPROVED SECTION
    public function ts_approved()
    {       
       $taxyearId = TaxYear::where('default', 1)->first();
    $TaxYear = TaxYear::get();
    $TaxWeek = TaxWeek::selectRaw('MIN(taxWeekId) as taxWeekId, weekNumber')
        ->where('taxYearId', $taxyearId->taxYearId)
        ->groupBy('weekNumber')
        ->orderBy('weekNumber')
        ->get();

    $loggedId = auth()->user()->portal_id;
    $actionWeek = request('action_week');
    $filterWeek = request('filter_tax_week');
    $filterYear = request('filter_tax_year') ?: $taxyearId->taxYearFrom;
    //APPROVED SECTION
    $oneMonthAgo = Carbon::now()->subMonth()->startOfDay();
    $today = Carbon::now()->endOfDay();
    $startPrevWeek = Carbon::now()->subWeeks(7)->startOfWeek(Carbon::MONDAY)->toDateString();//2025-05-19
    $endPrevWeek = Carbon::now()->subWeek(7)->endOfWeek(Carbon::SUNDAY)->toDateString();//2025-05-25
   if (auth()->user()->portal_type == 'App\\Models\\Client') {
            $summaryQuery = Booking::from('bookings as bookings')
                ->leftJoin('timesheets as timesheets', 'timesheets.bookingId', '=', 'bookings.bookingId')
                ->join('client_units as client_units', 'client_units.clientUnitId', '=', 'bookings.unitId')
                ->leftJoin('quotations as quotations', 'quotations.timesheetId', '=', 'timesheets.timesheetId')
                ->whereIn('bookings.staffStatus', [2, 3])
                ->whereIn('bookings.unitStatus', [1, 4])
                ->where('client_units.clientId', $loggedId)
                ->whereNotNull('quotations.week')
                ->where('quotations.year',date('Y'))
                ->whereNotNull('bookings.staffId')
                ->where('timesheets.invoice_status',1)
                ->where('quotations.year',date('Y'))
                ->whereBetween('bookings.date', [$startPrevWeek, $endPrevWeek])
                ->orWhereBetween('timesheets.isMovedFromPreviousWeek', [$startPrevWeek, $endPrevWeek])
                    
                ->select([
                    'quotations.week as week_no',
                    'client_units.clientUnitId as unit_id',
                    'client_units.enic_status as enic',
                    DB::raw('MAX(ng_bookings.date) as week_ending'),
                    'client_units.alias as unit_name',
                    DB::raw('COUNT(*) as current_week_ts'),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.verification_from_portal =0 
                                    AND ng_quotations.week IS NOT NULL 
                                THEN 1 ELSE 0 
                            END) as total_ts"),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.isMovedFromPreviousWeek IS NOT NULL 
                                THEN 1 ELSE 0 
                            END) as moved_from_previous"),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.verification_from_portal = 1 
                                THEN 1 ELSE 0 
                            END) as ts_approved"),
                    DB::raw("SUM(CASE 
                                WHEN ng_timesheets.isMovedFromPreviousWeek = 1 
                                THEN 1 ELSE 0 
                            END) as moved_to_next"),
                    DB::raw("MAX(ng_timesheets.invoice_status) as invoice_status"),
                ]);
    }


    //APPROVED SECTION
    // Filters
    if (request('filter_unit') && request('filter_unit') != 0) {
        $summaryQuery->where('bookings.unitId', request('filter_unit'));
    }

    if (request('filter_tax_week')) {
        $summaryQuery->where('quotations.week', $filterWeek);
    }

    $summaryResult = $summaryQuery
        ->groupBy('quotations.week', 'client_units.alias', 'client_units.clientUnitId')
        ->orderBy('quotations.week')
        ->get();
    //APPROVED SECTION
    $summaryData = $summaryResult->map(function ($item) {
        $weekEnding = Carbon::parse($item->week_ending)->endOfWeek(Carbon::SUNDAY);
        return [
            'Week No' => $item->week_no,
            'Enic' => $item->enic,
            'Weekending' => $weekEnding->format('d-F-Y'),
            'Unit' => $item->unit_name,
            'No of shifts current week' => $item->current_week_ts,
            'No of shifts from Previous week' => $item->moved_from_previous,
            'Total No of Ts to approve' => $item->total_ts,
            'No of Ts Approved' => $item->ts_approved,
            'unitId' => $item->unit_id,
            'No of shifts moved to next week' => $item->moved_to_next,
            'Invoice generated' => $item->invoice_status === 1 ? 'Generated' : 'Waiting',
            'Action' => $item->invoice_status === 1 ? 'Generated' : 'Waiting',
        ];
    });
    //APPROVED SECTION
    // Detail Timesheet View
    $selectedWeekTimesheets = collect();
    if($summaryResult->count() > 0){
       
        if ($actionWeek) {

            $query = Booking::with('category', 'unit','staff', 'shift')
                    ->leftJoin('timesheets as timesheets', 'timesheets.bookingId', '=', 'bookings.bookingId')
                    ->join('client_units as client_units', 'client_units.clientUnitId', '=', 'bookings.unitId')
                    ->leftJoin('quotations as quotations', 'quotations.timesheetId', '=', 'timesheets.timesheetId')
                    ->whereIn('bookings.staffStatus', [2,3])
                    ->whereIn('bookings.unitStatus', [1, 4])
                    ->whereNotNull('quotations.week')
                    ->where('quotations.week', $actionWeek)
                    ->whereNotNull('bookings.staffId')
                    ->where('timesheets.invoice_status',1)
                    // ->whereNotNull('hourly_rate')
                    ->where('quotations.year',date('Y'))
                    ->orderBy('startTime', 'ASC');
            if (request('filter_unit') && request('filter_unit') !== '0') {
                $query->where('bookings.unitId', request('filter_unit'));
            }
            if (request('searchDate')) {
                [$startDate, $endDate] = explode(' - ', request('searchDate'));
                $startDate = Carbon::createFromFormat('d-m-Y', trim($startDate))->startOfDay();
                $endDate = Carbon::createFromFormat('d-m-Y', trim($endDate))->endOfDay();
                $query->whereBetween('date', [$startDate, $endDate]);
            }

            if (request('searchCategory')) {
                $query->where('categoryId', request('searchCategory'));
            }

            if (request('searchShift')) {
                $query->where('shiftId', request('searchShift'));
            }

            if (request('searchStaff')) {
                $query->where('staffId', request('searchStaff'));
            }

            $selectedWeekTimesheets = $query->get();
            //APPROVED SECTION
            foreach($selectedWeekTimesheets as $selectedWeekTimesheet){
                if($selectedWeekTimesheet->hourly_rate==NULL){
                    $hourly_rate = $this->get_hourly_rate($selectedWeekTimesheet);
                    $quotation = Quotation::where('bookingId',$selectedWeekTimesheet->bookingId)->first();
                    $quotation->hourly_rate=$hourly_rate;
                    $quotation->save();
                }
            }

        }
    }
    $staffIds = array_column($selectedWeekTimesheets->toArray(), 'staffId');

    $shifts = Shift::where('status', 1)->get();
    $payrolstaff = Admin::where('status', 1)->where('department_id', 3)->get();
    $units = ClientUnit::where('status', 1)->where('clientId', $loggedId)->orderBy('name', 'ASC')->get();
    $staffs = Staff::whereIn('staffId', $staffIds)->groupBy('staffId')
        ->orderBy('forname', 'ASC')
        ->select('staffs.*')
        ->get();
    $categories = StaffCategory::where('status', 1)->get();
    $schedule = ClientUnitSchedule::where('status', 1)->get();

    return view('clientPortal.ts-approved', compact(
        'summaryData',
        'shifts',
        'units',
        'staffs',
        'categories',
        'payrolstaff',
        'schedule',
        'selectedWeekTimesheets',
        'actionWeek',
        'TaxYear',
        'TaxWeek'
    ));
    }
    public function approve(Request $request, $id)
    {
        $timesheet = Timesheet::find($id);
        if ($timesheet) {
            $timesheet->invoice_generated_by = Auth::user()->id; // Assuming the user is the one approving
            $timesheet->verification_from_portal = 1; // Mark as verified from portal
            $timesheet->save();
            return response()->json(['success' => true, 'message' => 'Timesheet approved successfully']);
        }
        return response()->json(['success' => false, 'message' => 'Timesheet not found'], 404);
    }
    public function decline(Request $request, $id)
    {
        $timesheet = Timesheet::find($id);
        if ($timesheet) {
            $timesheet->invoice_generated_by = Auth::user()->id; // Assuming the user is the one declining
            $timesheet->verification_from_portal = 2; // Mark as verified from portal
            $timesheet->decline_reason = request('decline_reason');
            $timesheet->save();
            return response()->json(['success' => true, 'message' => 'Timesheet declined successfully']);
        }
        return response()->json(['success' => false, 'message' => 'Timesheet not found'], 404);
    }

    public function approveMultiple(Request $request)
    {
        $timesheetIds = $request->input('timesheet_ids');

        if (empty($timesheetIds) || !is_array($timesheetIds)) {
            return response()->json(['success' => false, 'message' => 'No timesheets selected.'], 400);
        }
        $timesheets = Timesheet::whereIn('timesheetId', $timesheetIds)->get();

        if ($timesheets->isEmpty()) {
            return response()->json(['success' => false, 'message' => 'No valid timesheets found.'], 404);
        }

        foreach ($timesheets as $timesheet) {
            
            $timesheet->invoice_generated_by = Auth::user()->id;
            $timesheet->verification_from_portal = 1; // Mark as approved
            $timesheet->save();
        }

        return response()->json(['success' => true, 'message' => 'Selected timesheets approved successfully.']);
    }

    public function generateInvoiceMultiple(Request $request)
    {
        $timesheetIds = $request->input('timesheet_ids');

        if (empty($timesheetIds) || !is_array($timesheetIds)) {
            return response()->json(['success' => false, 'message' => 'No timesheets selected.'], 400);
        }

        $timesheets = Timesheet::whereIn('timesheetId', $timesheetIds)->get();

        if ($timesheets->isEmpty()) {
            return response()->json(['success' => false, 'message' => 'No valid timesheets found.'], 404);
        }

        foreach ($timesheets as $timesheet) {
            if($timesheet->status==2){
                    $bookings = Booking::where('bookingId', $timesheet->bookingId)->first();
                    if (!$bookings) {
                        return response()->json(['success' => false, 'message' => 'Booking not found for timesheet ID: ' . $timesheet->timesheetId], 404);
                    }else{
                        $final_cost = $this->finalcostCalculation($bookings);
                        $bookings->unit_final_cost = $final_cost;
                        $bookings->save();
                    }
                    // You can adjust this part based on your invoice logic
                    if($timesheet->verification_from_portal == 1){
                        $timesheet->invoice_generated_by = Auth::user()->id;
                        $timesheet->invoice_status = 1; // Assuming this field marks invoice status
                        $timesheet->save();
                    }    
            }
        }
        return response()->json(['success' => true, 'message' => 'Invoice generated successfully for selected timesheets.']);
    }
    public static function moveToNextWeek(){
        $timesheetIds = request('timesheet_ids');
        $timesheets   = Timesheet::whereIn('timesheetId', $timesheetIds)->get();
        foreach ($timesheets as $bookId) {
        $payment = Payment::where('bookingId',$bookId->bookingId)->first();
        $currentWeek = $payment->paymentWeek;
        $year =$payment->paymentYear;
        $nextWeek = $currentWeek+1;
        if($nextWeek > 52){
            $nextWeek = 1;
        }
        $bookId->isMovedToNextWeek = 1;
        $bookId->isMovedFromPreviousWeek = date('Y-m-d'); 

        $payment->paymentWeek = $nextWeek;
        $payment->save();
        BookingLog::create([
            'bookingId' =>$payment->booking->bookingId,
            'content' =>"<span class='logHgt'>Payment Marked </span> as <strong>Move to Next week</strong> ",
            'author' =>1,
        ]);
        }
        return response()->json(['success' => true, 'message' => 'Invoice generated successfully for selected timesheets.']);
  }
   public function calculateCostBookingNumberonly($booking)
    {
         $totalHoursUnit=0;
        $ta=0;
        $unitId = auth()->user()->portal_id;
        $allClientPayments = ClientUnitPayment::where('clientUnitId', $unitId)->get();
        $day = strtolower(date('D', strtotime($booking->date)));
        $shiftId = $booking->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 1);
        $filteredData = $clientPayment->first();

        if(isset($filteredData->taType)){

        if($filteredData->taType==1){

        }else if($filteredData->taType==2){
            if(isset($filteredData->taPerMile)){
             $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
            }else{
             $ta = 0;
            }
        }else if($filteredData->taType==3){
            if(isset($getTaMile->invoiceTaEditable)){
                 $ta = $getTaMile->invoiceTaEditable;
            }else{
                if(isset($getTaMile->invoiceTa)){
                    $ta = $getTaMile->invoiceTa;
                }
            }
        }
      }else{
            $ta = 0;
        }

        //$ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];

        $clientPaymentsEnic = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 2);
        $filteredDataEnic = $clientPaymentsEnic->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
        }

        if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day" . $selectColumn;
        } else if ($shiftId == 5) {
            $columnName = "twilight_" . strtolower($selectColumn);
        } else {
            $columnName = "night" . $selectColumn;
        }
        if (isset($filteredData->$columnName)) {
            $payAmountPerHr = $filteredData->$columnName;
        } else {
            $payAmountPerHr = 0;
        }
        //$payAmountPerHr = $filteredData[$columnName];

        if (isset($filteredDataEnic->$columnName)) {
            $clientPaymentsEnicRate = $filteredDataEnic->$columnName;
        } else {
            $clientPaymentsEnicRate = 0;
        }
        $schedule = ClientUnitSchedule::where('clientUnitId', $booking->unitId)->where('staffCategoryId', $booking->category->categoryId)->where('shiftId', $booking->shift->shiftId)->first();

        if(isset($schedule)){
            if(isset($payment->timesheet->unitHours)){
                $totalHoursUnit = $payment->timesheet->unitHours;
            }else{
                $totalHoursUnit = $schedule->totalHoursUnit;
            }
        }
        $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate) + $ta;
        return  number_format($inTotal, 2);
    }
    public function finalcostCalculation($booking)
    {   
        
        if( $booking->unitStatus!=4 || //4 is confirmed from the unit.
            $booking->staffStatus==1 || //1 not confirmed or potential.
            $booking->staffId==NULL || //staff not assigned.
            $booking->timesheet->status!=2){ //Timesheet not approved.
            return 0;
        }
        $taxyear = TaxYear::where('default',1)->first();
        $allClientPayments = ClientUnitPayment::where('clientUnitId', $booking->unitId)->where('tax_year_id', $taxyear->taxYearId)->get();
        $day = strtolower(date('D', strtotime($booking->date)));
        $shiftId = $booking->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 1);
        $filteredData = $clientPayment->first();

        if (isset($filteredData->taPerMile) && isset($filteredData->taNoOfMiles)) {
            $ta = $filteredData->taPerMile * $filteredData->taNoOfMiles;
        } else {
            $ta = 0;
        }
        $getTaMile = StaffUnitPayment::where('clientUnitId',$booking->unitId)->where('tax_year_id',$taxyear->taxYearId)
        ->where('staffId',$booking->staffId)->first();
        $timesheet = Timesheet::where('bookingId', $booking->bookingId)->where('status', 2)->first();
        if(isset($timesheet->unit_expense_amount)){
            $additionalExpenseAmount = $timesheet->unit_expense_amount;
        }else{
            $additionalExpenseAmount = 0;
        }
        if($filteredData->taType==1){
              $ta = 0;
            }else if($filteredData->taType==2){
              if(isset($filteredData->taPerMile)){
                $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
              }else{
                $ta = 0;
              }
            }else if($filteredData->taType==3){
              if(isset($getTaMile->invoiceTaEditable)){
                $ta = $getTaMile->invoiceTaEditable;
              }else{
                if(isset($getTaMile->invoiceTa)){
                  $ta = $getTaMile->invoiceTa;
                }
              }              
        }
        $clientPaymentsEnic = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 2);
        $filteredDataEnic = $clientPaymentsEnic->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
        }
        if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day" . $selectColumn;
        } else if ($shiftId == 5) {
            $columnName = "twilight_" . strtolower($selectColumn);
        } else {
            $columnName = "night" . $selectColumn;
        }
        if (isset($filteredData[$columnName])) {
            $payAmountPerHr = $filteredData[$columnName];
        } else {
            $payAmountPerHr = 0;
        }
        if (isset($filteredDataEnic[$columnName])) {
            $clientPaymentsEnicRate = $filteredDataEnic[$columnName];
        } else {
            $clientPaymentsEnicRate = 0;
        }

        // Check if timesheet has unitHours, otherwise get from schedule
        if(isset($timesheet->unitHours)){
            $totalHoursUnit = $timesheet->unitHours;
        }else{
            $schedule = ClientUnitSchedule::where('clientUnitId', $booking->unitId)->where('staffCategoryId', $booking->category->categoryId)->where('shiftId', $booking->shift->shiftId)->first();
            if (isset($schedule->totalHoursUnit)) {
                $totalHoursUnit = $schedule->totalHoursUnit;
            } else {
                $totalHoursUnit = 0;
            }
        }
        
        $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate) + $ta + $additionalExpenseAmount;
        return  number_format($inTotal, 2);
    }
}
