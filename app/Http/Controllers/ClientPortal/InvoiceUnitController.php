<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\ClientUnit;
use App\Models\Holiday;
use App\Models\InvoiceUnit;
use Carbon\Carbon;
use App\Models\Quotation;
use Carbon\CarbonImmutable;
use App\Services\UnitHelperService;
use Illuminate\Http\Request;


class InvoiceUnitController extends Controller
{
  protected $unitHelper;
  protected $clientUnitId; 
  
  public function __construct(UnitHelperService $unitHelper)
  {
      $this->unitHelper = $unitHelper;
     
  }
    public function index(){
        $invoices = InvoiceUnit::with('unit')->orderBy('unit_id')->orderBy('invoice_date')->where('client_id',request('client'))->get();
        foreach ($invoices as $invoiceItem) {
            $diffDays = Carbon::now()->diffInDays(Carbon::parse($invoiceItem->due_date),false);
            $invoiceItem->diffDays = $diffDays;
            if($invoiceItem->status == 0 && $diffDays < 0){
              $invoiceItem->redBg = 1;
            }elseif($invoiceItem->status == 0 && $diffDays > 0){
              $invoiceItem->redBg = 2;
            }else{
              $invoiceItem->redBg = 0;
            }
          }
    	$units = ClientUnit::where('clientId',request('client'))->where('clientUnitId','<>',81)->get();
        $events = Holiday::where('date','>=',date('Y-m-d'))->orderBy('date','DESC')->take(5)->get();
        $client = Client::find(request('client'));
      	return ['status'=>200,'invoices'=>$invoices,'units'=>$units,'events'=>$events,'client'=>$client];
    }
    public function invoiceslist(){
      $invoice = InvoiceUnit::where('unit_id',auth()->user()->portal_id)->orderBy('invoice_id','DESC')->get();
      foreach ($invoice as $invoiceItem) {
        $diffDays = CarbonImmutable::now()->diffInDays(CarbonImmutable::parse($invoiceItem->due_date), false);
        $invoiceItem->diffDays = $diffDays;
        if($invoiceItem->status == 0 && $diffDays < 0){
          $invoiceItem->redBg = 1;
        }elseif($invoiceItem->status == 0 && $diffDays > 0){
          $invoiceItem->redBg = 2;
        }else{
          $invoiceItem->redBg = 0;
        }
      }
      $unitHelper = app(UnitHelperService::class);
      $events = $unitHelper->getComingEvents();
      return view('unitPortal.invoicelist',compact('invoice','events'));
  }
    public function invoice_listing(){
      $loggedId = auth()->user()->portal_id;
      if (auth()->user()->portal_type == 'App\\Models\\Client') {
          $query = Quotation::with(['booking.unit', 'timesheet','booking.staff','booking.shift','booking.unit.client','booking.category'])
              ->whereHas('booking.unit', function ($q) use ($loggedId) {
                  $q->where('clientId', $loggedId);
              })
              ->where('status', 1)
              ->orderBy('created_at', 'DESC');
              
      }
      $quotation = $query->first();
      //echo '<pre>'; print_r($quotation); exit;
      $units = ClientUnit::where('clientId',auth()->user()->portal_id)->where('clientUnitId','<>',81)->get();
      return view('unitPortal.invoice_listing',compact('units','quotation'));
  }
}