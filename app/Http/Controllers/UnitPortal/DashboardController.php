<?php

namespace App\Http\Controllers\UnitPortal;
use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Client;
use App\Models\ClientUnit;
use App\Models\InvoiceUnit;
use App\Models\ClientUnitContact;
//use App\Http\Controllers\UnitPortal\UnitHelper;
use App\Models\Staff;
use Storage;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Arr;
use App\Services\UnitHelperService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\TaxYear;
use App\Models\ClientUnitPayment;
use App\Models\ClientUnitSchedule;


class DashBoardController extends Controller
{
  protected $unitHelper;
  protected $clientUnitId; 
  
  public function __construct(UnitHelperService $unitHelper)
  {
      $this->unitHelper = $unitHelper;
      if (Auth::check()) {
        $this->clientUnitId = Auth::user()->portal_id;
      }else{
        return redirect()->route('login');
      }
  }

  public function dashboard(){
    $TaxYear = TaxYear::where('default',1)->first();
    $taxYearId = $TaxYear->taxYearId;
    $events = $this->unitHelper->getComingEvents();
    $clientCOntacts = ClientUnitContact::where('clientUnitId',Auth::user()->portal_id)->take(6)->get();
    $thirtiethDay = Carbon::today()->addDays(29); 
    $thisMonth = Booking::
    whereBetween('date', [date('Y-m-d'), $thirtiethDay->format('Y-m-d')])
                ->
                where('unitId',Auth::user()->portal_id)
                ->whereIn('unitStatus',[3,4,1])
                ->get();

                $categoryCount = DB::table('bookings')
                ->select('categoryId', DB::raw('count(*) as category_count'))
               
                ->whereBetween('date', [date('Y-m-d'), $thirtiethDay->format('Y-m-d')])
                ->where('unitId', Auth::user()->portal_id)
                ->whereIn('categoryId', [1, 2, 3])
                ->whereIn('unitStatus',[3,4,1])
                ->groupBy('categoryId')
                ->get();
                
                $categories = [1, 2, 3];
                $scheduleData = [];
                foreach ($categories as $categoryId) {
                    $scheduleData[$categoryId] = [
                        'payment' => 0,
                        'category' => '',
                        'count' => 0,
                        'totalHours' => 0,
                    ];
                }

                foreach ($thisMonth as $thismo) {
                  $selectColumn = Carbon::parse($thismo->date)->format('l'); // Get the day name
                  $columnName = $this->determineColumnName($thismo, $selectColumn);
          
                  $payment = $this->getpayment($columnName, $thismo->categoryId);
                  $schedule = $this->getschedule($columnName, $thismo->categoryId, $thismo->shiftId);
          
                  if (in_array($thismo->categoryId, $categories)) {
                      if(isset($schedule->totalHoursUnit)){
                        $paymentResult = $schedule->totalHoursUnit * $payment->$columnName;  
                      }else{
                        $paymentResult =  $payment->$columnName;  
                      }
                      
                      $scheduleData[$thismo->categoryId]['payment'] += $paymentResult;
                      $scheduleData[$thismo->categoryId]['category'] = $thismo->category->name;
                      $scheduleData[$thismo->categoryId]['count']++;
                      if (isset($schedule->totalHoursUnit)) {
                        $scheduleData[$thismo->categoryId]['totalHours'] += $schedule->totalHoursUnit;
                       // $scheduleData[$thismo->categoryId]['cost'] += $schedule->totalHoursUnit * $payment->$columnName;
                    }
                  }
              }


              $reasonCategories = [
                'sickness' => 1,
                'holiday' => 2,
                'vacant' => 3,
                'resident_admission' => 4,
                '1_to_1' => 5,
                'extra_staff' => 6,
                'management' => 7,
                'others' => null,
            ];
        
            // Initialize an array to store the total costs and counts for each reason
            $totalCosts = array_fill_keys(array_keys($reasonCategories), ['cost' => 0, 'count' => 0]);
        
            // Iterate over each booking to calculate the total cost and count
            foreach ($thisMonth as $booking) {
                // Determine the reason category
                $reason = array_search($booking->reason, $reasonCategories);
        
                // Skip if the reason is not recognized
                if ($reason === false) {
                    continue;
                }
        
                // Get the day of the week for the booking date
                $selectColumn = Carbon::parse($booking->date)->format('l'); // Get the day name
                $columnName = $this->determineColumnName($booking, $selectColumn);
        
                // Fetch the payment details for the category
                $payment = $this->getpayment($columnName, $booking->categoryId);
        
                // Fetch the schedule details for the category and shift
                $schedule = $this->getschedule($columnName, $booking->categoryId, $booking->shiftId);
        
                // Calculate the total cost based on the presence of schedule hours
                $paymentAmount = isset($schedule->totalHoursUnit)
                    ? $schedule->totalHoursUnit * $payment->$columnName
                    : $payment->$columnName;
        
                // Accumulate the total cost and count for the reason category
                $totalCosts[$reason]['cost'] += $paymentAmount;
                $totalCosts[$reason]['count']++;
            }
        
    $reason='';
    $staffs = Staff::with('category')->whereNot('branchId', 3)->where('status', 'active')->get();
     for ($i=0; $i < count($staffs); $i++) {
        $thisCount = $thisMonth->where('staffId',$staffs[$i]->staffId)->count();
        $staffs[$i]->bookingCount = $thisCount;
     }
        $frequentStaffs = $staffs->sortByDesc('bookingCount')->take(8)->values();
    return view('unitPortal.dashboard',compact('events','clientCOntacts','frequentStaffs','reason','categoryCount','scheduleData','totalCosts'));
  }
  public function overviewreports(){
    $TaxYear = TaxYear::where('default',1)->first();
    $taxYearId = $TaxYear->taxYearId;
    $events = $this->unitHelper->getComingEvents();
    $clientCOntacts = ClientUnitContact::where('clientUnitId',Auth::user()->portal_id)->take(6)->get();
    $thirtiethDay = Carbon::today()->addDays(29); 
    $thisMonth = Booking::
    whereBetween('date', [date('Y-m-d'), $thirtiethDay->format('Y-m-d')])
                ->
                where('unitId',Auth::user()->portal_id)
                ->whereIn('unitStatus',[3,4,1])
                ->get();

                $categoryCount = DB::table('bookings')
                ->select('categoryId', DB::raw('count(*) as category_count'))
               
                ->whereBetween('date', [date('Y-m-d'), $thirtiethDay->format('Y-m-d')])
                ->where('unitId', Auth::user()->portal_id)
                ->whereIn('categoryId', [1, 2, 3])
                ->whereIn('unitStatus',[3,4,1])
                ->groupBy('categoryId')
                ->get();
                
                $categories = [1, 2, 3];
                $scheduleData = [];
                foreach ($categories as $categoryId) {
                    $scheduleData[$categoryId] = [
                        'payment' => 0,
                        'category' => '',
                        'count' => 0,
                        'totalHours' => 0,
                    ];
                }

                foreach ($thisMonth as $thismo) {
                  $selectColumn = Carbon::parse($thismo->date)->format('l'); // Get the day name
                  $columnName = $this->determineColumnName($thismo, $selectColumn);
          
                  $payment = $this->getpayment($columnName, $thismo->categoryId);
                  $schedule = $this->getschedule($columnName, $thismo->categoryId, $thismo->shiftId);
          
                  if (in_array($thismo->categoryId, $categories)) {
                      if(isset($schedule->totalHoursUnit)){
                        $paymentResult = $schedule->totalHoursUnit * $payment->$columnName;  
                      }else{
                        $paymentResult =  $payment->$columnName;  
                      }
                      
                      $scheduleData[$thismo->categoryId]['payment'] += $paymentResult;
                      $scheduleData[$thismo->categoryId]['category'] = $thismo->category->name;
                      $scheduleData[$thismo->categoryId]['count']++;
                      if (isset($schedule->totalHoursUnit)) {
                        $scheduleData[$thismo->categoryId]['totalHours'] += $schedule->totalHoursUnit;
                       // $scheduleData[$thismo->categoryId]['cost'] += $schedule->totalHoursUnit * $payment->$columnName;
                    }
                  }
              }


              $reasonCategories = [
                'sickness' => 1,
                'holiday' => 2,
                'vacant' => 3,
                'resident_admission' => 4,
                '1_to_1' => 5,
                'extra_staff' => 6,
                'management' => 7,
                'others' => null,
            ];
        
            // Initialize an array to store the total costs and counts for each reason
            $totalCosts = array_fill_keys(array_keys($reasonCategories), ['cost' => 0, 'count' => 0]);
        
            // Iterate over each booking to calculate the total cost and count
            foreach ($thisMonth as $booking) {
                // Determine the reason category
                $reason = array_search($booking->reason, $reasonCategories);
        
                // Skip if the reason is not recognized
                if ($reason === false) {
                    continue;
                }
        
                // Get the day of the week for the booking date
                $selectColumn = Carbon::parse($booking->date)->format('l'); // Get the day name
                $columnName = $this->determineColumnName($booking, $selectColumn);
        
                // Fetch the payment details for the category
                $payment = $this->getpayment($columnName, $booking->categoryId);
        
                // Fetch the schedule details for the category and shift
                $schedule = $this->getschedule($columnName, $booking->categoryId, $booking->shiftId);
        
                // Calculate the total cost based on the presence of schedule hours
                $paymentAmount = isset($schedule->totalHoursUnit)
                    ? $schedule->totalHoursUnit * $payment->$columnName
                    : $payment->$columnName;
        
                // Accumulate the total cost and count for the reason category
                $totalCosts[$reason]['cost'] += $paymentAmount;
                $totalCosts[$reason]['count']++;
            }
        
    $reason='';
    $staffs = Staff::with('category')->whereNot('branchId', 3)->where('status', 'active')->get();
     for ($i=0; $i < count($staffs); $i++) {
        $thisCount = $thisMonth->where('staffId',$staffs[$i]->staffId)->count();
        $staffs[$i]->bookingCount = $thisCount;
     }
        $frequentStaffs = $staffs->sortByDesc('bookingCount')->take(8)->values();
    return view('unitPortal.managementReports',compact('events','clientCOntacts','frequentStaffs','reason','categoryCount','scheduleData','totalCosts'));
  }


  public function getpayment($column,$category){
   return $clientPayments = ClientUnitPayment::select($column)->
    //where('clientUnitId',Auth::user()->portal_id)
    // ->where('rateType',1)
    //->where('status',1)
    // ->where('staffCategoryId',$category)
    // ->where('tax_year_id',$taxYearId)
    //->
    first();
  }
  public function getschedule($column,$category,$shiftId){
   return $scheduleAll = ClientUnitSchedule::select('totalHoursUnit')->where('clientUnitId',Auth::user()->portal_id)
    ->where('staffCategoryId',$category)
    ->where('status',1)
    ->where('shiftId',$shiftId)
    ->first();

    }
    public function determineColumnName($thismo, $selectColumn)
    {
    if (in_array($thismo->shiftId, [1, 2, 3])) {
        return "day" . $selectColumn;
    } elseif ($thismo->shiftId == 5) {
        return "twilight_" . strtolower($selectColumn);
    } else {
        return "night" . $selectColumn;
    }
  }
  public function graph(){
   
    $days = Arr::flatten([$this->getComingDays(30)]);
    for ($i=0; $i < count($days); $i++) {
      $bookings = Booking::whereDate('date',date('Y-m-d',strtotime($days[$i])))->where('unitId',Auth::user()->portal_id)
      ->whereIn('unitStatus',[4,1])
      ->get();
      $bookHCADataConfirmed[] = $bookings->where('categoryId','2')->count();
      $bookSHCADataConfirmed[] = $bookings->where('categoryId','3')->count();
      $bookRGNDataConfirmed[] = $bookings->where('categoryId','1')->count();
    }

    $lastMonth = Booking::query()
                    ->whereMonth('date', now()->subMonth()->month)
                    ->whereYear('date', now()->year)
                    ->where('unitId', auth()->user()->portal_id)
                    ->whereIn('unitStatus', [4,1])
                    ->get();

    $thisMonth = Booking::whereMonth('date',date('m'))->whereYear('date',date('Y'))->where('unitId',Auth::user()->portal_id)
    ->whereIn('unitStatus', [4,1])
    ->get();
    $nextMonth = Booking::whereMonth('date',date('m')+1)->whereYear('date',date('Y'))->where('unitId',Auth::user()->portal_id)
    ->whereIn('unitStatus', [4,1])
    ->get();

    return [
              'bar'=>
                  [
                    'categories'=>$days,
                    'confirmed'=>[
                      'data1'=>$bookRGNDataConfirmed,
                      'data2'=>$bookHCADataConfirmed,
                      'data3'=>$bookSHCADataConfirmed
                    ]
                  ],
              'line'=>
                  [
                    [
                      'name'=>'All',
                      'data'=> [$lastMonth->count(), $thisMonth->count(),$nextMonth->count()]
                    ],
                    [
                      'name'=>'Covered',
                      'data'=> [$lastMonth->where('staffStatus',3)->count(), $thisMonth->where('staffStatus',3)->count(),$nextMonth->where('staffStatus',3)->count()]
                    ],
                  ]
              ];
  }

  public function getComingDays($days,$format='d-M'){
    $m = date("m"); $de= date("d"); $y= date("Y");
    $dateArray = [];
    $dateArray[] = date('d-M');
    for($i=1; $i < $days; $i++){
        $dateArray[] = date('d-M', strtotime('+'.$i.' day'));
    }
    return $dateArray;
  }

  public function invoices(){
      $invoice = InvoiceUnit::where('unit_id',auth()->user()->portal_id)->orderBy('invoice_id','DESC')->get();
      foreach ($invoice as $invoiceItem) {
        $diffDays = CarbonImmutable::now()->diffInDays(CarbonImmutable::parse($invoiceItem->due_date), false);
        $invoiceItem->diffDays = $diffDays;
        if($invoiceItem->status == 0 && $diffDays < 0){
          $invoiceItem->redBg = 1;
        }elseif($invoiceItem->status == 0 && $diffDays > 0){
          $invoiceItem->redBg = 2;
        }else{
          $invoiceItem->redBg = 0;
        }
      }
      $unitHelper = app(UnitHelperService::class);
      $events = $unitHelper->getComingEvents();
      return view('unitPortal.invoice',compact('invoice','events'));
  }
  public function termsAndConditions(){
    $events = UnitHelper::getcomingEvents();
    return view('unitPortal.termsAndCondition',compact('events'));
  }
  public function privacyPolicy(){
    $events = UnitHelper::getcomingEvents();
    return view('unitPortal.privacyPolicy',compact('events'));
  }
  public function getHourlyRate($payment,$allClientPayments){
    $day = strtolower(date('D',strtotime($payment->date)));
        $shiftId = $payment->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId',$payment->category->categoryId)->where('rateType',1);
        $filteredData = $clientPayment->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
            }

        if($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day".$selectColumn;
        } else if($shiftId == 5) {
            $columnName = "twilight_".strtolower($selectColumn);
        } else {
            $columnName = "night".$selectColumn;
        }
        $payAmountPerHr = $filteredData[$columnName] ?? 0;
        return $payment['hourlyRate'] = "£ ". number_format(($payAmountPerHr),2);
}

public function yearData(){
  for ($i=1; $i < 13; $i++) { 
    if($i < 10)  $months[] = date('Y-0'.$i.'-01');
    else $months[] = date('Y-'.$i.'-01');

    $monthNames[] = date('F',strtotime(end($months)));
  }

  $lastYear = date('Y')-1;

  $last2Year = date('Y')-2;

  $last3Year = date('Y')-3;
  
  for ($i=0; $i < count($months); $i++) {
    $thisMonthStart = date('Y-m-01',strtotime($months[$i]));
    $thisMonthEnd = date('Y-m-t',strtotime($months[$i]));

    $lastMonthStart = date($lastYear.'-m-01',strtotime($months[$i]));
    $lastMonthEnd = date($lastYear.'-m-t',strtotime($months[$i]));

    $last2MonthStart = date($last2Year.'-m-01',strtotime($months[$i]));
    $last2MonthEnd = date($last2Year.'-m-t',strtotime($months[$i]));

    $last3MonthStart = date($last3Year.'-m-01',strtotime($months[$i]));
    $last3MonthEnd = date($last3Year.'-m-t',strtotime($months[$i]));

    $thisDayFull = Booking::ignoredummy()->where('unitId', Auth::user()->portal_id)->whereYear('date',date('Y'))->where('unitStatus',4)->where('staffStatus',3)->where('date','>=',$thisMonthStart)->where('date','<=',$thisMonthEnd)->count();
    $thisYearGraph[] = $thisDayFull;

    $thisDayFull = Booking::ignoredummy()->where('unitId', Auth::user()->portal_id)->whereYear('date',$lastYear)->where('unitStatus',4)->where('staffStatus',3)->where('date','>=',$lastMonthStart)->where('date','<=',$lastMonthEnd)->count();
    $lastYearGraph[] = $thisDayFull;

    $thisDayFull = Booking::ignoredummy()->where('unitId', Auth::user()->portal_id)->whereYear('date',$last2Year)->where('unitStatus',4)->where('staffStatus',3)->where('date','>=',$last2MonthStart)->where('date','<=',$last2MonthEnd)->count();
    $last2YearGraph[] = $thisDayFull;

    $thisDayFull = Booking::ignoredummy()->where('unitId', Auth::user()->portal_id)->whereYear('date',$last3Year)->where('unitStatus',4)->where('staffStatus',3)->where('date','>=',$last3MonthStart)->where('date','<=',$last3MonthEnd)->count();
    $last3YearGraph[] = $thisDayFull;

  }
  $response = [
    'series'=>[
      ['name'  => date('Y'), 'data'  => $thisYearGraph],
      ['name'  => $lastYear, 'data'  => $lastYearGraph],
      ['name'  => $last2Year, 'data'  => $last2YearGraph],
      ['name'  => $last3Year, 'data'  => $last3YearGraph],
      
    ],
    'categories'=>$monthNames,
  ];
  return response()->json($response);
}



public function getMonthlyCoverage()
{
    // Get the current date and subtract 1 month to exclude the current month
    $startDate = Carbon::now()->subMonths(5)->startOfMonth(); // 5 months ago, start of the month
    $endDate = Carbon::now()->subMonth()->endOfMonth(); // 1 month ago, end of the month

    // Fetch the total bookings per month for the last 4 months
    $totalBookings = Booking::select(
            DB::raw('YEAR(date) as year'),
            DB::raw('MONTH(date) as month'),
            DB::raw('COUNT(*) as total')
        )
        ->where('unitId', Auth::user()->portal_id)
        ->whereIn('unitStatus', [3, 4, 1])
        ->whereBetween('date', [$startDate, $endDate])
        ->groupBy(DB::raw('YEAR(date)'), DB::raw('MONTH(date)'))
        ->get();

    // Fetch the covered bookings per month (staffStatus = 3) for the last 4 months
    $coveredBookings = Booking::select(
            DB::raw('YEAR(date) as year'),
            DB::raw('MONTH(date) as month'),
            DB::raw('COUNT(*) as covered')
        )
        ->where('unitId', Auth::user()->portal_id)
        ->whereIn('unitStatus', [3, 4, 1])
        ->where('staffStatus', 3)
        ->whereBetween('date', [$startDate, $endDate])
        ->groupBy(DB::raw('YEAR(date)'), DB::raw('MONTH(date)'))
        ->get();

    // Merge the data and calculate the coverage percentage
    $coverageData = [];
    foreach ($totalBookings as $total) {
        $month = $total->month;
        $year = $total->year;
        $covered = $coveredBookings->firstWhere('month', $month)->covered ?? 0;
        $percentage = $total->total > 0 ? ($covered / $total->total) * 100 : 0;

        $coverageData[] = [
            'month' => Carbon::createFromDate($year, $month, 1)->format('F Y'),
            'total' => $total->total,
            'covered' => $covered,
            'percentage' => round($percentage, 2),
        ];
    }

    return response()->json([
        'bar' => $coverageData,
    ]);
}


}
