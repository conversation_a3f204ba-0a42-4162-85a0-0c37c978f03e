<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class AuditController extends Controller
{
    /**
     * Display the Script Budget Utilisation Log page
     */
    public function scriptBudgetLog()
    {
        return view('Audit.ScriptBudgetUtilisationLog');
    }

    /**
     * Get data for Script Budget Utilisation Log DataTable
     */
    public function getScriptBudgetLogData(Request $request)
    {
        try {
            $query = DB::table('weekly_budget_update_script_logs as logs')
                ->select([
                    'logs.id',
                    'logs.booking_id',
                    'logs.booking_details',
                    'logs.initial_value',
                    'logs.unit_final_cost',
                    'logs.status',
                    'logs.total_weekly_utilisation',
                    'logs.adjustment_type',
                    'logs.error_log',
                    'logs.processed_count',
                    'logs.last_processed_at',
                    'logs.created_at'
                ])
                ->orderBy('logs.created_at', 'desc');

            return DataTables::of($query)
                ->addColumn('unit_name_formatted', function ($row) {
                    if ($row->booking_details) {
                        $details = json_decode($row->booking_details, true);
                        return $details['unit_name'] ?? 'N/A';
                    }
                    return 'N/A';
                })
                ->addColumn('client_name_formatted', function ($row) {
                    return 'N/A'; // For now, until we can properly join
                })
                ->addColumn('week_year', function ($row) {
                    return 'N/A'; // For now, until we can properly join
                })
                ->addColumn('booking_details_formatted', function ($row) {
                    if ($row->booking_details) {
                        $details = json_decode($row->booking_details, true);
                        if (is_array($details)) {
                            $formatted = [];
                            if (isset($details['staff_name'])) $formatted[] = "Staff: {$details['staff_name']}";
                            if (isset($details['shift_name'])) $formatted[] = "Shift: {$details['shift_name']}";
                            if (isset($details['category_name'])) $formatted[] = "Category: {$details['category_name']}";
                            if (isset($details['unit_status'])) $formatted[] = "Unit Status: {$details['unit_status']}";
                            if (isset($details['staff_status'])) $formatted[] = "Staff Status: {$details['staff_status']}";
                            return implode('<br>', $formatted);
                        }
                    }
                    return 'N/A';
                })
                ->editColumn('initial_value', function ($row) {
                    return $row->initial_value ? '£' . number_format($row->initial_value, 2) : 'N/A';
                })
                ->editColumn('unit_final_cost', function ($row) {
                    return $row->unit_final_cost ? '£' . number_format($row->unit_final_cost, 2) : 'N/A';
                })
                ->editColumn('total_weekly_utilisation', function ($row) {
                    return $row->total_weekly_utilisation ? '£' . number_format($row->total_weekly_utilisation, 2) : 'N/A';
                })
                ->editColumn('status', function ($row) {
                    $badgeClass = match($row->status) {
                        'success' => 'badge-success',
                        'failed' => 'badge-danger',
                        'skipped' => 'badge-info',
                        'updated' => 'badge-warning',
                        default => 'badge-secondary'
                    };
                    return "<span class='badge {$badgeClass}'>" . ucfirst($row->status) . "</span>";
                })
                ->editColumn('adjustment_type', function ($row) {
                    $badgeClass = match($row->adjustment_type) {
                        'addition' => 'badge-success',
                        'deduction' => 'badge-danger',
                        'updation' => 'badge-warning',
                        default => 'badge-secondary'
                    };
                    return "<span class='badge {$badgeClass}'>" . ucfirst($row->adjustment_type ?? 'N/A') . "</span>";
                })
                ->editColumn('last_processed_at', function ($row) {
                    return $row->last_processed_at ? date('Y-m-d H:i:s', strtotime($row->last_processed_at)) : 'N/A';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at ? date('Y-m-d H:i:s', strtotime($row->created_at)) : 'N/A';
                })
                ->rawColumns(['booking_details_formatted', 'status', 'adjustment_type'])
                ->make(true);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Debug method to test data access
     */
    public function testData()
    {
        try {
            $count = DB::table('weekly_budget_update_script_logs')->count();
            $sample = DB::table('weekly_budget_update_script_logs')->limit(3)->get();
            
            return response()->json([
                'total_records' => $count,
                'sample_data' => $sample,
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'success' => false
            ]);
        }
    }
}
