#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import Docker from 'dockerode';
import * as fs from 'fs-extra';
import * as path from 'path';
import { glob } from 'glob';
import * as yaml from 'yaml';

class ClientPortalMCPServer {
  private server: Server;
  private docker: Docker;
  private projectRoot: string;

  constructor() {
    this.server = new Server({
      name: 'client-portal-mcp-server',
      version: '1.0.0',
    });
    
    this.docker = new Docker();
    this.projectRoot = path.resolve(__dirname, '../../..');
    
    this.setupHandlers();
  }

  private setupHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'get_docker_containers',
          description: 'Get information about Docker containers (running, stopped, all)',
          inputSchema: {
            type: 'object',
            properties: {
              status: {
                type: 'string',
                enum: ['running', 'stopped', 'all'],
                default: 'all',
                description: 'Filter containers by status'
              }
            }
          }
        },
        {
          name: 'get_docker_images',
          description: 'Get information about Docker images',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        },
        {
          name: 'get_docker_compose_info',
          description: 'Get Docker Compose configuration information',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        },
        {
          name: 'get_project_structure',
          description: 'Get the project directory structure and file organization',
          inputSchema: {
            type: 'object',
            properties: {
              depth: {
                type: 'number',
                default: 3,
                description: 'Maximum depth to traverse'
              },
              includeFiles: {
                type: 'boolean',
                default: true,
                description: 'Include files in the structure'
              }
            }
          }
        },
        {
          name: 'get_laravel_info',
          description: 'Get Laravel project information including routes, models, and configuration',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        },
        {
          name: 'get_database_schema',
          description: 'Get database schema information from Laravel migrations',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        },
        {
          name: 'get_env_config',
          description: 'Get environment configuration (sanitized - no secrets)',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        },
        {
          name: 'get_container_logs',
          description: 'Get logs from a specific Docker container',
          inputSchema: {
            type: 'object',
            properties: {
              containerName: {
                type: 'string',
                description: 'Name of the container to get logs from'
              },
              lines: {
                type: 'number',
                default: 100,
                description: 'Number of recent log lines to retrieve'
              }
            },
            required: ['containerName']
          }
        },
        {
          name: 'analyze_project_dependencies',
          description: 'Analyze project dependencies from composer.json and package.json',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        },
        {
          name: 'get_docker_network_info',
          description: 'Get Docker network information',
          inputSchema: {
            type: 'object',
            properties: {}
          }
        }
      ] as Tool[]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'get_docker_containers':
            return await this.getDockerContainers(args?.status as string || 'all');
          
          case 'get_docker_images':
            return await this.getDockerImages();
          
          case 'get_docker_compose_info':
            return await this.getDockerComposeInfo();
          
          case 'get_project_structure':
            return await this.getProjectStructure(args?.depth as number || 3, args?.includeFiles !== false);
          
          case 'get_laravel_info':
            return await this.getLaravelInfo();
          
          case 'get_database_schema':
            return await this.getDatabaseSchema();
          
          case 'get_env_config':
            return await this.getEnvConfig();
          
          case 'get_container_logs':
            return await this.getContainerLogs(args?.containerName as string, args?.lines as number || 100);
          
          case 'analyze_project_dependencies':
            return await this.analyzeProjectDependencies();
          
          case 'get_docker_network_info':
            return await this.getDockerNetworkInfo();
          
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error executing ${name}: ${error instanceof Error ? error.message : 'Unknown error'}`
            }
          ]
        };
      }
    });
  }

  private async getDockerContainers(status: string) {
    try {
      const containers = await this.docker.listContainers({ 
        all: status === 'all',
        filters: status !== 'all' ? { status: [status] } : undefined
      });

      const detailedContainers = await Promise.all(
        containers.map(async (container: any) => {
          const containerInstance = this.docker.getContainer(container.Id);
          const inspect = await containerInstance.inspect();
          
          return {
            id: container.Id.substring(0, 12),
            name: container.Names[0].replace('/', ''),
            image: container.Image,
            status: container.Status,
            state: container.State,
            ports: container.Ports,
            created: new Date(container.Created * 1000).toISOString(),
            mounts: inspect.Mounts,
            networkSettings: inspect.NetworkSettings,
            config: {
              env: inspect.Config.Env,
              cmd: inspect.Config.Cmd,
              workingDir: inspect.Config.WorkingDir
            }
          };
        })
      );

      return {
        content: [
          {
            type: 'text',
            text: `Docker Containers (${status}):\n\n${JSON.stringify(detailedContainers, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get Docker containers: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getDockerImages() {
    try {
      const images = await this.docker.listImages();
      
      const imageInfo = images.map((image: any) => ({
        id: image.Id.substring(7, 19),
        repoTags: image.RepoTags,
        size: Math.round(image.Size / 1024 / 1024) + ' MB',
        created: new Date(image.Created * 1000).toISOString(),
        containers: image.Containers
      }));

      return {
        content: [
          {
            type: 'text',
            text: `Docker Images:\n\n${JSON.stringify(imageInfo, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get Docker images: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getDockerComposeInfo() {
    try {
      const composePath = path.join(this.projectRoot, 'docker', 'compose.yml');
      
      if (!await fs.pathExists(composePath)) {
        throw new Error('Docker Compose file not found');
      }

      const composeContent = await fs.readFile(composePath, 'utf-8');
      const composeConfig = yaml.parse(composeContent);

      return {
        content: [
          {
            type: 'text',
            text: `Docker Compose Configuration:\n\n${JSON.stringify(composeConfig, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get Docker Compose info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getProjectStructure(depth: number, includeFiles: boolean) {
    try {
      const structure = await this.buildDirectoryTree(this.projectRoot, depth, includeFiles);
      
      return {
        content: [
          {
            type: 'text',
            text: `Project Structure (depth: ${depth}):\n\n${this.formatDirectoryTree(structure)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get project structure: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async buildDirectoryTree(dirPath: string, maxDepth: number, includeFiles: boolean, currentDepth = 0): Promise<any> {
    if (currentDepth >= maxDepth) return null;

    const items = await fs.readdir(dirPath);
    const result: any = {};

    for (const item of items) {
      if (item.startsWith('.') && !['env.example', '.gitignore'].includes(item)) continue;
      
      const itemPath = path.join(dirPath, item);
      const stats = await fs.stat(itemPath);

      if (stats.isDirectory()) {
        const subTree = await this.buildDirectoryTree(itemPath, maxDepth, includeFiles, currentDepth + 1);
        if (subTree) {
          result[item + '/'] = subTree;
        } else {
          result[item + '/'] = {};
        }
      } else if (includeFiles) {
        result[item] = {
          size: stats.size,
          modified: stats.mtime.toISOString()
        };
      }
    }

    return result;
  }

  private formatDirectoryTree(tree: any, indent = ''): string {
    let result = '';
    for (const [key, value] of Object.entries(tree)) {
      result += `${indent}${key}\n`;
      if (typeof value === 'object' && value !== null && !(value as any).size) {
        result += this.formatDirectoryTree(value, indent + '  ');
      }
    }
    return result;
  }

  private async getLaravelInfo() {
    try {
      const info: any = {};

      // Get composer.json info
      const composerPath = path.join(this.projectRoot, 'composer.json');
      if (await fs.pathExists(composerPath)) {
        info.composer = JSON.parse(await fs.readFile(composerPath, 'utf-8'));
      }

      // Get routes
      const routesPath = path.join(this.projectRoot, 'routes');
      if (await fs.pathExists(routesPath)) {
        const routeFiles = await fs.readdir(routesPath);
        info.routes = {};
        for (const file of routeFiles) {
          if (file.endsWith('.php')) {
            const content = await fs.readFile(path.join(routesPath, file), 'utf-8');
            info.routes[file] = this.extractRoutes(content);
          }
        }
      }

      // Get models
      const modelsPath = path.join(this.projectRoot, 'app', 'Models');
      if (await fs.pathExists(modelsPath)) {
        const modelFiles = await glob('*.php', { cwd: modelsPath });
        info.models = modelFiles.map(file => file.replace('.php', ''));
      }

      // Get controllers
      const controllersPath = path.join(this.projectRoot, 'app', 'Http', 'Controllers');
      if (await fs.pathExists(controllersPath)) {
        const controllerFiles = await glob('**/*.php', { cwd: controllersPath });
        info.controllers = controllerFiles.map(file => file.replace('.php', ''));
      }

      return {
        content: [
          {
            type: 'text',
            text: `Laravel Project Information:\n\n${JSON.stringify(info, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get Laravel info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private extractRoutes(content: string): string[] {
    const routes: string[] = [];
    const routeRegex = /Route::(get|post|put|patch|delete|any|match|resource|group)\s*\(\s*['"`]([^'"`]+)['"`]/g;
    let match;
    
    while ((match = routeRegex.exec(content)) !== null) {
      routes.push(`${match[1].toUpperCase()} ${match[2]}`);
    }
    
    return routes;
  }

  private async getDatabaseSchema() {
    try {
      const migrationsPath = path.join(this.projectRoot, 'database', 'migrations');
      
      if (!await fs.pathExists(migrationsPath)) {
        throw new Error('Migrations directory not found');
      }

      const migrationFiles = await glob('*.php', { cwd: migrationsPath });
      const schema: any = {};

      for (const file of migrationFiles) {
        const content = await fs.readFile(path.join(migrationsPath, file), 'utf-8');
        const tableName = this.extractTableName(content);
        if (tableName) {
          schema[tableName] = {
            migration: file,
            columns: this.extractColumns(content)
          };
        }
      }

      return {
        content: [
          {
            type: 'text',
            text: `Database Schema:\n\n${JSON.stringify(schema, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get database schema: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private extractTableName(content: string): string | null {
    const createMatch = content.match(/Schema::create\s*\(\s*['"`]([^'"`]+)['"`]/);
    const tableMatch = content.match(/Schema::table\s*\(\s*['"`]([^'"`]+)['"`]/);
    return createMatch?.[1] || tableMatch?.[1] || null;
  }

  private extractColumns(content: string): string[] {
    const columns: string[] = [];
    const columnRegex = /\$table->(\w+)\s*\(\s*['"`]?([^'"`\),]+)['"`]?/g;
    let match;
    
    while ((match = columnRegex.exec(content)) !== null) {
      columns.push(`${match[2]}: ${match[1]}`);
    }
    
    return columns;
  }

  private async getEnvConfig() {
    try {
      const envExamplePath = path.join(this.projectRoot, '.env.example');
      
      if (!await fs.pathExists(envExamplePath)) {
        throw new Error('.env.example file not found');
      }

      const envContent = await fs.readFile(envExamplePath, 'utf-8');
      const config: any = {};
      
      envContent.split('\n').forEach(line => {
        const [key, value] = line.split('=');
        if (key && !key.startsWith('#')) {
          // Sanitize sensitive values
          const sanitizedValue = this.sanitizeEnvValue(key.trim(), value?.trim() || '');
          config[key.trim()] = sanitizedValue;
        }
      });

      return {
        content: [
          {
            type: 'text',
            text: `Environment Configuration (sanitized):\n\n${JSON.stringify(config, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get env config: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private sanitizeEnvValue(key: string, value: string): string {
    const sensitiveKeys = ['PASSWORD', 'KEY', 'SECRET', 'TOKEN', 'API'];
    const isSensitive = sensitiveKeys.some(sensitive => key.toUpperCase().includes(sensitive));
    
    if (isSensitive && value && value !== '') {
      return '[REDACTED]';
    }
    
    return value;
  }

  private async getContainerLogs(containerName: string, lines: number) {
    try {
      const container = this.docker.getContainer(containerName);
      const logs = await container.logs({
        stdout: true,
        stderr: true,
        tail: lines,
        timestamps: true
      });

      return {
        content: [
          {
            type: 'text',
            text: `Logs for container '${containerName}' (last ${lines} lines):\n\n${logs.toString()}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get container logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async analyzeProjectDependencies() {
    try {
      const analysis: any = {};

      // Analyze composer.json
      const composerPath = path.join(this.projectRoot, 'composer.json');
      if (await fs.pathExists(composerPath)) {
        const composer = JSON.parse(await fs.readFile(composerPath, 'utf-8'));
        analysis.php = {
          require: composer.require || {},
          requireDev: composer['require-dev'] || {},
          autoload: composer.autoload || {}
        };
      }

      // Analyze package.json
      const packagePath = path.join(this.projectRoot, 'package.json');
      if (await fs.pathExists(packagePath)) {
        const packageJson = JSON.parse(await fs.readFile(packagePath, 'utf-8'));
        analysis.node = {
          dependencies: packageJson.dependencies || {},
          devDependencies: packageJson.devDependencies || {},
          scripts: packageJson.scripts || {}
        };
      }

      return {
        content: [
          {
            type: 'text',
            text: `Project Dependencies Analysis:\n\n${JSON.stringify(analysis, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to analyze dependencies: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getDockerNetworkInfo() {
    try {
      const networks = await this.docker.listNetworks();
      
      const networkInfo = networks.map((network: any) => ({
        id: network.Id.substring(0, 12),
        name: network.Name,
        driver: network.Driver,
        scope: network.Scope,
        containers: network.Containers || {},
        created: network.Created,
        config: network.IPAM
      }));

      return {
        content: [
          {
            type: 'text',
            text: `Docker Networks:\n\n${JSON.stringify(networkInfo, null, 2)}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`Failed to get Docker network info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Client Portal MCP Server running on stdio');
  }
}

const server = new ClientPortalMCPServer();
server.run().catch(console.error);
