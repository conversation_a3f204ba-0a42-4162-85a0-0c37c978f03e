{"name": "client-portal-mcp-server", "version": "1.0.0", "description": "MCP server providing Docker and project information for client-unit-portal", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc --watch & nodemon dist/index.js", "clean": "rm -rf dist"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "dockerode": "^4.0.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "yaml": "^2.3.4"}, "devDependencies": {"@types/dockerode": "^3.3.42", "@types/fs-extra": "^11.0.4", "@types/node": "^20.11.0", "nodemon": "^3.0.3", "typescript": "^5.3.3"}, "keywords": ["mcp", "docker", "laravel", "server"], "author": "Your Name", "license": "MIT"}