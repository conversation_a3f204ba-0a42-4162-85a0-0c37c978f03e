#!/bin/bash

# Setup script for Client Portal MCP Server

echo "🚀 Setting up Client Portal MCP Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js (v18 or higher) first."
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm found: $(npm --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Build the project
echo "🔨 Building the project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build the project"
    exit 1
fi

echo "✅ Project built successfully"

# Check if Docker is running (optional)
if command -v docker &> /dev/null; then
    if docker info &> /dev/null; then
        echo "✅ Docker is running - Docker features will be available"
    else
        echo "⚠️  Docker is installed but not running - Docker features will be limited"
    fi
else
    echo "⚠️  Docker not found - Docker features will be unavailable"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Install the MCP Client extension in VS Code"
echo "2. Add the following to your VS Code settings.json:"
echo ""
echo "{"
echo "  \"mcp.servers\": {"
echo "    \"client-portal\": {"
echo "      \"command\": \"node\","
echo "      \"args\": [\"dist/index.js\"],"
echo "      \"cwd\": \"$(pwd)\","
echo "      \"env\": {"
echo "        \"NODE_ENV\": \"development\""
echo "      }"
echo "    }"
echo "  }"
echo "}"
echo ""
echo "3. Restart VS Code"
echo "4. Your MCP server will be available with comprehensive Docker and project information!"
echo ""
echo "For more details, see README.md"
