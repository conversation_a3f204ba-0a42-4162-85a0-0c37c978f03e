<?php

require_once __DIR__ . '/vendor/autoload.php';

// Initialize Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing Updated Budget Command with Reset Functionality...\n\n";

try {
    // Check current database state
    echo "=== CURRENT DATABASE STATE ===\n";
    
    $totalBookings = \App\Models\Booking::count();
    $bookingsWithInitialCost = \App\Models\Booking::whereNotNull('initial_cost')->count();
    $bookingsProcessed = \App\Models\Booking::where('budget_utilisation_updated', true)->count();
    $totalLogs = \Illuminate\Support\Facades\DB::table('weekly_utilised_fund_logs')->count();
    $totalWeeklyBudgets = \Illuminate\Support\Facades\DB::table('client_unit_weekly_budgets')->count();
    $weeklyUtilisationSum = \Illuminate\Support\Facades\DB::table('client_unit_weekly_budgets')
        ->sum('total_weekly_utilisation');
    
    echo "📊 Current Statistics:\n";
    echo "  - Total bookings: {$totalBookings}\n";
    echo "  - Bookings with initial_cost: {$bookingsWithInitialCost}\n";
    echo "  - Bookings processed for budget: {$bookingsProcessed}\n";
    echo "  - Weekly utilised fund logs: {$totalLogs}\n";
    echo "  - Weekly budgets: {$totalWeeklyBudgets}\n";
    echo "  - Total weekly utilisation: £" . number_format($weeklyUtilisationSum, 2) . "\n\n";
    
    // Check if command exists
    $artisan = app('Illuminate\Contracts\Console\Kernel');
    $commands = $artisan->all();
    
    if (isset($commands['budget:update-costs'])) {
        echo "✅ Command 'budget:update-costs' is registered\n";
    } else {
        echo "❌ Command 'budget:update-costs' is NOT registered\n";
        exit(1);
    }
    
    // Check required database columns
    echo "\n=== DATABASE STRUCTURE CHECK ===\n";
    
    $booking = \App\Models\Booking::first();
    if ($booking) {
        $fillable = $booking->getFillable();
        
        if (in_array('initial_cost', $fillable)) {
            echo "✅ Column 'initial_cost' is in fillable array\n";
        } else {
            echo "❌ Column 'initial_cost' is NOT in fillable array\n";
        }
        
        if (in_array('budget_utilisation_updated', $fillable)) {
            echo "✅ Column 'budget_utilisation_updated' is in fillable array\n";
        } else {
            echo "❌ Column 'budget_utilisation_updated' is NOT in fillable array\n";
        }
    }
    
    // Check helper function
    if (function_exists('updateWeeklyUtilisedFund')) {
        echo "✅ Helper function 'updateWeeklyUtilisedFund' is available\n";
    } else {
        echo "❌ Helper function 'updateWeeklyUtilisedFund' is NOT available\n";
    }
    
    // Check required tables
    echo "\n=== TABLE STRUCTURE CHECK ===\n";
    
    $tables = [
        'bookings',
        'client_unit_weekly_budgets', 
        'weekly_utilised_fund_logs',
        'client_unit_annual_budgets'
    ];
    
    foreach ($tables as $table) {
        try {
            $count = \Illuminate\Support\Facades\DB::table($table)->count();
            echo "✅ Table '{$table}' exists with {$count} records\n";
        } catch (Exception $e) {
            echo "❌ Table '{$table}' does NOT exist or is inaccessible\n";
        }
    }
    
    echo "\n=== COMMAND FEATURES ===\n";
    echo "🧹 NEW: Step 0 - Reset weekly utilisation data\n";
    echo "   - Truncates weekly_utilised_fund_logs table\n";
    echo "   - Resets all weekly budget utilisation to 0\n";
    echo "   - Triggers recalculation for all annual budgets\n";
    echo "   - Resets all booking budget flags\n\n";
    
    echo "💰 Step 1 - Update initial costs for all bookings\n";
    echo "🔄 Step 2 - Recalculate weekly budget utilisation\n\n";
    
    echo "📋 READY TO RUN!\n";
    echo "\n🔧 Usage Examples:\n";
    echo "  🧪 Dry run: docker exec -it clientportal-php php artisan budget:update-costs --dry-run\n";
    echo "  🚀 Live run: docker exec -it clientportal-php php artisan budget:update-costs\n";
    echo "  ⚙️  Small batch: docker exec -it clientportal-php php artisan budget:update-costs --batch-size=50\n\n";
    
    echo "⚠️  IMPORTANT WARNINGS:\n";
    echo "  🗑️  Step 0 will COMPLETELY CLEAR weekly_utilised_fund_logs table\n";
    echo "  🔄 Step 0 will RESET ALL weekly budget utilisation values to 0\n";
    echo "  💾 ALWAYS backup database before running in production!\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nTest completed successfully! ✅\n";
