<!DOCTYPE html>
<html>
<head>
    <title>DataTable Test</title>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap4.min.css">
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h3>DataTable Test</h3>
        <table id="testTable" class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Status</th>
                </tr>
            </thead>
        </table>
    </div>

    <script>
    $(document).ready(function() {
        console.log('jQuery loaded:', !!$);
        console.log('DataTables loaded:', !!$.fn.DataTable);
        
        $('#testTable').DataTable({
            data: [
                [1, 'Test 1', 'Active'],
                [2, 'Test 2', 'Inactive'],
                [3, 'Test 3', 'Active']
            ],
            columns: [
                { title: "ID" },
                { title: "Name" },
                { title: "Status" }
            ]
        });
    });
    </script>
</body>
</html>
