<?php

require_once __DIR__ . '/vendor/autoload.php';

// Initialize Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing Budget Update Command Setup...\n\n";

try {
    // Check if the command is registered
    $commands = app('Illuminate\Contracts\Console\Kernel')->all();
    
    if (isset($commands['budget:update-costs'])) {
        echo "✅ Command 'budget:update-costs' is successfully registered\n";
    } else {
        echo "❌ Command 'budget:update-costs' is NOT registered\n";
        echo "Available commands containing 'budget':\n";
        foreach ($commands as $name => $command) {
            if (strpos($name, 'budget') !== false) {
                echo "  - {$name}\n";
            }
        }
    }
    
    // Check if required database columns exist
    echo "\nChecking database structure...\n";
    
    // Check if the migrations have been run
    $booking = \App\Models\Booking::first();
    if ($booking) {
        $attributes = $booking->getAttributes();
        
        if (array_key_exists('initial_cost', $attributes)) {
            echo "✅ Column 'initial_cost' exists in bookings table\n";
        } else {
            echo "❌ Column 'initial_cost' does NOT exist in bookings table\n";
        }
        
        if (array_key_exists('budget_utilisation_updated', $attributes)) {
            echo "✅ Column 'budget_utilisation_updated' exists in bookings table\n";
        } else {
            echo "❌ Column 'budget_utilisation_updated' does NOT exist in bookings table\n";
        }
        
        echo "✅ Found sample booking ID: {$booking->bookingId}\n";
    } else {
        echo "⚠️  No bookings found in database\n";
    }
    
    // Check if helper function is available
    if (function_exists('updateWeeklyUtilisedFund')) {
        echo "✅ Helper function 'updateWeeklyUtilisedFund' is available\n";
    } else {
        echo "❌ Helper function 'updateWeeklyUtilisedFund' is NOT available\n";
    }
    
    echo "\n📋 Ready to run the command!\n";
    echo "\nUsage examples:\n";
    echo "  🧪 Dry run: php artisan budget:update-costs --dry-run\n";
    echo "  🚀 Live run: php artisan budget:update-costs\n";
    echo "  ⚙️  Custom batch: php artisan budget:update-costs --batch-size=50\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nTest completed successfully! ✅\n";
