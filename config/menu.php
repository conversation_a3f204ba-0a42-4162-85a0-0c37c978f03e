<?php

return [

    [ // Home
        'key' => 'home',
        'name' => 'Home',
        'icon' => 'fa-home',
        'permission' => 'client_portal_dashboard_dashboard_view',
        'route' => null,
        'order' => 1,
        'children' => [
            [
                'key' => 'home.dashboard',
                'name' => 'Dashboard',
                'permission' => 'client_portal_dashboard_dashboard_view',
                'route' => 'client.portal.dashboard',
                'order' => 1,
            ],
            // [
            //     'key' => 'home.alert',
            //     'name' => 'Alert',
            //     'permission' => 'client_portal_dashboard_dashboard_view',
            //     'route' => '#',
            //     'order' => 2,
            // ],
        ],
    ],

    [ // Bookings
        'key' => 'bookings',
        'name' => 'Bookings',
        'icon' => 'fa-calendar',
        'permission' => null,
        'route' => null,
        'order' => 2,
        'children' => [
            [
                'key' => 'bookings.new_shifts',
                'name' => 'New Shifts',
                'permission' => 'client_portal_invoice_view_invoice',
                'route' => 'bookings',
                'order' => 1,
            ],
            [
                'key' => 'bookings.all_shifts',
                'name' => 'All Shifts',
                'permission' => 'client_portal_invoice_view_invoice',
                'route' => 'booked.shifts',
                'order' => 2,
            ],
            [
                'key' => 'bookings.to_approve',
                'name' => 'Shifts to Approve',
                'permission' => 'client_portal_invoice_view_invoice',
                'route' => 'client.bookings',
                //'badge' => 12,
                'order' => 3,
            ],
            [
                'key' => 'bookings.archived',
                'name' => 'Archived Shifts',
                'permission' => 'client_portal_invoice_view_invoice',
                'route' => 'client.archived-booking',
                'order' => 4,
            ],
        ],
    ],

    [ // Budget
        'key' => 'budget',
        'name' => 'Budget',
        'icon' => 'fa-pie-chart',
        'permission' => 'client_portal_budget_view_budget_details',
        'route' => null,
        'order' => 3,
        'children' => [
            [
                'key' => 'budget.allocation',
                'name' => 'Budget Allocation',
                'permission' => 'client_portal_budget_view_budget_details',
                'route' => 'budget.allocation',
                'order' => 1,
            ],
            [
                'key' => 'budget.view',
                'name' => 'Budget View',
                'permission' => 'client_portal_budget_view_budget_details',
                'route' => 'budget.view',
                'order' => 2,
            ],
        ],
    ],

    [ // Timesheets
        'key' => 'timesheets',
        'name' => 'Timesheets',
        'icon' => 'fa-clock-o',
        'permission' => null,
        'route' => null,
        'order' => 4,
        'children' => [
            [
                'key' => 'timesheets.to_approve',
                'name' => 'TS for Approval',
                'permission' => null,
                'route' => 'ts.approvals',
                'order' => 1,
            ],
            [
                'key' => 'timesheets.approved',
                'name' => 'TS Approved',
                'permission' => null,
                'route' => 'ts.approved',
                'order' => 2,
            ],
        ],
    ],

    [ // Invoices
        'key' => 'invoices',
        'name' => 'Invoices',
        'icon' => 'fa-file-text-o',
        'permission' => 'client_portal_invoice_view_invoice',
        'route' => null,
        'order' => 5,
        'children' => [
            [
                'key' => 'invoice.list',
                'name' => 'Invoice for Approval',
                'permission' => 'client_portal_invoice_view_invoice',
                'route' => 'invoice.list',
                'order' => 1,
            ],
            [
                'key' => 'invoices.approved',
                'name' => 'Invoice Approved',
                'permission' => 'client_portal_invoice_view_invoice',
                'route' => 'unit.invoices',
                'order' => 2,
            ],
            [
                'key' => 'invoices.archived',
                'name' => 'Archived Invoice',
                'permission' => null,
                'route' => '#',
                'order' => 3,
            ],
        ],
    ],

    [ // Staff Compliance
        'key' => 'staff_compliance',
        'name' => 'Staff Compliance',
        'icon' => 'fa-user',
        'permission' => 'client_portal_dashboard_dashboard_view',
        'route' => null,
        'order' => 6,
        'children' => [
            [
                'key' => 'staff_compliance.index',
                'name' => 'Staff Compliance',
                'permission' => 'client_portal_dashboard_dashboard_view',
                'route' => 'staff.complaince',
                'order' => 1,
            ],
        ],
    ],

    [ // Reports
        'key' => 'reports',
        'name' => 'Reports',
        'icon' => 'fa-line-chart',
        'permission' => 'client_portal_dashboard_dashboard_view',
        'route' => null,
        'order' => 7,
        'children' => [
            [
                'key' => 'reports.management',
                'name' => 'Management Reports',
                'permission' => 'client_portal_dashboard_dashboard_view',
                'route' => 'management.reports',
                'order' => 1,
            ],
        ],
    ],

    [ // Incident Log
        'key' => 'incident_log',
        'name' => 'Incident Log',
        'icon' => 'fa-warning',
        'permission' => 'client_portal_dashboard_dashboard_view',
        'route' => null,
        'order' => 8,
        'children' => [
            [
                'key' => 'incident_log.index',
                'name' => 'Incident Log',
                'permission' => 'client_portal_dashboard_dashboard_view',
                'route' => 'incident.log',
                'order' => 1,
            ],
        ],
    ],

    [ // Configuration
        'key' => 'configuration',
        'name' => 'Configuration',
        'icon' => 'fa-cogs',
        'permission' => null,
        'route' => null,
        'order' => 11,
        'children' => [
            [
                'key' => 'config.user_management',
                'name' => 'User Management',
                'permission' => null,
                'route' => null,
                'order' => 1,
                'children' => [
                    [
                        'key' => 'config.user_management.users',
                        'name' => 'Users',
                        'permission' => 'client_portal_user_management_view_user|unit_portal_user_management_view_user',
                        'route' => 'users.index',
                        'order' => 1,
                    ],
                    [
                        'key' => 'config.user_management.roles',
                        'name' => 'Roles',
                        'permission' => 'client_portal_user_management_view_role|unit_portal_user_management_view_role',
                        'route' => 'users.roles',
                        'order' => 2,
                    ],
                ],
            ],
            [
                'key' => 'config.units',
                'name' => 'Units',
                'permission' => 'client_portal_units_units_view',
                'route' => 'unit.view',
                'order' => 2,
            ],
            [
                'key' => 'config.workflows',
                'name' => 'Workflows',
                'permission' => 'client_portal_workflow_view_workflow|unit_portal_workflow_view_workflow',
                'route' => 'workflows.index',
                'order' => 3,
            ],
        ],
    ],

    [ // Audit
        'key' => 'audit',
        'name' => 'Audit',
        'icon' => 'fa-search',
        'permission' => null,
        'route' => null,
        'order' => 10,
        'children' => [
            [
                'key' => 'audit.script_budget_utilisation_log',
                'name' => 'Script Budget Utilisation Log',
                'permission' => null,
                'route' => 'audit.script-budget-log',
                'order' => 1,
            ],
        ],
    ],

];
