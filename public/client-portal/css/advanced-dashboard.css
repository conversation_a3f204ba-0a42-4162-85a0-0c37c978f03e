/* Advanced Dashboard Styles */

/* Enhanced Filter Container */
.dashboard-filters-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
    flex-wrap: wrap;
}

.dashboard-filters-container select {
    min-width: 140px;
    height: 38px;
    border-radius: 6px !important;
    border: 1px solid #ddd !important;
    background: white;
    font-size: 13px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-filters-container select:focus {
    border-color: #1d75bd !important;
    box-shadow: 0 0 0 2px rgba(29, 117, 189, 0.2);
    outline: none;
}

.dashboard-filters-container select:hover {
    border-color: #1d75bd !important;
}

/* Enhanced Budget Cards */
.BudgDashBox {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
}

.BudgDashBox::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1d75bd, #4ECDC4);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.BudgDashBox:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
}

.BudgDashBox:hover::before {
    opacity: 1;
}

.ClitBdBg {
    padding: 25px 20px;
    position: relative;
}

/* Enhanced Amount Display */
.amt {
    font-size: 2.2rem !important;
    font-weight: 700 !important;
    margin-bottom: 8px;
    position: relative;
    animation: countUp 0.8s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Title Styling */
.title {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #666 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.subTitle {
    font-size: 12px !important;
    color: #999 !important;
    font-weight: 500;
}

/* Enhanced Card Icons */
.card-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    transition: all 0.3s ease;
    z-index: 1;
}

.BudgDashBox:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
    opacity: 0.6 !important;
}

.card-icon i {
    transition: all 0.3s ease;
}

/* Progress Indicators */
.progress-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Counter Animation */
.amt span[data-counter] {
    display: inline-block;
    transition: all 0.3s ease;
}

/* Enhanced Card Hover Effects */
.BudgDashBox:hover .amt {
    transform: scale(1.05);
}

.BudgDashBox:hover .title {
    color: #333 !important;
}

/* Pulsing Animation for Important Cards */
.budget-card:hover,
.expenses-card:hover {
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }
    50% {
        box-shadow: 0 8px 30px rgba(29, 117, 189, 0.2);
    }
}

/* Enhanced Chart Containers */
.shadowWidget {
    background: white;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    border: none !important;
    transition: all 0.3s ease;
    overflow: hidden;
}

.shadowWidget:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

/* Enhanced Chart Titles */
.BudTitle {
    font-size: 18px !important;
    font-weight: 600 !important;
    margin-bottom: 20px !important;
    position: relative;
    padding-left: 15px;
}

.BudTitle::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    border-radius: 2px;
}

.greenFont.BudTitle::before {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.redFont.BudTitle::before {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
}

/* Enhanced Tables */
.table {
    margin-bottom: 0 !important;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #666 !important;
    padding: 15px 12px !important;
}

.table tbody td {
    border: none !important;
    border-bottom: 1px solid #f1f3f4 !important;
    padding: 12px !important;
    font-size: 13px;
    transition: background-color 0.2s ease;
}

.table tbody tr:hover td {
    background-color: rgba(29, 117, 189, 0.05) !important;
}

/* Loading States */
.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #666;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 0.2em;
    border-color: #1d75bd;
    border-right-color: transparent;
    margin-bottom: 15px;
}

/* Enhanced Smart View */
.GpBoxHeading h4 {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-filters-container {
        justify-content: center;
        margin-top: 15px;
    }
    
    .dashboard-filters-container select {
        min-width: 120px;
        margin-bottom: 10px;
    }
    
    .BudgDashBox {
        margin-bottom: 15px;
    }
    
    .amt {
        font-size: 1.8rem !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Progress Indicators */
.progress-ring {
    width: 60px;
    height: 60px;
    position: absolute;
    top: 15px;
    right: 15px;
    opacity: 0.3;
}

.progress-ring circle {
    fill: none;
    stroke-width: 4;
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
    transition: stroke-dasharray 0.5s ease;
}

/* Color Enhancements */
.greenFont {
    color: #28a745 !important;
}

.redFont {
    color: #dc3545 !important;
}

.balance-amount-color.greenFont {
    color: #28a745 !important;
}

.balance-amount-color.redFont {
    color: #dc3545 !important;
}

/* Filter Enhancement */
.FlitSelect {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 8px center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 12px !important;
    padding-right: 32px !important;
}

/* Enhanced Content Header */
.content-header {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.headTitleBtm {
    align-items: center;
}

/* Dashboard Title Enhancement */
.GpBoxHeading h4 i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
