/**
 * Advanced Dashboard Filters
 * Handles all filter functionality for the advanced dashboard
 */

var AdvancedDashboardFilters = {
    config: {
        clientId: null,
        currentFilter: 1,
        currentUnit: null,
        currentFinancialYear: null,
        selectors: {
            monthFilter: '#monthselect',
            unitFilter: '#unitselect',
            financialYearFilter: '#financialyearselect'
        }
    },

    /**
     * Initialize the filters
     */
    init: function(options = {}) {
        this.config = Object.assign(this.config, options);
        this.bindEvents();
        this.loadInitialData();
    },

    /**
     * Bind filter events
     */
    bindEvents: function() {
        var self = this;

        // Month filter change
        $(this.config.selectors.monthFilter).on('change', function() {
            self.config.currentFilter = $(this).val();
            self.refreshAllComponents();
        });

        // Unit filter change
        $(this.config.selectors.unitFilter).on('change', function() {
            self.config.currentUnit = $(this).val() || null;
            self.refreshAllComponents();
        });

        // Financial year filter change
        $(this.config.selectors.financialYearFilter).on('change', function() {
            self.config.currentFinancialYear = $(this).val();
            self.refreshAllComponents();
        });
    },

    /**
     * Load initial data
     */
    loadInitialData: function() {
        this.refreshAllComponents();
    },

    /**
     * Refresh all dashboard components
     */
    refreshAllComponents: function() {
        var params = this.getFilterParams();
        
        // Show loading state
        this.showLoadingState();

        // Refresh all components
        if (typeof BudgetSummaryCards !== 'undefined') {
            BudgetSummaryCards.refresh(params);
        }

        if (typeof BudgetDonutChart !== 'undefined') {
            BudgetDonutChart.refresh(params);
        }

        if (typeof ExpensesDonutChart !== 'undefined') {
            ExpensesDonutChart.refresh(params);
        }

        if (typeof SmartViewChart !== 'undefined') {
            SmartViewChart.refresh(params);
        }

        if (typeof UnitExpenseOverview !== 'undefined') {
            UnitExpenseOverview.refresh(params);
        }

        if (typeof BookingReasons !== 'undefined') {
            BookingReasons.refresh(params);
        }

        // Hide loading state after a delay
        setTimeout(function() {
            AdvancedDashboardFilters.hideLoadingState();
        }, 1000);
    },

    /**
     * Get current filter parameters
     */
    getFilterParams: function() {
        return {
            client: this.config.clientId,
            filter: this.config.currentFilter,
            unit_id: this.config.currentUnit,
            financial_year: this.config.currentFinancialYear
        };
    },

    /**
     * Show loading state
     */
    showLoadingState: function() {
        // Add loading overlay to dashboard
        if ($('.dashboard-loading-overlay').length === 0) {
            $('body').append(
                '<div class="dashboard-loading-overlay">' +
                    '<div class="loading-content">' +
                        '<div class="spinner-border text-primary" role="status">' +
                            '<span class="sr-only">Loading...</span>' +
                        '</div>' +
                        '<p class="mt-3">Updating dashboard...</p>' +
                    '</div>' +
                '</div>'
            );
        }
        $('.dashboard-loading-overlay').fadeIn(300);
    },

    /**
     * Hide loading state
     */
    hideLoadingState: function() {
        $('.dashboard-loading-overlay').fadeOut(300);
    },

    /**
     * Update filter display
     */
    updateFilterDisplay: function() {
        var filterName = this.getFilterName(this.config.currentFilter);
        $('.filter-text').text(filterName);
    },

    /**
     * Get filter name for display
     */
    getFilterName: function(filter) {
        switch (parseInt(filter)) {
            case 2:
                return 'Last Month';
            case 4:
                return 'Next Month';
            default:
                return 'This Month';
        }
    },

    /**
     * Reset filters to default
     */
    resetFilters: function() {
        $(this.config.selectors.monthFilter).val(1);
        $(this.config.selectors.unitFilter).val('');
        $(this.config.selectors.financialYearFilter).val($(this.config.selectors.financialYearFilter + ' option:first').val());
        
        this.config.currentFilter = 1;
        this.config.currentUnit = null;
        this.config.currentFinancialYear = $(this.config.selectors.financialYearFilter + ' option:first').val();
        
        this.refreshAllComponents();
    },

    /**
     * Export current data
     */
    exportData: function(format = 'csv') {
        var params = this.getFilterParams();
        params.export_format = format;
        
        // Create download link
        var url = '/api/client-portal/advanced-dashboard/export?' + $.param(params);
        window.open(url, '_blank');
    },

    /**
     * Get current filter summary
     */
    getFilterSummary: function() {
        var summary = {
            period: this.getFilterName(this.config.currentFilter),
            unit: this.config.currentUnit ? $(this.config.selectors.unitFilter + ' option:selected').text() : 'All Units',
            financialYear: this.config.currentFinancialYear
        };
        
        return summary;
    }
};

// CSS for loading overlay
$(document).ready(function() {
    if ($('#advanced-dashboard-loading-styles').length === 0) {
        $('head').append(`
            <style id="advanced-dashboard-loading-styles">
                .dashboard-loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.9);
                    z-index: 9999;
                    display: none;
                }
                
                .loading-content {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    color: #666;
                }
                
                .loading-content .spinner-border {
                    width: 3rem;
                    height: 3rem;
                    border-width: 0.3em;
                }
                
                .loading-content p {
                    font-size: 16px;
                    font-weight: 500;
                    margin: 0;
                }
            </style>
        `);
    }
});

// Auto-initialize when DOM is ready
$(document).ready(function() {
    if (typeof clientId !== 'undefined') {
        AdvancedDashboardFilters.init({
            clientId: clientId
        });
    }
});
