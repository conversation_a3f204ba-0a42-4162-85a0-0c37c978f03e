/**
 * Expenses Donut Chart Component
 * Handles the expenses donut chart and data table
 */

var ExpensesDonutChart = {
    config: {
        clientId: null,
        permissions: {},
        chartContainer: '#nextMonthExpensePie',
        tableContainer: '#expensesTable',
        chart: null,
        apiUrl: null
    },

    /**
     * Initialize the expenses donut chart component
     */
    init: function(options = {}) {
        this.config = Object.assign(this.config, options);
        // Use a different API endpoint for expenses if available
        this.config.apiUrl = $(this.config.chartContainer).data('fetch') || 
                           DashboardComponents.config.apiBaseUrl + '/client-portal/get-dashboard-expense-donut-graph';
        this.loadData();
    },

    /**
     * Load expenses chart data
     */
    loadData: function(params = {}) {
        if (!this.config.apiUrl) return;

        var self = this;
        this.showChartLoading();

        var requestData = Object.assign({
            client: this.config.clientId,
            year: $(this.config.chartContainer).data('year') || new Date().getFullYear(),
            month: $(this.config.chartContainer).data('month') || (new Date().getMonth() + 1),
            filter: DashboardComponents.config.currentFilter
        }, params);

        DashboardComponents.makeRequest(this.config.apiUrl, requestData)
            .done(function(response) {
                self.renderChart(response);
                self.renderTable(response);
                self.hideChartLoading();
                DashboardComponents.hideError('#expenses-donut-component');
            })
            .fail(function(xhr, status, error) {
                console.error('Failed to load expenses chart:', error);
                self.hideChartLoading();
                DashboardComponents.showError('#expenses-donut-component', 'Failed to load expenses chart');
            });
    },

    /**
     * Render the donut chart
     */
    renderChart: function(data) {
        if (!(this.config.permissions.view_chart ?? true)) return;

        var chartData = this.prepareChartData(data);
        var totalExpenses = this.calculateTotal(chartData);

        // Destroy existing chart
        if (this.config.chart) {
            this.config.chart.destroy();
        }

        // Create new chart
        this.config.chart = Highcharts.chart(this.config.chartContainer.replace('#', ''), {
            chart: {
                type: 'pie',
                height: 300
            },
            title: {
                text: null
            },
            tooltip: {
                pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>Amount: <b>£{point.y:.2f}</b>'
            },
            accessibility: {
                point: {
                    valueSuffix: '%'
                }
            },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: false
                    },
                    showInLegend: false,
                    innerSize: '60%',
                    colors: ['#dc3545', '#fd7e14', '#ffc107', '#e83e8c', '#6f42c1']
                }
            },
            series: [{
                name: 'Expenses',
                colorByPoint: true,
                data: chartData
            }]
        });

        // Update inner summary
        this.updateInnerSummary(totalExpenses);
    },

    /**
     * Prepare chart data from API response
     */
    prepareChartData: function(data) {
        var chartData = [];
        
        if (data.units && Array.isArray(data.units)) {
            data.units.forEach(function(unit) {
                chartData.push({
                    name: unit.name || 'Unknown Unit',
                    y: parseFloat(unit.expense || unit.expenses || 0)
                });
            });
        } else if (data.expenses && Array.isArray(data.expenses)) {
            data.expenses.forEach(function(expense) {
                chartData.push({
                    name: expense.name || expense.unit_name || 'Unknown Unit',
                    y: parseFloat(expense.amount || expense.expense || 0)
                });
            });
        }

        return chartData;
    },

    /**
     * Calculate total from chart data
     */
    calculateTotal: function(chartData) {
        return chartData.reduce(function(total, item) {
            return total + item.y;
        }, 0);
    },

    /**
     * Update inner summary display
     */
    updateInnerSummary: function(total) {
        $('.graphInnerSUm.redFont').html('<div class="inner-total">£' + total.toFixed(2) + '</div>');
    },

    /**
     * Render the data table
     */
    renderTable: function(data) {
        if (!(this.config.permissions.view_data_table ?? true)) return;

        var $tbody = $(this.config.tableContainer + ' tbody');
        $tbody.empty();

        var units = data.units || data.expenses || [];
        
        if (Array.isArray(units) && units.length > 0) {
            var total = this.calculateTotalFromUnits(units);
            
            units.forEach(function(unit) {
                var expenseAmount = parseFloat(unit.expense || unit.expenses || unit.amount || 0);
                var percentage = total > 0 ? ((expenseAmount / total) * 100).toFixed(1) : 0;
                var row = '<tr>' +
                    '<td>' + (unit.name || unit.unit_name || 'Unknown Unit') + '</td>' +
                    '<td style="text-align: end;">£' + expenseAmount.toFixed(2) + '</td>' +
                    '<td style="text-align: end;">' + percentage + '%</td>' +
                    '<td></td>' +
                    '</tr>';
                $tbody.append(row);
            });
        } else {
            $tbody.append('<tr><td colspan="4" class="text-center">No data available</td></tr>');
        }
    },

    /**
     * Calculate total from units data
     */
    calculateTotalFromUnits: function(units) {
        return units.reduce(function(total, unit) {
            return total + parseFloat(unit.expense || unit.expenses || unit.amount || 0);
        }, 0);
    },

    /**
     * Show chart loading state
     */
    showChartLoading: function() {
        $(this.config.chartContainer).find('.chart-loading').show();
        $(this.config.tableContainer + ' tbody').html(
            '<tr class="loading-row">' +
            '<td colspan="4" class="text-center">' +
            '<div class="spinner-border spinner-border-sm" role="status">' +
            '<span class="sr-only">Loading...</span>' +
            '</div> Loading data...' +
            '</td>' +
            '</tr>'
        );
    },

    /**
     * Hide chart loading state
     */
    hideChartLoading: function() {
        $(this.config.chartContainer).find('.chart-loading').hide();
    },

    /**
     * Refresh the component
     */
    refresh: function(params = {}) {
        this.loadData(params);
    },

    /**
     * Update filter
     */
    updateFilter: function(filterValue) {
        DashboardComponents.config.currentFilter = filterValue;
        this.loadData();
    },

    /**
     * Destroy the component
     */
    destroy: function() {
        if (this.config.chart) {
            this.config.chart.destroy();
            this.config.chart = null;
        }
    }
};
