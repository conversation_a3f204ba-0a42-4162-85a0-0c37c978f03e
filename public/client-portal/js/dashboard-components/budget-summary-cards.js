/**
 * Budget Summary Cards Component
 * Handles the budget summary cards display and updates
 */

var BudgetSummaryCards = {
    config: {
        clientId: null,
        permissions: {},
        selectors: {
            container: '#budget-summary-cards',
            budgetAmount: '.budget-amount',
            expensesAmount: '.expenses-amount',
            balanceAmount: '.balance-amount',
            balanceAmountColor: '.balance-amount-color',
            expensePercentage: '.expense-percentage',
            balancePercentage: '.balance-percentage',
            filterText: '.filter-text'
        },
        apiUrl: null
    },

    /**
     * Initialize the budget summary cards component
     */
    init: function(options = {}) {
        this.config = Object.assign(this.config, options);
        this.config.apiUrl = $(this.config.selectors.container).data('fetch');
        this.loadData();
    },

    /**
     * Load budget summary data
     */
    loadData: function(params = {}) {
        if (!this.config.apiUrl) return;

        var self = this;
        DashboardComponents.showLoading(this.config.selectors.container);

        var requestData = Object.assign({
            client: this.config.clientId,
            year: $(this.config.selectors.container).data('year') || new Date().getFullYear(),
            month: $(this.config.selectors.container).data('month') || (new Date().getMonth() + 1),
            filter: DashboardComponents.config.currentFilter
        }, params);

        DashboardComponents.makeRequest(this.config.apiUrl, requestData)
            .done(function(response) {
                self.renderCards(response);
                DashboardComponents.hideLoading(self.config.selectors.container);
                DashboardComponents.hideError(self.config.selectors.container);
            })
            .fail(function(xhr, status, error) {
                console.error('Failed to load budget summary:', error);
                DashboardComponents.hideLoading(self.config.selectors.container);
                DashboardComponents.showError(self.config.selectors.container, 'Failed to load budget summary');
            });
    },

    /**
     * Render the budget summary cards
     */
    renderCards: function(data) {
        // Calculate percentages
        var expensePercentage = this.calculatePercentage(data.expense, data.budget);
        var balancePercentage = this.calculatePercentage(data.balance, data.budget);
        
        // Get filter text
        var filterText = $("#monthselect option:selected").text() || 'This Month';

        // Update budget card with animation
        if ((this.config.permissions.view_budget_card ?? true) !== false) {
            this.animateCounter($(this.config.selectors.budgetAmount), data.budget);
            this.updateProgressBar('.budget-progress', data.budget, data.budget);
        }

        // Update expenses card with animation
        if ((this.config.permissions.view_expenses_card ?? true) !== false) {
            this.animateCounter($(this.config.selectors.expensesAmount), data.expense);
            this.updateProgressBar('.expense-progress', data.expense, data.budget);
        }

        // Update balance card with animation
        if ((this.config.permissions.view_balance_card ?? true) !== false) {
            this.animateCounter($(this.config.selectors.balanceAmount), data.balance);
            // Update balance color based on positive/negative
            var $balanceColor = $(this.config.selectors.balanceAmountColor);
            $balanceColor.removeClass('redFont greenFont');
            $balanceColor.addClass(data.balance < 0 ? 'redFont' : 'greenFont');
            this.updateProgressBar('.balance-progress', Math.abs(data.balance), data.budget);
        }

        // Update utilization percentage card with animation
        if ((this.config.permissions.view_expense_percentage_card ?? true) !== false) {
            this.animateCounter($(this.config.selectors.expensePercentage), expensePercentage, '%');
            this.updateProgressBar('.utilization-progress', expensePercentage, 100);
        }

        // Update efficiency score card with animation
        if ((this.config.permissions.view_balance_percentage_card ?? true) !== false) {
            var efficiencyScore = Math.max(0, 100 - expensePercentage);
            this.animateCounter($(this.config.selectors.balancePercentage), efficiencyScore, '%');
            this.updateProgressBar('.efficiency-progress', efficiencyScore, 100);
        }

        // Update filter text
        var filterName = data.filter_info?.filter_name || 'This Month';
        this.updateFilterText(filterName);

        // Update client image if available
        if (data.client && data.client.image) {
            $('.image img').attr('src', 'https://nursesgroup-crm.s3.eu-west-2.amazonaws.com/clients/' + data.client.image).show();
        }

        // Update unit menu if available
        if (data.units && data.units.length > 0) {
            this.updateUnitMenu(data.units);
        }
    },

    /**
     * Animate counter with easing
     */
    animateCounter: function($element, targetValue, suffix = '') {
        var startValue = parseFloat($element.attr('data-counter')) || 0;
        var duration = 1500;
        var startTime = null;

        var animate = function(currentTime) {
            if (startTime === null) startTime = currentTime;
            var progress = Math.min((currentTime - startTime) / duration, 1);

            // Easing function (ease-out)
            var easedProgress = 1 - Math.pow(1 - progress, 3);

            var currentValue = startValue + (targetValue - startValue) * easedProgress;

            if (suffix === '%') {
                $element.text(currentValue.toFixed(1) + suffix);
            } else {
                $element.text(BudgetSummaryCards.formatCurrency(currentValue));
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                $element.attr('data-counter', targetValue);
            }
        };

        requestAnimationFrame(animate);
    },

    /**
     * Update progress bar with animation
     */
    updateProgressBar: function(selector, value, maxValue) {
        var percentage = maxValue > 0 ? Math.min((value / maxValue) * 100, 100) : 0;

        setTimeout(function() {
            $(selector).css('width', percentage + '%');
        }, 300);
    },

    /**
     * Update filter text across all cards
     */
    updateFilterText: function(filterName) {
        $('.filter-text').text(filterName);
    },

    /**
     * Update unit menu
     */
    updateUnitMenu: function(units) {
        var menu = '';
        var link = $('.unitMenu').attr('link');

        if (link) {
            for (var i = 0; i < units.length; i++) {
                menu += '<li class="nav-item">' +
                    '<a href="' + link + '?id=' + units[i].clientUnitId + '" class="nav-link">' +
                    '<i class="far fa-circle nav-icon"></i>' +
                    units[i].name +
                    '</a>' +
                    '</li>';
            }
            $('.unitMenu').html(menu);
        }
    },

    /**
     * Calculate percentage
     */
    calculatePercentage: function(value, total) {
        if (!value || !total || total === 0) return 0;
        return parseFloat((value / total) * 100).toFixed(2);
    },

    /**
     * Format currency value
     */
    formatCurrency: function(value) {
        if (value === null || value === undefined) return '0.00';
        return parseFloat(value).toFixed(2);
    },

    /**
     * Refresh the component
     */
    refresh: function(params = {}) {
        this.loadData(params);
    },

    /**
     * Update filter
     */
    updateFilter: function(filterValue) {
        DashboardComponents.config.currentFilter = filterValue;
        this.loadData();
    }
};
