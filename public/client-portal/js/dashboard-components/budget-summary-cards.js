/**
 * Budget Summary Cards Component
 * Handles the budget summary cards display and updates
 */

var BudgetSummaryCards = {
    config: {
        clientId: null,
        permissions: {},
        selectors: {
            container: '#budget-summary-cards',
            budgetAmount: '.budget-amount',
            expensesAmount: '.expenses-amount',
            balanceAmount: '.balance-amount',
            balanceAmountColor: '.balance-amount-color',
            expensePercentage: '.expense-percentage',
            balancePercentage: '.balance-percentage',
            filterText: '.filter-text'
        },
        apiUrl: null
    },

    /**
     * Initialize the budget summary cards component
     */
    init: function(options = {}) {
        this.config = Object.assign(this.config, options);
        this.config.apiUrl = $(this.config.selectors.container).data('fetch');
        this.loadData();
    },

    /**
     * Load budget summary data
     */
    loadData: function(params = {}) {
        if (!this.config.apiUrl) return;

        var self = this;
        DashboardComponents.showLoading(this.config.selectors.container);

        var requestData = Object.assign({
            client: this.config.clientId,
            year: $(this.config.selectors.container).data('year') || new Date().getFullYear(),
            month: $(this.config.selectors.container).data('month') || (new Date().getMonth() + 1),
            filter: DashboardComponents.config.currentFilter
        }, params);

        DashboardComponents.makeRequest(this.config.apiUrl, requestData)
            .done(function(response) {
                self.renderCards(response);
                DashboardComponents.hideLoading(self.config.selectors.container);
                DashboardComponents.hideError(self.config.selectors.container);
            })
            .fail(function(xhr, status, error) {
                console.error('Failed to load budget summary:', error);
                DashboardComponents.hideLoading(self.config.selectors.container);
                DashboardComponents.showError(self.config.selectors.container, 'Failed to load budget summary');
            });
    },

    /**
     * Render the budget summary cards
     */
    renderCards: function(data) {
        // Calculate percentages
        var expensePercentage = this.calculatePercentage(data.expense, data.budget);
        var balancePercentage = this.calculatePercentage(data.balance, data.budget);
        
        // Get filter text
        var filterText = $("#monthselect option:selected").text() || 'This Month';

        // Update budget card
        if ((this.config.permissions.view_budget_card ?? true) !== false) {
            $(this.config.selectors.budgetAmount).text(this.formatCurrency(data.budget));
        }

        // Update expenses card
        if ((this.config.permissions.view_expenses_card ?? true) !== false) {
            $(this.config.selectors.expensesAmount).text(this.formatCurrency(data.expense));
        }

        // Update balance card
        if ((this.config.permissions.view_balance_card ?? true) !== false) {
            $(this.config.selectors.balanceAmount).text(this.formatCurrency(data.balance));
            // Update balance color based on positive/negative
            var $balanceColor = $(this.config.selectors.balanceAmountColor);
            $balanceColor.removeClass('redFont greenFont');
            $balanceColor.addClass(data.balance < 0 ? 'redFont' : 'greenFont');
        }

        // Update expense percentage card
        if ((this.config.permissions.view_expense_percentage_card ?? true) !== false) {
            $(this.config.selectors.expensePercentage).text(expensePercentage + '%');
        }

        // Update balance percentage card
        if ((this.config.permissions.view_balance_percentage_card ?? true) !== false) {
            $(this.config.selectors.balancePercentage).text(balancePercentage + '%');
        }

        // Update filter text in all cards
        $(this.config.selectors.filterText).text(filterText.toUpperCase());

        // Update client image if available
        if (data.client && data.client.image) {
            $('.image img').attr('src', 'https://nursesgroup-crm.s3.eu-west-2.amazonaws.com/clients/' + data.client.image).show();
        }

        // Update unit menu if available
        if (data.units && data.units.length > 0) {
            this.updateUnitMenu(data.units);
        }
    },

    /**
     * Update unit menu
     */
    updateUnitMenu: function(units) {
        var menu = '';
        var link = $('.unitMenu').attr('link');
        
        if (link) {
            for (var i = 0; i < units.length; i++) {
                menu += '<li class="nav-item">' +
                    '<a href="' + link + '?id=' + units[i].clientUnitId + '" class="nav-link">' +
                    '<i class="far fa-circle nav-icon"></i>' +
                    units[i].name +
                    '</a>' +
                    '</li>';
            }
            $('.unitMenu').html(menu);
        }
    },

    /**
     * Calculate percentage
     */
    calculatePercentage: function(value, total) {
        if (!value || !total || total === 0) return 0;
        return parseFloat((value / total) * 100).toFixed(2);
    },

    /**
     * Format currency value
     */
    formatCurrency: function(value) {
        if (value === null || value === undefined) return '0.00';
        return parseFloat(value).toFixed(2);
    },

    /**
     * Refresh the component
     */
    refresh: function(params = {}) {
        this.loadData(params);
    },

    /**
     * Update filter
     */
    updateFilter: function(filterValue) {
        DashboardComponents.config.currentFilter = filterValue;
        this.loadData();
    }
};
