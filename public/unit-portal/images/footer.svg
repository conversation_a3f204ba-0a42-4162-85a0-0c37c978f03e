<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 607.71 39.48">
  <defs>
    <style>
      .cls-1, .cls-2 {
        font-family: Marine, Marine;
        font-size: 8.5px;
      }

      .cls-3 {
        stroke: #212121;
        stroke-width: .6px;
      }

      .cls-3, .cls-4 {
        fill: none;
        stroke-miterlimit: 10;
      }

      .cls-4 {
        stroke: #a5a4a4;
        stroke-width: .5px;
      }

      .cls-5 {
        fill: #ddd;
      }

      .cls-6 {
        letter-spacing: 0em;
      }

      .cls-7, .cls-2 {
        fill: #212121;
      }

      .cls-8 {
        fill: #fff;
      }

      .cls-9 {
        letter-spacing: 0em;
      }

      .cls-10 {
        letter-spacing: -.06em;
      }

      .cls-11 {
        letter-spacing: -.02em;
      }

      .cls-12 {
        letter-spacing: -.02em;
      }

      .cls-13 {
        letter-spacing: -.02em;
      }

      .cls-14 {
        letter-spacing: -.02em;
      }

      .cls-15 {
        letter-spacing: -.02em;
      }

      .cls-16 {
        letter-spacing: -.02em;
      }

      .cls-17 {
        letter-spacing: -.02em;
      }

      .cls-18 {
        letter-spacing: -.03em;
      }

      .cls-19 {
        letter-spacing: -.03em;
      }

      .cls-20 {
        letter-spacing: 0em;
      }

      .cls-21 {
        letter-spacing: 0em;
      }

      .cls-22 {
        letter-spacing: 0em;
      }

      .cls-23 {
        letter-spacing: 0em;
      }

      .cls-24 {
        letter-spacing: 0em;
      }

      .cls-25 {
        letter-spacing: 0em;
      }

      .cls-26 {
        letter-spacing: 0em;
      }

      .cls-27 {
        letter-spacing: 0em;
      }

      .cls-28 {
        letter-spacing: 0em;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <text class="cls-1" transform="translate(5.66 22.09)"><tspan class="cls-21" x="0" y="0">N</tspan><tspan class="cls-26" x="5.86" y="0">e</tspan><tspan x="10.15" y="0">xus </tspan><tspan class="cls-23" x="24.4" y="0">I</tspan><tspan x="26.46" y="0">nn</tspan><tspan class="cls-24" x="35.67" y="0">o</tspan><tspan class="cls-20" x="40.2" y="0">v</tspan><tspan x="44.25" y="0">ation </tspan><tspan class="cls-11" x="63.91" y="0">C</tspan><tspan class="cls-9" x="68.48" y="0">ent</tspan><tspan class="cls-13" x="80.43" y="0">r</tspan><tspan x="83.09" y="0">e,</tspan><tspan class="cls-14" x="89.33" y="0"> </tspan><tspan x="90.91" y="0">Artiller</tspan><tspan class="cls-16" x="115.44" y="0">y</tspan><tspan x="119.12" y="0" xml:space="preserve"> 88, </tspan><tspan x="0" y="14">Geo</tspan><tspan class="cls-21" x="14.13" y="14">r</tspan><tspan class="cls-27" x="16.94" y="14">g</tspan><tspan x="21.37" y="14">e Smith </tspan><tspan class="cls-17" x="49.94" y="14">W</tspan><tspan class="cls-28" x="56.87" y="14">a</tspan><tspan class="cls-18" x="61" y="14">y</tspan><tspan x="64.55" y="14">,</tspan><tspan class="cls-19" x="66.42" y="14"> </tspan><tspan class="cls-10" x="67.98" y="14">Y</tspan><tspan x="72.18" y="14">e</tspan><tspan class="cls-25" x="76.54" y="14">o</tspan><tspan x="81.07" y="14">vil, BA22 8QR</tspan></text>
    <text class="cls-2" transform="translate(255.52 21)"><tspan x="0" y="0">01935 350366</tspan></text>
    <g>
      <circle class="cls-5" cx="245.57" cy="17.76" r="5.85"/>
      <path class="cls-7" d="M248.25,17.7c-.07,0-.13-.02-.18-.07-.05-.05-.08-.11-.09-.18-.07-.56-.31-1.03-.7-1.42-.39-.39-.87-.63-1.42-.7-.07,0-.13-.04-.18-.09-.05-.05-.07-.11-.07-.19s.03-.13.08-.18c.05-.05.11-.07.18-.06.7.08,1.3.36,1.79.86.49.49.78,1.09.86,1.79,0,.07-.01.13-.06.18-.05.05-.11.08-.18.08ZM246.83,17.7c-.06,0-.11-.02-.15-.06-.04-.04-.08-.09-.1-.15-.05-.19-.15-.35-.29-.49-.14-.14-.3-.24-.49-.29-.06-.02-.12-.05-.15-.1-.04-.04-.06-.09-.06-.16,0-.09.03-.16.09-.21.06-.05.13-.07.2-.05.3.07.56.21.78.43.22.22.36.48.44.78.02.08,0,.14-.06.2-.06.06-.13.09-.21.09ZM248.13,20.72c-.65,0-1.3-.15-1.96-.45-.65-.3-1.26-.73-1.8-1.28-.55-.55-.97-1.15-1.28-1.8-.3-.65-.45-1.3-.45-1.95,0-.1.03-.19.1-.26s.15-.1.26-.1h1.12c.09,0,.16.03.23.09.07.06.11.13.13.21l.2,1.01c.01.09.01.17,0,.24-.02.07-.05.12-.1.17l-.8.77c.13.23.27.46.44.67.16.21.34.41.53.6.19.19.39.36.6.53.21.16.44.31.69.45l.77-.78c.05-.06.12-.1.2-.12.08-.02.16-.03.24-.02l.96.19c.09.02.16.07.21.13.05.07.08.14.08.22v1.12c0,.1-.03.19-.1.26-.07.07-.16.1-.26.1ZM243.53,16.87l.61-.59s.02-.02.02-.04,0-.03,0-.04l-.15-.77s-.01-.03-.02-.04-.03-.01-.04-.01h-.74s-.02,0-.03.01c0,0-.01.02-.01.03.02.24.06.47.12.72.06.24.14.48.25.73ZM246.52,19.85c.23.11.47.19.71.24.25.06.48.09.69.1.01,0,.02,0,.03-.01,0,0,.01-.02.01-.03v-.72s0-.03-.01-.04c0-.01-.02-.02-.04-.02l-.72-.15s-.02,0-.03,0-.02.01-.03.02l-.61.62Z"/>
    </g>
    <g>
      <circle class="cls-5" cx="245.57" cy="33.63" r="5.85"/>
      <path class="cls-7" d="M243.07,36.06c-.16,0-.3-.06-.42-.17s-.17-.25-.17-.42v-3.69c0-.16.06-.3.17-.42s.25-.17.42-.17h4.99c.16,0,.3.06.42.17s.17.25.17.42v3.69c0,.16-.06.3-.17.42s-.25.17-.42.17h-4.99ZM248.16,32.15l-2.44,1.56s-.05.03-.08.03c-.03,0-.05.01-.08.01s-.05,0-.08-.01c-.03,0-.05-.02-.08-.03l-2.44-1.56v3.32s0,.05.03.07.04.03.07.03h4.99s.05,0,.07-.03.03-.04.03-.07v-3.32ZM245.57,33.3l2.54-1.62h-5.09l2.54,1.62ZM242.98,32.15v.07-.37h0s0-.17,0-.17v.18h0s0,.36,0,.36v-.07,3.42-3.42Z"/>
    </g>
    <g>
      <g>
        <circle class="cls-7" cx="576.53" cy="31.8" r="6.21"/>
        <path class="cls-8" d="M574.57,34.82h-1.54v-4.63h1.54v4.63ZM573.81,29.56h-.02c-.51,0-.85-.36-.85-.8s.35-.8.88-.8.85.35.86.8c0,.44-.33.8-.87.8ZM580.12,34.82h-1.54v-2.47c0-.62-.23-1.05-.78-1.05-.43,0-.68.28-.79.56-.04.09-.05.24-.05.37v2.58h-1.54s.03-4.19,0-4.63h1.54v.65c.2-.31.57-.77,1.39-.77,1.01,0,1.78.66,1.78,2.08v2.65Z"/>
      </g>
      <g>
        <circle class="cls-7" cx="591.74" cy="31.8" r="6.21"/>
        <path class="cls-8" d="M592.35,31.27c.82-.88,1.64-1.76,2.46-2.64-.04-.02-.07-.02-.08-.02h-.43c-.06,0-.09.02-.14.06-.67.73-1.33,1.46-2.01,2.19-.02.02-.04.04-.05.06-.03-.04-.04-.06-.06-.08-.53-.72-1.08-1.44-1.62-2.19-.03-.03-.06-.04-.09-.04h-1.91s-.03,0-.06.02c.88,1.16,1.75,2.33,2.62,3.49-.89.96-1.75,1.9-2.64,2.87.19,0,.36-.02.52,0,.06,0,.09-.04.13-.06.62-.68,1.25-1.36,1.87-2.02.13-.14.26-.29.39-.43.03.04.04.04.06.08.59.79,1.17,1.58,1.76,2.36.04.04.06.06.12.06.63-.02,1.25,0,1.89,0h.07c-.94-1.26-1.85-2.49-2.79-3.72ZM593.37,34.53s-.04-.02-.06-.04c-.39-.54-.79-1.06-1.2-1.59-.94-1.26-1.87-2.52-2.81-3.78-.02-.04-.04-.04-.05-.08h.85c.05,0,.08.02.12.06.88,1.16,1.75,2.35,2.62,3.51.46.62.91,1.23,1.37,1.85.02,0,.03.04.06.07-.31,0-.6,0-.9,0Z"/>
      </g>
      <g>
        <circle class="cls-7" cx="561.62" cy="31.8" r="6.21"/>
        <path class="cls-8" d="M561.62,30.07c-.96,0-1.73.78-1.73,1.73s.78,1.73,1.73,1.73,1.73-.78,1.73-1.73-.78-1.73-1.73-1.73ZM561.62,32.93c-.62,0-1.13-.51-1.13-1.13s.51-1.13,1.13-1.13,1.13.51,1.13,1.13-.51,1.13-1.13,1.13ZM563.42,29.59c-.23,0-.41.18-.41.41s.18.41.41.41.41-.18.41-.41-.18-.41-.41-.41ZM563.42,29.59c-.23,0-.41.18-.41.41s.18.41.41.41.41-.18.41-.41-.18-.41-.41-.41ZM561.62,30.07c-.96,0-1.73.78-1.73,1.73s.78,1.73,1.73,1.73,1.73-.78,1.73-1.73-.78-1.73-1.73-1.73ZM561.62,32.93c-.62,0-1.13-.51-1.13-1.13s.51-1.13,1.13-1.13,1.13.51,1.13,1.13-.51,1.13-1.13,1.13ZM564.97,30.41c0-.36-.07-.61-.15-.82-.09-.23-.22-.43-.39-.6s-.38-.3-.6-.39c-.21-.08-.46-.14-.81-.15-.37-.02-.48-.02-1.4-.02s-1.03,0-1.39.02c-.36,0-.6.07-.82.15-.23.09-.43.22-.6.39-.17.17-.31.38-.4.6-.08.21-.14.46-.15.81-.02.37-.02.48-.02,1.4s0,1.03.02,1.39c.02.36.07.6.15.82.09.23.23.43.4.6.16.17.37.31.6.4.22.08.46.14.82.15.36.02.47.02,1.39.02s1.03,0,1.39-.02c.36-.02.6-.07.82-.15.45-.18.81-.54.99-.99.08-.22.14-.46.15-.82.02-.36.02-.47.02-1.39s0-1.03-.02-1.39ZM564.37,33.16c-.02.33-.07.51-.12.63-.12.29-.35.52-.64.64-.12.05-.3.1-.63.12-.35,0-.46.02-1.36.02s-1.01,0-1.36-.02c-.33-.02-.51-.07-.62-.12-.15-.05-.28-.14-.39-.25-.12-.11-.2-.24-.25-.39-.05-.12-.11-.3-.12-.63-.02-.35-.02-.46-.02-1.36s0-1.01.02-1.36c0-.33.06-.51.12-.63.05-.14.14-.28.25-.39.11-.11.23-.19.39-.24.12-.05.3-.11.62-.12.36-.02.46-.03,1.36-.03s1.01,0,1.37.03c.32,0,.51.06.62.12.14.05.28.14.39.24.11.12.2.24.25.39.05.13.1.3.12.63.02.35.02.46.02,1.36s0,1.01-.02,1.36ZM563.42,29.59c-.23,0-.41.18-.41.41s.18.41.41.41.41-.18.41-.41-.18-.41-.41-.41ZM561.62,30.07c-.96,0-1.73.78-1.73,1.73s.78,1.73,1.73,1.73,1.73-.78,1.73-1.73-.78-1.73-1.73-1.73ZM561.62,32.93c-.62,0-1.13-.51-1.13-1.13s.51-1.13,1.13-1.13,1.13.51,1.13,1.13-.51,1.13-1.13,1.13Z"/>
      </g>
      <g>
        <circle class="cls-7" cx="546.64" cy="31.8" r="6.21"/>
        <path class="cls-8" d="M547.86,29.24h.68v-1.16c-.12-.02-.53-.05-1-.05-.99,0-1.67.6-1.67,1.72v.96h-1.12v1.3h1.12v3.55l1.35.03v-3.58h1.12l.17-1.3h-1.29v-.83c0-.38.11-.63.64-.63Z"/>
      </g>
    </g>
    <text class="cls-2" transform="translate(255.77 36.86)"><tspan class="cls-24" x="0" y="0">o</tspan><tspan class="cls-6" x="4.53" y="0">ffi</tspan><tspan class="cls-15" x="12.33" y="0">c</tspan><tspan x="16.13" y="0">e@nu</tspan><tspan class="cls-22" x="36.12" y="0">r</tspan><tspan x="38.94" y="0">se</tspan><tspan class="cls-20" x="46.84" y="0">s</tspan><tspan x="50.34" y="0">g</tspan><tspan class="cls-12" x="54.79" y="0">r</tspan><tspan x="57.48" y="0">oup.</tspan><tspan class="cls-17" x="73.09" y="0">c</tspan><tspan x="76.9" y="0">o.uk</tspan></text>
    <text class="cls-2" transform="translate(453.74 35.56)"><tspan x="0" y="0">smartseek.</tspan><tspan class="cls-17" x="38.98" y="0">c</tspan><tspan x="42.79" y="0">o.uk</tspan></text>
    <line class="cls-4" x1="530.36" y1="25.94" x2="530.36" y2="38.66"/>
    <line class="cls-3" y1=".3" x2="607.71" y2=".3"/>
  </g>
</svg>