.season_tabs {
    position: relative;
    min-height:500px;
    clear: both;
    margin-bottom: 30px;
  }

  .season_tab {
    float: left;
    clear: both;
    width: 246px;
    margin-bottom: 6px;
  }

  .season_tab label {
    background: #f4f4f4;
    padding: 10px;
    border: 1px solid #f4f4f4;
    margin-left: -1px;
    font-size: 16px;
    vertical-align: middle;
    position: relative;
    left: 1px;
    width: 264px;
    height: 47px;
    display: table-cell;
  }

  .season_tab [type=radio] {
    display: none;
  }

  .Report_Content {
    position: absolute;
    top: 0;
    left: 259px;
    right: 0;
    padding: 15px;
    background: #fff;
    border: 1px solid #f4f4f4;
        height: 100%;
  }

  .Report_Content span {
    animation: 0.5s ease-out 0s 1 slideInFromTop;
  }

  [type=radio]:checked~.RepLabel {
    background: #212121;
    border: 3px solid #212121;
    cursor: pointer;
    z-index: 2;
    color: #fff;
  }

  [type=radio]:checked~.RepLabel~.Report_Content {
    z-index: 1;
  }

  .Reportcard {
    border-radius: 6px;
    padding: 15px 15px 12px;
    margin-bottom: 22px;
    position: relative;
    background: #f4f4f4;
  }
  .Reportcard:hover {
    background: #212121;
  }
  .Reportcard:hover h3 {
    color: #fff;
  }
  .Reportcard:hover p {
    color: #fff;
  }

  .Reportcard h3 {
    font-size: 15px;
    margin: auto;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .Reportcard p {
    font-size: 13px;
    margin: auto;
    margin-bottom: 0;
  }

  .Reportcard .dropdown {
    position: absolute;
    top: 11px;
    right: 11px;
  }

  .BtTogle {
    background: none;
  }

  .BtTogle i {
    color: #bfbdbd;
    font-size: 21px;
  }
  .drpPad{
    padding: 10px;
  }



.filter-container {
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.filter-box {
    flex: 1 1 180px;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
}
.filter-label {
    font-size: 10px;
    color: #888;
    display: block;
    margin-bottom: 4px;
}

.filter-value {
    font-size: 14px;
    font-weight: 500;
}

.download-btn {
    background: #5f3dc4;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    margin-left: auto;
}

.toggle-filters {
font-size: 12px;
    color: #3f51b5;
    cursor: pointer;
    margin-top: 2px;
    display: inline-block;
    margin-left: 8px;
}

.filters-wrapper {
    transition: max-height 0.3s ease;
    overflow: hidden;
}

.hidden {
    display: none;
}

.view-report {
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
    display: inline-block;
}
.titlesm{
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 17px;
}