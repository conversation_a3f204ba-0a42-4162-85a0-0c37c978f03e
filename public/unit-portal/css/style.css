@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');
.container {
    width: 100%;
    max-width: 100%;
    margin: 0px auto;
    padding: 0 4%;
}

header {
    display: inline-block;
    width: 100%;
    float: left;
}

.header_top {
    float: left;
    width: 100%;
}

.header_top .left {
    width: calc(100% - 300px);
    background: #f36b22;
    min-height: 74px;
}

.header_top .left a {
    color: #fff;
    padding: 29px 30px;
    float: left;
    font-weight: bold;
}

.header_top .left a+a {
    float: right;
}

.header_top .right {
    width: 300px;
    min-height: 74px;
}

.header_top .right a {
    float: right;
    padding: 29px 10px;
    text-align: center;
    color: #fff;
    font-weight: bold;
    background: #212121;
    width: 100%;
}

.header_top .right a:hover {
    background: #0f63a2;
}

.header_bottom {
    float: left;
    width: 100%;
    background: #d7d7d7;
    border-bottom: 1px solid #909393;
    padding: 10px 0;
}

.header_bottom a {
    font-weight: bold;
    color: #494949;
    padding: 10px 0;
    display: inline-block;
}

.header_bottom .left {
    width: calc(100% - 300px);
}

.header_bottom .left a {
    float: right;
    padding: 10px 30px 10px 40px;
    background: url(../images/power.png) no-repeat left center;
}

.header_bottom .right {
    width: 300px;
    text-align: center;
    border-left: 1px solid #5e5e5e;
}

.main_area {
    min-height: 100vh;
    background: url(../images/banner.jpg) no-repeat;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

}

.main_area .left {
    width: 40%;
    padding-top: 0px;
    height: 100vh;
    padding-left: 80px;
}

.main_area .left .logo {
    max-width: 240px;
    padding-top: 30px;
}

.main_area .left h1 {
    font-size: 36px;
    line-height: 46px;
    color: #f36b22;
    font-weight: bold;
    margin-top: 20%;
    letter-spacing: -1px;
}

.main_area .left p {
    font-size: 18px;
    color: #494949;
    margin-top: 60px;
    max-width: 80%;
    margin-bottom: 80px;
}

.contact_box {
    border-bottom: aqua;
    margin-top: 30px;
    padding-bottom: 30px;
    position: absolute;
    bottom: 0;
}

.contact_box+.contact_box {
    border-bottom: none;
    margin-bottom: 50px;
}

.contact_box .left {
    width: 30%;
    padding-top: 0;
}

.contact_box+.contact_box .right {
    padding-top: 0;
}

.contact_box .right h3 {
    color: #494949;
    font-size: 26px;
    font-weight: bold;
}

.contact_box .right a {
    color: #f36b22;
    font-size: 19px;
    font-weight: bold;
    margin-top: 10px;
    display: inline-block;
}

.contact_area {
    float: right;
    width: 450px;
}

.CltTitle {
    margin-top: 20%;
}

.contact_top {
    float: left;
    width: 580px;
    background: #212121;
    padding: 60px;
    border: 1px solid #ddd;
    border-radius: 10px;
    margin-top: 3%;
}

.contact_top ul {
    float: left;
    width: 100%;
}

.contact_top ul li {
    float: left;
    width: 100%;
    margin: 10px 0;
}

.text_ {
    float: left;
    width: 100%;
    color: #acacac;
    border-radius: 5px;
    border: 1px solid #fff;
    padding: 15px 30px;
}

.forgt_psswrd {
    float: right;
    font-size: 12px;
    color: #fff;
}

.login_ {
    background: #212121;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 12px 5px;
    width: 100%;
        cursor: pointer;
    display: inline-block;
}
.login_:hover {
    opacity: 0.8;
}

.regiter_now {
    background: #0d4875;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    padding: 15px 5px;
    border-radius: 5px;
    border: 1px solid #0d4875;
    cursor: pointer;
    width: 100%;
    display: inline-block;
    text-align: center;
}

.contact_bottom {
    float: left;
    width: 100%;
    background: #ff6000;
    padding: 30px;
}

.contact_bottom h3 {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    width: 100%;
    float: left;
}

.contact_bottom h6 {
    font-size: 16px;
    color: #fff;
    text-align: center;
    width: 100%;
    background: #212121;
    padding: 10px 5px;
    border-radius: 5px;
    margin: 15px 0;
    float: left;
}

.contact_bottom ul {
    float: left;
    width: 100%;
}

.contact_bottom ul li {
    float: left;
    width: 100%;
    border-bottom: 1px solid #5e5e5e;
    padding: 15px 0;
}

.contact_bottom ul li:last-child {
    border-bottom: none;
    padding-right: 0;
}

.contact_bottom ul li .left {
    width: 25%;
    padding: 0;
    display: inline-block;
}

.contact_bottom ul li .right {
    width: 73%;
}

.contact_bottom ul li .right h4 {
    color: #fff;
    font-weight: bold;
    margin-bottom: 10px;
}

.contact_bottom ul li .right p {
    color: #fff;
    font-size: 15px;
    line-height: 18px;
}

#global-loader {
    position: fixed;
    z-index: 50000;
    background: url(../images/loader.svg) no-repeat 50% 50% rgba(255, 255, 255);
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: 0 auto;
    text-align: center;
}

#global-loader h2 {
    margin-top: 10%;
    font-size: 30px;
}

.col-lg-6 {
    width: 45%;
    float: left;
}

text.highcharts-credits {
    display: none;
}

.left_section {
    float: left;
    width: 250px;
    height: 100vh;
    background: #212121;
    position: fixed;
}

.left_section_top {
    width: 100%;
    display: flex;
    align-items: center;

}

.left_section_top_imgbox {
    float: left;
}

.left_section_bottom {
    float: left;
    width: 100%;
    padding-left: 25px;
    height: calc(100vh - 40px);
    padding-right: 25px;
}
.small_logo {
    padding: 14px 24px;
    margin-bottom: 10px;
}
h2.PortName {
    margin: 0;
    color: #fff;
    font-weight: 700;
    margin-bottom: 13px;
    font-size: 26px;
}

.nav_area {
    float: left;
    width: 100%;
}

.nav_area ul {
    float: left;
    width: 100%;
}

.nav_area ul li {
    float: left;
    width: 100%;
    border-top: 1px solid #5e5e5e;
}

.nav_area ul li a {
    float: left;
    color: #7c7c7c;
    font-size: 14px;
    width: 100%;
    padding: 13px 0px;
    text-transform: capitalize;
}

.nav_area ul li a:hover,
.nav_area ul li a.active {
    color:#fff !important;
}

.nav_area ul li a img {
    float: left;
    max-width: 32px;
    margin-right: 16px;
}

.nav_area ul li a p {
    color: #fff;
    text-transform: uppercase;
    font-size: 12px;
    margin: 8px 0;
    
}

.profile_boxTwo {
    float: left;
    width: 222px;
    bottom: 10px;
    position: absolute;
}

.profile_box {
    float: left;
    width: 222px;
}

.CLientNamePt {
    position: relative;
    color: #494949;
}

.CLientNamePt h5 {
    margin: 0;
    font-style: italic;
    font-size: 12px;
    margin-top: 3px;
}

.CLientNamePt h6 {
    margin-bottom: 7px;
}
.PorfName {
    float: left;
    border-radius: 5px;
    font-size: 15px;
    width: 59%;
    margin-left: 5%;
    color: #7c7c7c;
    
    line-height: 20px;
    padding-top: 14px;
}

.pr__det {
    float: left;
    width: 100%;
    margin-top: 6px;
}



.pr__det a {
    float: left;
    width: 100%;
    font-size: 12px;
    color: #7c7c7c;
    padding: 2px 0;
    
}

.pr__det a i {
    color: #7c7c7c;
}

.pr__det a:hover {
    color: #fff;
}

.chat_box {
    float: left;
    width: 100%;
    margin-top: 28px;
}

.chat_box a {
    float: left;
    width: 100%;
    background: url(../images/chat.png) no-repeat 16px 8px #ffffff;
    background-size: 30px;
    border-radius: 5px;
    padding: 15px;
    padding-left: 60px;
    color: #494949;
}

.calendar_box {
    display: inline-block;
    width: 100%;
    float: left;
    margin-top: 28px;
}

.days {
    padding: 0px 0px;
    margin: 0;
    float: left;
    width: 100%;
}

.days li {
    list-style-type: none;
    background: #212121;
    padding: 10px 5px;
    display: inline-block;
    width: 12.6%;
    text-align: center;
    margin-bottom: 5px;
    font-size: 12px;
    color: #fff;
}

.days li.active {
    background: #f36b22;
    color: #fff !important;
    display: inline-block;
}

.w10Percntge {
    width: 10% !important;
}

.right_section {
    float: left;
    width: calc(100% - 250px);
}

.right_section_top_strip {
    background: #f4f4f4;
    padding: 12px 20px 7px 25px;
    
}
ul#user-menu {
    margin-top: -10px;
}
.right_section_top_strip .small_logo {
    float: left;
    max-width: 215px;
    margin-top: -13px;
    position: relative;
    top: 1px;
    margin-bottom: 0px;
}

.right_section_top_strip .left h6 {
    float: left;
    font-size: 12px;
    color: #0d4875;
    padding-left: 7px;
    
    margin-top: 0;
}

.right_section_top_strip .left h6 span {
    font-size: 14px;
    color: #f36b22;
}

.idSeachStaf {
    font-size: 14px;
    margin-left: 5px;
    margin-top: 2px;
}

.right_section_top_strip .left h5 {
    font-weight: bold;
    float: left;
    font-size: 18px;
    margin-left: 12px;
    color: #0d4875;
    margin-top: -2px;
    padding-left: 12px;
    border-left: 1px solid #0d4875;
}


.WelcMan {
    font-size: 14px;
    position: relative;
    border-radius: 2px;
    margin: 10px 10px 0 0px;
}

li.WelDrpMenu {
    margin-right: 15px;
}

.WelDrpMenu a {
    font-size: 35px;
}

.right_section_top_strip .right h4 {
    float: left;
    font-size: 12px;
    padding: 0 12px;
    border-left: 1px solid #0d4875;
    border-right: 1px solid #0d4875;
    margin: 0px 12px;
}

.right_section_top_strip .right h5 {
    float: left;
    font-size: 12px;
}

.right_section_top_strip .right a {
    padding: 0;
    line-height: 27px;
}


button.closeBtn:hover {
    
}

.add_booking {
    float: left;
    width: 100%;
}

.add_booking .full_width {
    width: 100%;
    margin-bottom: 10px;
        background: #fff;
        
}

.full_width {
    float: left;
    width: 100%;
    margin: 0px;
}

.fby_one {
    float: left;
    width: 18%;
    padding: 0 1px;
}

.sby_one {
    float: left;
    width: 7%;
}

.add_booking p {
    float: left;
    font-size: 12px;
    margin-bottom: 0px;
    width: 100%;
    line-height: 19px;
}

.txt_bx {
    float: left;
    width: 100%;
    background: #fff;
    border: none;
    border-radius: 5px;
    padding: 5px 10px;
    height: 34px;
    resize: none;
    font-family: "Nunito", sans-serif;
    font-size: 12px;
}

.font10,
.font10 strong {
   
}

select.txt_bx {
    float: left;
    width: 100%;
    background: #fff;
    border: none !important;
    border-radius: 5px;
    padding: 5px 10px;
    height: 34px;
    resize: none;
    font-family: "Nunito", sans-serif;
    font-size: 12px;
}

.add_newrow {
    float: left;
    font-size: 14px;
    color: #fff;
    background: url(../images/plus.png) 7px center #0d4875 no-repeat;
    background-size: 15px;
    border: 1px solid #0d4875;
    border-radius: 5px;
    padding: 10px 15px 10px 33px;
    cursor: pointer;
    width: auto;
}

.addBtnNew {
    width: 95%;
    margin-bottom: 13px;
    margin-top: 19px;
    padding: 13px 15px 13px 33px;
}

.restBtn {
    float: left;
    font-size: 14px;
    color: #fff;
    background: #174d79;
    border: 1px solid #0d4875;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    width: auto;
}

.add_save {
    float: left;
    font-size: 14px;
    color: #fff;
    background: #f36b22 no-repeat;
    background-size: 20px;
    border: 1px solid #f36b22;
    border-radius: 3px;
    padding: 10px 20px;
    cursor: pointer;
    width: auto;
    text-align: center;
}

.BtSave {

}

.restBtn {
    margin: 0;
}

.btn_save {
    float: left;
    font-size: 14px;
    color: #fff;
    background: #f36b22 no-repeat;
    background-size: 20px;
    border: 1px solid #f36b22;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    width: auto;
}
.ClitBdBg {
    padding: 24px;
    margin-bottom: 23px;
      background: #f4f4f4;
    text-align: left;
        padding-left: 23px;
}
.FlitSelect {
    width: 290px !important;
    float: right;
    margin-top: 12px;
}
.blue_strip {
    float: left;
    padding: 10px 20px;
    background: #212121;
    margin: 0 0 10px 0;
    width: 100%;
    color: #fff;
    font-size: 12px;
}

.shift_box {
    float: left;
    width: 100%;
}

.shift_top {
    float: left;
    width: 100%;
    color: #494949;
    margin-bottom: 5px;
}
label {
    font-weight: 500 !important;
}
.GpHedBtm{
    border: none !important;
    margin-bottom: 0px !important;
    padding-bottom: 3px !important;
}
.shift_top p {
    float: left;
    width: 100%;
}

.shift_top h5 {
    float: left;
    font-weight: 700;
    margin: 3px;
}

.shift_top span {
    color: #494949;
    font-weight: bold;
}

.shift_bottom {
    float: left;
    width: 100%;
}

.shift_line {
    float: left;
    background: #ededed;
    width: 100%;
    padding: 0px 0px;
    font-weight: 600;
}
.bold {
    font-weight: 500 !important;
}
.bk_id {
    float: left;
    width: 8%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.bk_date {
    float: left;
    width: 11%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.bk_shift {
    float: left;
    width: 5%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.bk_cagry {
    float: left;
    width: 8%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.bk_staff {
    float: left;
    width: 23%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.bk_status {
    float: left;
    width: 9%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.bk_amnd {
    float: left;
    width: 8%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.bk_amnded {
    float: left;
    width: 8%;
    padding: 12px 8px;
    color: #494949 !important;
    font-size: 14px;
    border-color: greenyellow !important;

}
.bk_amnded_amber {
    float: left;
    width: 8%;
    padding: 8px 8px;
    color: #494949 !important;
    font-size: 14px;
    border-color: greenyellow !important;

}

.bk_details {
    float: right;
    width: 14%;
    padding: 12px 8px;
    color: #494949;
    font-size: 14px;
}

.p25px {
    padding: 25px 8px !important;
}

.bold {
    font-weight: bold;
}

.redFont {
    color: #f00 !important;
}

.btndate_warning {
    background: #fff !important;
    border: 2px solid #a6a6a6 !important;
    margin-top: 4px !important;
    color: #343434 !important;
}

.btndate_warning:hover {
    background: #a6a6a6 !important;
    color: #fff !important;
}

.btndate_danger {
    background: #fff !important;
    border: 2px solid #f02828 !important;
    margin-top: 4px !important;
    color: #f02828 !important;
}

.btndate_danger:hover {
    background: #f02828 !important;
    color: #fff !important;
}

.btndate_success {
    background: #fff !important;
    border: 2px solid #338812 !important;
    margin-top: 4px !important;
    color: #338812 !important;
}

.btndate_success:hover {
    background: #338812 !important;
    color: #fff !important;
}

.btndate_primary {
    background: #fff;
    border: 2px solid #174d79;
    margin-top: 4px;
    color: #494949;
}

.btndate_primary:hover {
    background: #174d79;
    color: #fff;
}

a:focus {
    color: #fff;
    text-decoration: none;
    opacity: 0.9;
}

.selected_warning_button {
    background: #494949 !important;
    color: #fff !important;
}

.leftSideGrph,
.rightSideGraph {
    padding-top: 0px;
}

.pagination>li>a:focus,
.pagination>li>a:hover,
.pagination>li>span:focus,
.pagination>li>span:hover {
    z-index: 2;
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.shift_bottom ul {
    float: left;
    width: 100%;
}

.shift_bottom ul li {
    float: left;
    width: 100%;
}
.shift_bottom li:nth-child(even) {
    background-color: #f8f8f8;
}
.shift_bottom li:last-child{
        border-bottom: 1px solid #ddd;
}

.FiltBgOne {

}

.FiltBgOne label {
margin-top: 12px;
    margin-bottom: 2px;
}

.ulchild li:nth-child(even) {
    background-color: #f5f5f5;
}

.btmt4 {
    margin-top: 29px;
}

.shift_bottom ul li .bk_id {
    color: #494949;
    font-size: 12px;
    padding: 9px 8px;
}

.shift_bottom ul li .bk_date {
    color: #494949;
    font-size: 12px;
    padding: 9px 8px;
}

.shift_bottom ul li .bk_shift {
    color: #494949;
    font-size: 12px;
    padding: 13px 8px;

}

.shift_bottom ul li .bk_cagry {
    color: #494949;
    font-size: 12px;
    padding: 13px 8px;
}

.shift_bottom ul li .bk_staff {
    color: #494949;
    font-size: 12px;
    padding: 13px 8px;
}

.shift_bottom ul li .bk_status {
    color: #494949;
    font-size: 12px;
    padding: 13px 8px;
}

.shift_bottom ul li .bk_details {
    color: #494949;
    font-size: 12px;
    padding: 4px 8px;
    line-height: 12px;
}

.shift_bottom ul li .bk_details p {
    width: 100%;
    display: inline-block;
    color: #494949;
    font-weight: normal;
}

.shift_bottom ul li .bk_details span {
    width: 100%;
    display: inline-block;
    
    font-weight: normal;
}

.shift_bottom ul li .confrimd {
    font-size: 13px;
    display: inline-block;
    padding: 6px 6px 6px;
    color: #42b315;
    text-align: center;
}

.shift_bottom ul li .awaiting {
    font-size: 13px;
    display: inline-block;
    padding: 2px 6px 2px;
    color: #e1a700;
    text-align: center;
    margin-top: 3px;
}

.BtProgress {
    padding: 3px 7px;
    background: #e8e8e8;
    border-radius: 200px;
    margin-left: 4px;
}

.BtProgress:hover {
    background: #212121;
}

.shift_bottom ul li .confrimd:hover {
    color: #36a50a !important;
}

.shift_bottom ul li .cancel {
    font-size: 13px;
    background: #ffe7e7;
    display: inline-block;
    padding: 2px 6px 2px;
    color: #de2424;
    
    text-align: center;
    margin-top: 2px;
}

.shift_bottom ul li .cancel:hover {
    color: #de2424;
}

.shift_bottom ul li .view {
    color: #494949;
    font-size: 13px;
    background: #fff;
    display: inline-block;
    text-align: center;
    border: 1px solid #e6e6e6;
    height: 28px;
    width: 28px;
    line-height: 27px;
    padding: 7px;
}
.shift_bottom ul li .view:hover {
    color: #fff;
    background: #494949;
}
.shift_bottom ul li .profile {
    color: #494949;
    font-size: 12px;
    background: #fff;
    display: inline-block;
    text-align: center;
    border: 1px solid #e6e6e6;
    height: 28px;
    width: 28px;
    line-height: 27px;
    padding: 7px;
}
.shift_bottom ul li .profile:hover {
    color: #fff;
    background: #494949;
}

.log_button {
    color: #494949;
    font-size: 12px;
    background: #fff;
    display: inline-block;
    text-align: center;
    border: 1px solid #e6e6e6;
    height: 28px;
    width: 28px;
    line-height: 27px;
    padding: 7px;
}
.log_button:hover {
    color: #fff;
    background: #494949;
}

.btns {
    color: #fff;
    font-size: 12px;
    background: #494949;
    display: inline-block;
    padding: 10px 5px;
    border-radius: 5px;
    width: 100%;
    text-align: center;
}

.entry_lab {
    padding-left: 2px !important;
}
.btn-default:hover {
    color: #fff;
        background: #212121;
}

img.thmbImg {
    width: 26px;
    border-radius: 200px;
    height: 26px;
    float: left;
    position: relative;
    padding: 2px;
    cursor: pointer;
    margin-right: 1px;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1);
    transition: all 200ms ease-in;
    transform: scale(1);
    display: inline-block;
}

img.thmbImg:hover {
    z-index: 2;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(4);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(4);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1.5);
    transition: all 200ms ease-in;
    transform: scale(4);
}

.bk_staff span {
    float: left;
    margin-top: 11px;
    padding-left: 10px;
}

.bk_staff span {
    float: left;
    margin-top: 6px;
    padding-left: 0px;
}

button.cancelBtn.btn.btn-sm.btn-default {
    background: #ec6a33;
    color: #fff;
}

button.applyBtn.btn.btn-sm.btn-primary {
    background: #2675bb;
    color: #fff;
}

.txt_bx_modal {
    width: 100%;
    background: #fff;
    border: 1px solid #737373;
    border-radius: 5px;
    padding: 5px 10px;
    color: #494949;
    font-weight: 700;
    height: 36px;
    resize: none;
    font-family: "Nunito", sans-serif;
    font-size: 12px;
}

.add_save_modal {
    font-size: 14px;
    color: #fff;
    background: #f36b22 no-repeat;
    background-size: 20px;
    border: 1px solid #f36b22;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
    padding-right: 10px;
    width: 120px;
}

.add_save_modal_close {
    font-size: 14px;
    color: #fff;
    background: #f36b22 no-repeat;
    background-size: 20px;
    border: 1px solid #f36b22;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
    padding-right: 10px;
    width: 120px;
}

.inlineBlock {
    width: 100%;
}

.upEvnts {
    float: left;
    width: 100%;
    margin-top: 20px;
}

.upEvnts .head {
    font-size: 12px;
    color: #fff;
    background: #174d78;
    padding-bottom: 10px;
    padding: 10px;
}

.upEvnts ul li {
    padding-bottom: 10px;
    color: #fff;
    font-size: 11px;
    margin-left: 10px;
    border-bottom: 1px solid;
    margin-top: 10px;
}

.setBudget {
    float: right;
    font-size: 12px;
    color: #fff;
    background: #f36b22 no-repeat;
    background-size: 20px;
    border: 1px solid #f36b22;
    border-radius: 5px;
    padding: 5px 15px;
    cursor: pointer;
    padding-right: 10px;
}

.saveBudget {
    float: left;
    font-size: 12px;
    color: #fff;
    background: #f36b22 no-repeat;
    background-size: 20px;
    border: 1px solid #f36b22;
    border-radius: 5px;
    padding: 5px 15px;
    margin-left: 10px;
    margin-top: 5px;
    cursor: pointer;
    padding-right: 10px;
}

input.text_field {
    width: 70% !important;
}

.unit_privacy_sec {
    padding: 10px;
}

.amdbtn_green {
    padding: 4px 40px 7px;
    background: #4ebf21 !important;
    
    font-size: 18px;
    color: #fff !important;
    border-radius: 4px !important;
    text-align: center;
    border: none;
    height: 40px !important;
    margin-right: 5px;
    width: auto;
}

.amdbtn_red {
    padding: 4px 40px 7px;
    background: #f02828 !important;
    
    font-size: 18px;
    color: #fff !important;
    border-radius: 4px !important;
    text-align: center;
    border: none;
    height: 40px !important;
    margin-right: 5px;
    width: auto;
}

.unit_privacy_sec h1 {
    
    font-size: 24px;
    margin-bottom: 11px;
    margin-top: 7px;
}

.unit_privacy_sec h3 {
    
    font-size: 15px;
    color: #494949;
    margin-top: 12px;
    margin-bottom: 5px;
}

.unit_privacy_sec p {
    color: #0d4875;
    margin-left: 18px;
    font-size: 15px;
}

.unit_privacy_sec h2 {
    color: #0d4875;
    font-size: 19px;
    
    margin-bottom: 9px;
}

.unit_privacy_sec h4 {
    color: #494949;
    font-size: 16px;
    
    margin-bottom: 9px;
    margin-top: 13px;
}


.pvsec_p {
    margin-bottom: 15px;
    color: #0d4875;
    font-size: 15px;
    line-height: 21px;
}

.pv_line {
    line-height: 26px;
}

.temsbld {
    
    margin-right: 2px;
}

.menu_right_link {
    font-size: 14px !important;
    cursor: pointer !important;
    padding: 0px !important;
    
}

.menu_right_link:hover {
    color: #f36b22 !important;
}

.modal-body {
    padding: 22px 30px 20px !important;
}


/* Dashboard CSS */

.dash_bg {
    padding: 13px;
    float: left;
    width: 100%;
}

.dash_boxOne {
    box-shadow: 0px 1px 3px #bebebe;
    -webkit-box-shadow: 0px 1px 3px #bebebe;
    -khtml-box-shadow: 0px 1px 3px #bebebe;
    -moz-box-shadow: 0px 1px 3px #bebebe;
    width: 100%;
    height: 120px;
    padding: 10px;
}

.bkng_count {
    float: left;
    font-size: 30px;
    margin-top: 11px;
    color: #494949;
    
}

.bkng_blueBg {
    border: 2px solid #174d79;
    float: left;
    padding: 10px;
    border-radius: 4px;
    width: 100%;
}

.bkng_icon {
    width: 50px;
    height: 50px;
    background: #f7f7f7;
    position: absolute;
    right: 20px;
    opacity: 0.5;
}

.bkng_time {
    float: left;
    font-size: 12px;
    margin-top: 10px;
    color: #494949;
    width: 100%;
}

.bkng_redBg {
    border: 2px solid #ff0000;
    float: left;
    padding: 10px;
    border-radius: 4px;
}

.bkng_colorRed {
    color: #ff0000 !important;
}

.bkng_pr {
    padding-right: 7px !important;
}

.bkng_pl {
    padding-left: 7px !important;
}

.bkngmian_pr {
    padding-right: 9px !important;
}

.bkngmian_pl {
    padding-left: 9px !important;
}

.bkng_contacts {
    background: #174d79;
    color: #ffffff;
    width: 100%;
    
    font-size: 18px;
    padding: 11px;
    text-align: center;
}

.Booking_Gsec {
    float: left;
    width: 100%;
    padding: 18px;
}

.graph_sec {
    box-shadow: 0px 1px 3px #bebebe;
    -webkit-box-shadow: 0px 1px 3px #bebebe;
    -khtml-box-shadow: 0px 1px 3px #bebebe;
    -moz-box-shadow: 0px 1px 3px #bebebe;
    float: left;
    padding: 17px;
    width: 100%;
}

.bkng_contacts_sec {
    box-shadow: 0px 1px 3px #bebebe;
    -webkit-box-shadow: 0px 1px 3px #bebebe;
    -khtml-box-shadow: 0px 1px 3px #bebebe;
    -moz-box-shadow: 0px 1px 3px #bebebe;
    float: left;
    width: 100%;
    overflow: hidden;
    height: 305px;
    overflow-y: scroll;
    padding: 18px 0px 18px 15px;
    /* overflow: hidden; */
}

.bkng_con_sec {
    padding: 0 10px;
    margin-top: 10px;
    margin-bottom: 5px;
    float: left;
    width: 100%;
}

.left_bkng_c {
    text-align: center;
}

.left_bkng_c {
    color: #fff;
    width: 49%;
    float: left;
    text-align: center;
}

.right_bkng_c {
    color: #fff;
    width: 49%;
    float: right;
    text-align: center;
}

.bkng_NameBG {
    width: 33px;
    height: 33px;
    background: #ff00ff;
    color: #fff;
    border-radius: 200px;
    font-size: 12px;
    line-height: 34px;
    
    float: left;
    text-align: center;
}

.bkng_Name {
    font-size: 14px;
    color: #494949;
    float: left;
    margin-top: 9px;
    margin-left: 8px;
}

.bkng_Desig {
    font-size: 12px;
    color: #767676;
}

.bkng_sep {
    height: .2px;
    float: left;
    width: 100%;
    margin: 6px 0;
    background: #ececec;
}


/* Scrollbar Styling */

::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background-color: #ffffff;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    -webkit-border-radius: 10px;
    border-radius: 10px;
    background: #ddd;
}

.red {
    background: #FF0000 !important;
}

.maroon {
    background: #800000 !important;
}

.green {
    background: #008000 !important;
}

.olive {
    background: #808000 !important;
}

.blue {
    background: #0000FF !important;
}

.navy {
    background: #000080 !important;
}

.purple {
    background: #800080 !important;
}

.fuchsia {
    background: #FF00FF !important;
}


/* Float */

.float_hov {
    display: inline-block;
    transition-duration: 0.3s;
    transition-property: transform;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    transform: translateZ(0);
}

.float_hov:hover {
    transform: translateY(-5px);
}

.SupportTxt {
    position: relative;
    top: -15px;
    border: 1.5px solid #a5a5a5;
    padding: 8px 20px;
    text-align: center;
    border-radius: 4px;
    left: 20px;
    background: #fbfbfb;
    color: #494949;
    cursor: pointer;
    transition: 0.3s;
    -webkit-transition: 0.3s;
}

.Scare {
    width: 26px;
    height: 26px;
    position: relative;
    top: 5px;
    margin-right: 5px;
}

.chartOne {
    min-height: 400px;
    width: 100%;
    height: auto;
    box-shadow: 0px 1px 3px #bebebe;
    -webkit-box-shadow: 0px 1px 3px #bebebe;
    -khtml-box-shadow: 0px 1px 3px #bebebe;
    -moz-box-shadow: 0px 1px 3px #bebebe;
    border-radius: 4px;
    margin-bottom: 30px;
}

.profile-card {
    width: 100%;
    background: #fff;
    padding: 14px 4px;
    border-radius: 4px;
    box-shadow: 0 0 2px 0 #ccc;
    transition: .3s;
    margin-bottom: 15px;
    text-align: center;
    min-height: 187px;
    height: auto;
}

.profile-card:hover {
    transform: scale(1.1);
    box-shadow: 0 0 25px -5px #ccc;
}

.profile-icon {
    height: 80px;
    width: 80px;
    object-fit: cover;
    border-radius: 50%;
}

.profile-name {
    font-size: 12px;
    
    margin: 2px 0 3px 0;
    text-align: center;
    line-height: 14px;
    color: #494949;
}

.profile-position {
    font-size: 12px;
    color: #777;
    letter-spacing: -1px;
}

@media screen and (max-width: 1000px) {
    .profile-name {
        font-size: 18px;
    }
}

@media screen and (max-width: 800px) {
    .profile-card {
        padding: 20px 15px;
    }

    .profile-name {
        font-size: 16px;
    }
}

.profileBgD {
    margin-top: 10px;
    margin-bottom: 0px;
}

.profileDash {
    background: #174d79;
    color: #fff;
    font-size: 11px;
    padding: 2px 20px 3px;
    border-radius: 200px;
}

.ReaseonBook {
    height: 305px;
    width: 100%;
    box-shadow: 0px 1px 3px #bebebe;
    -webkit-box-shadow: 0px 1px 3px #bebebe;
    -khtml-box-shadow: 0px 1px 3px #bebebe;
    -moz-box-shadow: 0px 1px 3px #bebebe;
    border-radius: 4px;
    padding: 20px 23px 10px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.reasonTitle {
    color: #494949;
    font-size: 20px;
    letter-spacing: -1px;
    
    margin-bottom: 9px;
    position: relative;
    padding-left: 37px;
}

.reasonTitle::before {
    content: "";
    background: #174d79;
    position: absolute;
    height: 2px;
    width: 28px;
    top: 11px;
    left: 2px;
}

.mt-20 {
    margin-top: 20px;
}

.single_feature {
    margin-top: 10px;
    margin-bottom: 8px;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    transition: 0.3s;
    width: 100%;

}
.single_feature i {
    border: 1px solid #e8e8e9;
    border-radius: 50%;
    font-size: 24px;
    /* height: 60px; */
    line-height: 60px;
    position: relative;
    text-align: center;
    transition: all 0.3s ease 0s;
    width: 60px;
    z-index: 3;
}

.feature_icon {
    background: #fff;
    width: 65px;
    margin: 0 auto;
    margin-top: -38px;
}

.bkng_Name:hover {
    color: #f36b22;
    cursor: pointer;
}

.feature_icon i {
    color: #494949 !important;
}


.single_feature h3 {
    text-transform: capitalize;
    font-size: 12px;
    font-weight: 400;
    margin-top: 4px;
    margin-bottom: 0;
}

.stfBgseLeft {
    padding: 11px 14px;
        margin-right: 5px;
    background: #f4f4f4;
    text-align: left;
}

.single_feature h5 {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 17px;
    text-align: left;
}

.stfBgseRIght {
    padding: 11px 14px;
        margin-right: 5px;
    background: #f4f4f4;
    text-align: left;
}

.ReasonBkTitle {
    padding: 5px 0px 5px 0px;
}

.single_feature span {
    border-bottom: 1px dashed #ccc;
    display: block;
    margin: 15px auto 10px;
    width: 80px;
}

.single_feature p {
    margin-bottom: 0;
    font-size: 18px;
    margin-top: 7px;
    font-weight: 600;
    
}

.exploreBtn.bold {
    width: 8% !important;
}

.bk_details {
    width: 13% !important;
}

.Booking_Gsec::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #F5F5F5;
}

.Booking_Gsec::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
}

.Booking_Gsec::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #e3e3e3;
}

.widthBkSh {

}

.UnitHradTitle {
    font-size: 20px;
    color: #494949;
    margin: 0;
}
.widthBkSh img {
    width: 42px !important;
    height: 42px !important;
    margin-right: 5px;
    border: 1px solid #fff;
    box-shadow: 0px 2px 3px #969696;
}

.FooterBottm {
    display: inline-block;
    width: 103%;
    height: 42px;
    background: #f4f4f4;
    padding: 11px 60px;
    text-align: center;
    font-size: 12px;
    margin-top: 12px;
    margin-left: -14px;
}

.footCopy1 {
    border-right: 1px solid #c3c3c3;
    padding-right: 20px;
    margin-right: 20px;
}

.footCopy2 {
    border-right: 1px solid #c3c3c3;
    padding-right: 20px;
    margin-right: 13px;
}

.footCopy3 {
    margin-right: 20px;
}

@media (min-width: 1541px) {

    .Booking_Gsec {
        float: left;
        width: 100%;
        padding: 18px;
    }

    #ManageScroll {
        overflow: hidden;
        height: 1075px;
        overflow-y: scroll;
    }
}

@media (max-width: 1540px) {
    #vertical-scrollbar-demo {
        overflow: hidden;
        height: 925px;
        overflow-y: scroll;
    }

    #ManageScroll {
        overflow: hidden;
        height: 845px;
        overflow-y: scroll;
    }
}

@media screen and (min-device-width: 100px) and (max-device-width: 1366px) {
    #vertical-scrollbar-demo {
        overflow: hidden;
        height: 1020px;
        overflow-y: scroll;
    }

    .Booking_Gsec {
        float: left;
        width: 100%;
        overflow: hidden;
        height: 960px;
        overflow-y: scroll;
        padding: 18px;
    }

    #ManageScroll {
        overflow: hidden;
        height: 1015px;
        overflow-y: scroll;
    }
}

.ShReset {

}

@media (min-width: 1280px) and (max-width: 1365px) {
    .Booking_Gsec {
        float: left;
        width: 100%;
        overflow: hidden;
        height: 770px;
        overflow-y: scroll;
        padding: 18px;
    }
}

@media (min-width: 1024px) and (max-width: 1279px) {
    /* CSS */
}

@media (min-width: 768px) and (max-width: 1024px) {
    /* CSS */
}

@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
    /* CSS */
}


.w100 {
    width: 100% !important;
}

.pl-0 {
    padding-left: 0 !important
}

.pr-0 {
    padding-right: 0 !important
}

.sl_box {
    float: left;
    width: 100%;
    background: #fff;
    border: none !important;
    border-radius: 5px;
    padding: 5px;
    height: 34px;
    resize: none;
    font-family: "Nunito", sans-serif;
    font-size: 12px;
}

.bx_margin {
    margin: 3px;
    margin-bottom: 10px;
    float: left;
}

.cl_bx_padd {
    padding-right: 0px !important;
    display: flex;
        padding-left: 10px !important;
}

.add_note {
    width: 100%;
}

.add_note input {
    height: 101px;
}

.CLTitle {
    color: #fff;
    font-weight: 700;
    font-size: 21px !important;
    line-height: 47px;
    position: relative;
    top: 9px;
    background: #212121;
    display: inline-block;
    margin-bottom: 15px;
    width: 100%;
    text-align: center;
}

.prsave {
    padding-right: 1px !important;
}

/* ****************   Workflow CSS ************** */

.wkflow h4 {
    margin: 15px 0;
    font-size: 20px;
    
    margin-top: 22px;
}

.approval-step label {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 500;
}

.WfBg {
border: 1px solid #e8e8e8;
    padding: 13px 17px 20px;
}

.ManFiled {
    margin-top: 26px;
    padding-left: 20px;
}

.wk_right_btn {
    background: #e2e2e2;
    position: absolute;
    right: -10px;
    margin-top: -24px;
    border-radius: 200px;
    width: 22px;
    text-align: center;
    height: 22px;
    font-size: 14px;
}

.wk_right_btn:hover {
    background: #494949;
    
}

.wk_right button {
    margin: 10px;
}

.approval-step {
    margin-bottom: 15px;
}

.AddBtnNew {
    text-align: right;
}

.AddBtnNew button {
    background: #212121;
    border: none;
    padding: 7px 15px;
    
    font-size: 14px;
}

.AddBtnNew button:hover {
    background: #212121;
}

.createBtn button {
    float: right;
    margin-left: 8px;
}

.createBtn button:hover {
    background: #268126;
}

.FlowBg {

}

.FlowBg label {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 500;
}

.wkflow {

}

.wkflow h2 {

}

.FlowBtn {
    background: #212121;
    border: none;
    padding: 8px 15px;
    
    font-size: 14px;
    color: #fff;
    border-radius: 4px;
}

.FlowBtn:hover {
    background: #212121;
    color: #fff;
    text-decoration: none;
}

.AddBtRight {
    text-align: right;
    margin-top: 6px;
}

.WkflowTable th {
    background: #d2e5f5;
    padding: 9px !important;
    border: 1px solid #bfd7eb !important;
    
    font-size: 14px !important;
    vertical-align: middle;
}

.WkflowTable td {
    font-size: 14px !important;
    padding: 7px 9px !important;
    vertical-align: middle !important;
    border: 1px solid #e9e9e9 !important;
}

.flowlast {
    width: 15%;
    text-align: right !important;
}

.WkflowTable tr:nth-child(even) {
    background-color: #f9f9f9;
}

.CltProfile img {
    width: 45px;
    height: 45px;
    margin-right: 9px;
    border-radius: 200px;
    margin-top: 0px;
}

.left_section_top_imgbox {

}

.BgFIrstBx {
    padding: 15px;
}

.table thead tr {
    background: #f4f4f4 !important;
}

.UnInfotable tr:nth-child(even) {
    background-color: #f5f5f5;
}

.UnInfotable td,
th {
    vertical-align: middle !important;
    font-size: 14px;
    
}

.TitleBred {
padding: 10px 15px;
    margin-bottom: 10px;
    background: #ededed;
}

a:focus {
    color: #fff;
    text-decoration: none !important;
}

.TitleBred h1 {
    font-size: 26px;
    color: #494949;
    font-weight: 700;
    margin: 0;
}

.hh-grayBox {
    background-color: #F8F8F8;
    margin-bottom: 20px;
    padding: 35px;
    margin-top: 20px;
}

.pt45 {
    padding-top: 45px;
}

.order-tracking {
    text-align: center;
    width: 190px;
    position: relative;
    display: block;
    margin-bottom: 40px;
    float: left;
}

.order-tracking .is-complete {
    display: block;
    position: relative;
    border-radius: 50%;
    height: 55px;
    width: 55px;
    background-color: #e0dede;
    margin: 0 auto;
    transition: background 0.25s linear;
    -webkit-transition: background 0.25s linear;
    z-index: 2;
    font-size: 18px;
    line-height: 60px;
    padding-top: 20px;
}

.order-tracking .is-complete:after {
    display: block;
    position: absolute;
    content: '';
    height: 14px;
    width: 7px;
    top: -2px;
    bottom: 0;
    left: 5px;
    margin: auto 0;
    border: 0px solid #AFAFAF;
    border-width: 0px 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
}

.order-tracking.completed .is-complete {
    border-color: #e0dede;
    border-width: 0px;
    background-color: #e0dede;
}

.order-tracking.declined .is-complete {
    border-color: #d9534f;
    border-width: 0px;
    background-color: #d9534f;
}

.ConfStaff {
    font-size: 14px;
    
    margin-top: 7px;
    margin-bottom: 0px;
    color: #494949;
}

.CompleteStatus {
    color: #494949;
    font-size: 14px;
    border: 1px solid #737373;
    border-radius: 36px;
    width: 90%;
    margin: 8px auto;
    padding: 4px 0;
}

.CancelStatus {
    color: #494949;
    font-size: 14px;
    border: 1px solid #c1c1c1;
    border-radius: 36px;
    width: 90%;
    margin: 8px auto;
    padding: 3px 0;
}

.PendingStatus {
    color: #494949;
    font-size: 14px;
    border: 1px solid #737373;
    border-radius: 36px;
    width: 90%;
    margin: 8px auto;
    padding: 4px 0;
}

.DateStat {
    font-size: 12px;
    margin-top: 3px;
}

.order-tracking p {
    color: #A4A4A4;
    font-size: 16px;
    margin-top: 8px;
    margin-bottom: 0;
    line-height: 20px;
}

.order-tracking p span {
    font-size: 14px;
}

.order-tracking.completed p {
    color: #000;
}

.order-tracking::before {
    content: '';
    display: block;
    height: 3px;
    width: calc(100% - -20px);
    background-color: #A4A4A4;
    top: 30px;
    position: absolute;
    left: calc(-65% + 20px);
    z-index: 0;
}

.order-tracking:first-child:before {
    display: none;
}

.order-tracking.completed:before {
    background-color: #e0dede;
}

.order-tracking.declined:before {
    background-color: #f7be16;
}

.table thead tr {
    background: #f4f4f4 !important;
}



.MianMenuIco {
    font-size: 18px !important;
    margin-right: 10px;
    float: left;
    color: #494949;
}


.modal-header .close {
    color: #a3a3a3 !important;
}

.btwkFlow {
    margin-top: 13px;
    padding: 4px 8px !important;
    margin-right: 1px;
    margin-left: 1px;
    font-size: 13px !important;
}

.ScrollRow {
    display: flex;
    align-items: center;
    width: 100% !important;
    justify-content: center;
}

b.ConfStaff {
    margin-top: 31px;
    font-size: 17px;
    position: relative;
    top: 7px;
}

.MarqTxt {
    color: #fff;
    
    background: #f36b22;
    padding: 3px 9px 20px;
    font-size: 12px;
}


.Wktooltip {
    position: relative;
    display: inline-block;
    padding-left: 4px;
    text-align: center;
    cursor: pointer;
}



.Wktooltip i {
    color: #494949 !important;
    font-size: 16px;
}

.Wktooltip .tooltiptext {
    visibility: hidden;
    width: 360px;
    background-color: #494949;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 15px;
    position: absolute;
    z-index: 100;
    bottom: 100%;
    left: 50%;
    margin-left: -90px;
    margin-bottom: 10px;
}

.Wktooltip:hover .tooltiptext {
    visibility: visible;
}

.flexRow {
    overflow: hidden;
    width: 100%;
}

.RolList tr td {
    text-align: left !important;
}

.RolList tr td:last-child {
    text-align: right !important;
}

.RolList tr th:last-child {
    text-align: right !important;
}

.UntNmBgTit {
    margin-bottom: 7px;
    font-weight: 600;
    font-size: 18px;
    padding-bottom: 3px;
    
    border-bottom: 1px solid #b3b3b3;
}

.UntBg {
    border-radius: 8px;
}

.no-margin {
    margin: 0 !important;
}

.UntNmBg {
    margin-bottom: 1px;
    font-size: 14px;
    color: #494949;
    text-align: left;
    
    padding: 5px 0;
    border-bottom: 1px solid #e8e8e8;
}

.NesShiftTitle {
    background: #f2f8ff;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dde9f7;
    margin: 20px;
    margin-top: 160px;
    font-size: 20px;
    
    color: #494949;
}

.UntNmBg span {
    font-weight: 500;
}

.Wkpad .col-md-3 {
    padding: 0 7px;
}

.bk_amnded button {
    background: #F44336 !important;
}

.graphBg {
    padding: 10px 20px;
}

.GpTitle {
    margin: 0;
    text-align: center;
    margin-bottom: 7px;
    background: #ededed;
    padding: 8px;
}

.bdgBg {
margin: 0;
    margin-bottom: 10px;
    background: #ffffff;
    padding: 7px;
    border: 1px solid #e1e1e1;
    height: 46px;
    font-size: 16px;
}

.bk_amnded button i {
    color: #fff;
}

.BtProgress i {
    color: #a3a3a3;
}

.flowlast a i {
    color: #fff;
}

.remove-step i {
    color: #828282;
}
.remove-step i:hover {
    color: #fff !important;
}

.view-btn i {
    color: #fff;
}

.active i {
    color: #fff;
}

.TpCount {
    float: right;
    margin-top: -30px;
    border: 1px solid #174d79;
    padding: 2px 18px;
    border-radius: 48px;
}

.HCctagoryBg1 {
    padding: 15px 1px;
    border: 2px dotted #4084f4;
    text-align: center;
    background: #e3eeff;
    color: #4084f4;
    border-radius: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.HCctagoryBg2 {
    padding: 15px 1px;
    border: 2px dotted #32a953;
    text-align: center;
    background: #ebfff1;
    color: #fff;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    border-radius: 10px;
}

.HCctagoryBg3 {
    padding: 15px 1px;
    border: 2px dotted #e94335;
    text-align: center;
    background: #fff1f0;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #fff;
    border-radius: 10px;
}

.HccaTitle {
    margin: 0;
    
    font-size: 30px;
    border-radius: 9px;
    color: #494949;
}

.HCctagory h5 {
    padding: 3px 15px;
    border: 1px solid #8c8c8c;
    margin: 0 4px;
    border-radius: 1px;
    margin-bottom: 5px;
    font-size: 12px;
    text-align: left;
}

.HCctagory h5:last-child {
    margin-bottom: 0px;
}

.LoginCltBg {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.logSign {
    width: 280px;
    margin: 0 auto;
}

.LoginMainBg {
    width: 390px;
    margin: 0 auto;
    background: #f4f4f4;
    padding: 37px 33px 45px;
}

.hrLine1 {
    width: 100%;
    height: 1px;
    background: #ddd;
    margin: 16px 0 30px;
}

h1.TitleLogin {
    text-align: center;
    font-size: 20px;
    margin-bottom: 23px;
    
}

.LgInp {
    padding: 12px;
    background: #fff !important;
    margin-bottom: 14px;
    font-size: 15px;
    border: none;
}

.FtLogin {
    position: absolute;
    bottom: 0;
    padding: 11px 60px;
    height: auto;
    font-size: 12px;
    text-align: center;
    width: 100%;
    color: #fff;
}

.FtLogin .footCopy1 {
    color: #fff;
}

.FtLogin .footCopy2 {
    color: #fff;
}

.FtLogin .footCopy3 {
    color: #fff;
}

@media (min-width: 1541px) {

    .HccaTitle {
        font-size: 42px;
    }
    .HCctagory h5 {
        font-size: 15px;
        padding: 5px 15px;
    }

}

select {
    border: none !important;
    background: #fff !important;
    box-shadow: none !important;
}

.form-control {
    padding: 6px 15px 6px 5px;
}

#vertical-scrollbar-demo {
    height: auto !important;
    padding-left: 250px;
    width: 100%;
    overflow: auto;
    min-height: calc(100vh - 50px);
    overflow-x: hidden;
}

.HeadFlex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remiDivSt {
    margin: 0 !IMPORTANT;
}

.remiDivSt div {
    padding: 4px 0;
    margin-top: 5px;
    font-size: 12px;
    border-bottom: 1px solid #e3e3e3;
}

.form-control {
    padding: 3px 16px 4px 5px !important;
}

.TitleBred h2 {
    font-size: 26px;
    color: #494949;
    font-weight: 700;
    margin: 0;
}

i.fa.fa-edit.edit-todos {
    font-size: 14px !important;
    color: #7b7b7b;
}

i.fa.fa-trash.confirmation {
    font-size: 15px !important;
    color: #7b7b7b;
}

i.fa.fa-edit.edit_reminders {
    font-size: 14px !important;
    color: #7b7b7b;
}

i.fa.fa-trash.remove_reminders {
    font-size: 15px !important;
    color: #7b7b7b;
}
.left_section_head{
    height: 80vh;
}
.LeftFooter{
    height: 20vh;
}
.mr-0{
    margin-right: 0 !important;
}
.pl-0{
    padding-left: 0 !important;
}
.Dash_Fluid_Main{
display: flex;
    padding-left: 15px;
    padding-right: 15px;
}
.BudgDashBox {
    width: 100%;
    margin-right: 28px;
}
.Container_Fluid_Main {
padding: 0 25px;
    padding-right: 19px;
}
.budgetBox .title {
    font-weight: normal;
    color: #606472;
    font-size: 19px;
}
.budgetBox .amt {
    font-weight: normal;
    font-size: 30px;
    margin-bottom: 10px;
    display: inline-block;
}
.GpBoxAuto {

}

.GpBox {
    padding: 15px 18px;
    margin-bottom: 15px;
    height: auto;
    background: #fdfdfd;
    border: 1px solid #f8f6f6;
}
.GpBoxHeading{
    height: auto;
    color: #5e5e5e !important;
    padding-bottom: 10px;
    border-bottom: 1px solid #c5c5c5;
}

.GpBoxtwo {
    padding: 18px 20px 0;
    margin-bottom: 15px;
    min-height: 640px;
    border-radius: 9px;
    height: auto;
    background: #fdfdfd;
    border: 1px solid #f8f6f6;
}
.ClitBdBg .title span {
    font-size: 22px;
}

.greenFont {
    color: #51b44f;
}

.redFont {
    color: #e64b30 !important;
}

.greenFont span {
    color: #51b44f;
    font-weight: 600
}

.redFont span {
    color: #e64b30 !important;
    
}

.greenFontBold {
    font-weight: 700;
    color: #51b44f;
}

.no-bdr{
    border: none !important;
}
.BudTitle {
    margin-top: 5px;
    margin-bottom: 20px;
    font-size: 21px;
    color: #494949 !important;
    padding-bottom: 10px;
    border-bottom: 1px solid #dbd7d7;
}

.pagination-link {
    margin-right: 8px;
    border: 1px solid #337ab7;
    padding: 5px 12px;
    
    text-align: left;
}

.dashboard-todos {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.addTodoBtn {
    background: #28a745;
    float: right;
    font-size: 11px;
    padding: 5px 11px;
    color: #fff;
    margin-top: 5px;
}

.addTodoBtn span i {
    
}

.new_todos {
    border: 1px solid #d2cfcf;
    border-radius: 4px;
    font-size: 15px;
    padding: 3px;
    padding-left: 7px;
    height: 31px;
}


.NtBox {
    padding: 20px 20px;
    margin-bottom: 15px;
    height: 365px !important;
    background: #fdfdfd;
    border: 1px solid #f8f6f6;
        overflow-y: scroll;
}

.dashboard-reminder {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.headRem {
    font-size: 18px;
    font-weight: 500;
    margin-top: -12px;
}

.smartbg {
    background: #f2f8ff;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 10px;
    margin-top: 0%;
    border: 1px solid #dde9f7;
}

#headsm {
    margin: 0px;
    margin-top: 7px;
    text-transform: uppercase;
    font-weight: 700;
    color: #494949;
}

.CltProfile img {
    width: 45px;
    height: 45px;
    margin-right: 9px;
    border-radius: 200px;
    margin-top: 0px;
}


.ReasonBkTitle {
    margin: 0px;
    text-transform: uppercase;
    font-weight: 700;
    color: #494949;
}

.table thead tr {
    background: #f4f4f4 !important;
}
.table thead tr {
    background: #212121 !important;
    color: #c2c2c2 !important;
}
.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd;
    color: #494949 !important;
    background: #ededed;
}
.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
    padding: 6px !important;
}
.table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border: none !important;
        vertical-align: middle;
}
.table tr:nth-child(even) {
    background-color: #fff !important;
}
tfoot {
    background: #ececec;
}
.leftLogo{
    width: 245px;
    margin-bottom: 21px;
}
details summary {
    font-size: 15px;
    padding: 13px 0px;
    cursor: pointer;
    list-style: none;
    color: #d2d2d2;
}
details summary i {
    color: #d2d2d2;
    margin-right: 7px;
        font-size: 18px !important;
}

    details[open] summary:after {
        transform: rotate(45deg);
    }

    details summary:after {
    content: "+";
    color: #7c7c7c;
    position: absolute;
    font-size: 18px;
    line-height: 0;
    margin-top: 9px;
    right: 29px;
    font-weight: 500;
    transform-origin: center;
    transition: 200ms linear;
    }

    details[open] summary~* {
        animation: open 0.3s ease-in-out;
        padding: 13px 43px !important;
    }

    @keyframes open {
        0% { opacity: 1; }
        100% { opacity: 1; }
    }

    details summary::-webkit-details-marker {
        display: none;
    }

    .SubMenuSec summary img {
        max-width: 32px;
        margin-right: 16px;
    }

    .nav_area ul li .SubMenuNav {
    padding-left: 66px !important;
    font-size: 14px;
    padding: 6px 15pX !important;
    background: #000000;
    }

    .loader {
        top: 0;
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        position: fixed;
        overflow: hidden;
        z-index: 100000;
        text-align: center;
        background: rgba(255, 255, 255, 0.9);
    }

    .spinner {
        width: 80px;
        height: 80px;
        margin: 0 auto;
        border: 5px solid #efefef;
        border-top-color: #1f73b9;
        border-radius: 100px;
        animation: spin 1s infinite linear;
    }

    @keyframes spin {
        100% {
            transform: rotate(360deg);
        }
    }

    .loader-icon {
        position: absolute;
        top: calc(50% - 50px);
        left: calc(50% - 10px);
    }
    .LeftMenuTopSection{
        height: 85vh;
        padding: 17px 30px;
    }
.left_section_bottom {
    height: 15vh;
    padding: 20px;
}
.HelpModalSec{
    background: url(../images/help.svg) no-repeat;
    width: 100%;
    height: 38px;
}
.HelpModalSec span{
    position: relative;
    top: 9px;
    left: 37px;
    color: #494949;
    font-size: 14px;
}
.powered {
    font-size: 12px;
    margin-top: 20px;
        color: #7c7c7c;
            text-align: center;
}
.powered a {
    color: #fff;;
}
.powered a:hover{
    color: #7c7c7c;;
}
.ModaHelp {
    width: 1160px !important;
}
.ProfPic {
    float: left;
    border-radius: 5px;
    border: 1px solid #ddd;
}
.ProfTitles {
    color: #595959 !important;
}
.ProfLeft {
    padding: 0px !important;
}
.ProfTitles h3 {
    margin: 0;
    font-size: 18px;
    margin-bottom: 10px;
    margin-top: 5px;
    color: #595959;
}
.ProfTitles div {
    color: #595959;
    margin-top: 4px;
}
.HelpTitle {
    color: #595959;
    
    font-size: 24px;
    padding-bottom: 4px;
    margin-bottom: 23px;
    border-bottom: 1px solid #ddd;
}
.ProfCtBg {
    background: #f4f4f4;
    padding: 12px;
    border-radius: 5px;
        margin-bottom: 10px;
}
.FooterHelpMOdal{
    text-align: right;
    margin: 5px 16px;
    padding-top: 11px;
    padding-bottom: 18px;
}
.PicOpa{
    opacity: 0.5;
}
.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 10px;
}
.mt30{
    margin-top: 30px !important;
}
.mt25{
    margin-top: 25px !important;
}
.mt20{
    margin-top: 20px !important;
}
.mt15{
    margin-top: 15px !important;
}
.mt10{
    margin-top: 10px !important;
}
.mt5{
    margin-top: 5px !important;
}
.mt0{
    margin-top: 0px !important;
}
.GpBoxHeading h4 {
    font-size: 25px;
    color: #494949;
    margin: 0;
}
.badge {
    padding: 7px 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    background: #faf1f1;
    color: #494949;
    border-radius: 0;
}
.btn-success {
    color: #494949;
    background-color: #e9f7e9;
    border-color: #e0f5e0;
}
.UntLabel {
    float: left;
    margin-left: 17px;
    position: relative;
    top: 4px;
}
.Main_Btn {
    font-size: 15px;
    background: #212121;
    color: #ffffff;
    padding: 9px 40px;
    border-radius: 2px;
    font-weight: 600;
    border: 1px solid #212121;
}
.Main_Btn:hover {
    background: #fff;
    color: #494949;
    border: 1px solid #212121;
    text-decoration: none;
}
.BookingModalSection{
    width: 90%;
    margin: 0 auto;
}
.NewModalContent{
     
    padding: 30px;
}
.UrgBkng{
    margin-left: 20px;
}
.NewRowpAdd {
    padding: 9px 20px;
    background: #e5e5e5;
    margin-bottom: 10px;
    border: 1px solid #dedede;
}
.BxUrgent {
    position: relative;
    margin: 18px 0px 0 15px;
}
.NewRowDynamic{
    padding: 18px 0px 7px;
    background: #e5e5e5 !important;
    margin-bottom: 20px;
    border: 1px solid #dedede;
}
.ClsBtnAddShift{
    padding: 4px;
    cursor: pointer;
    background: none;
    color: #ffffff;
    position: absolute;
    font-size: 11px;
    width: 16px;
    height: 17px;
    line-height: 10px;
    right: -7px;
    text-align: center;
    top: -23px;
    background: #ff0000;
}
.Mfooter {
    text-align: right;
}
.MainBoxRow {
    background: #f4f4f4;
        padding: 15px;
}
.btn-primary {
    background-color: #212121 !important;
    border: 1px solid #212121 !important;
    color: #e1e1e1 !important;
    padding: 6px 15px !important;
    font-size: 14px !important;
    font-weight: 500;
    border-radius: 0 !important;
}
.btn-primary:hover {
    background-color: #fff !important;
    border: 1px solid #212121 !important;
    color: #494949 !important;
}
.btn-primary-sm {
    color: #fff;
    background-color: #212121 !important;
    border: 1px solid #212121 !important;
    color: #c2c2c2 !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
        font-weight: 500;
    border-radius: 0 !important;
}
.btn-primary-sm:hover {
    background-color: #fff !important;
    border: 1px solid #212121 !important;
    color: #494949 !important;
}
.btn-primary-ico {
    color: #fff;
    background-color: #212121 !important;
    border: 1px solid #212121 !important;
    color: #c2c2c2 !important;
    padding: 4px 6px !important;
    font-size: 14px !important;
    font-weight: 500;
    border-radius: 0 !important;
    height: 34px;
    width: 39px;
}
.btn-primary-ico:hover {
    background-color: #fff !important;
    border: 1px solid #212121 !important;
    color: #494949 !important;
}
.btn-two{
    background: #cac8c8;
    color:#000 !important;
    padding: 6px 15px !important;
    font-weight: 500;
    font-size: 14px !important;
    border-radius: 0 !important;
}
.btn-two:hover{
    background: #000;
    color: #fff !important;
}
.btn-two-sm{
    background: #cac8c8;
    color:#000 !important;
    padding: 4px 8px !important;
    font-weight: 500;
    font-size: 12px !important;
    border-radius: 0 !important;
}
.btn-two-sm:hover{
    background: #000;
    color: #fff !important;
}
.form-control {
    border: none;
}
.btn-three{ 
    background: #fff !important;
    color: #494949 !important;
    border: 1px solid #979797 !important;
    padding: 6px 15px !important;
    font-size: 14px !important;
    font-weight: 500;
    border-radius: 0 !important;
}
.btn-three:hover{
    background: #000 !important;
    color: #fff !important;
}
.btn-three-sm{ 
    background: #fff !important;
    color: #494949 !important;
    border: 1px solid #d4d4d4 !important;
    padding: 3px 8px !important;
    font-size: 12px !important;
    font-weight: 500;
    border-radius: 0 !important;
}
.btn-three-sm:hover{
    background: #000 !important;
    color: #fff !important;
    border: 1px solid #000 !important;
}
.btn-three-sm:hover i{
    color: #fff !important;
}

.btn-three-ico{ 
    background: #fff !important;
    color: #494949 !important;
    border: 1px solid #d4d4d4 !important;
    padding: 3px 8px !important;
    font-size: 14px !important;
    font-weight: 500;
    border-radius: 0 !important;
    height: 34px;
    width: 39px;
}
.btn-three-ico:hover{
    background: #000 !important;
    color: #fff !important;
    border: 1px solid #000 !important;
}
.btn-three-ico:hover i{
    color: #fff !important;
}



.mt7{
    margin-top: 7px !important;
}


.btn {
    border-radius: 0px !important;
}
.ArchBtn{
    position: relative;
    margin-right: 9px;
    padding: 8px 15px !important;
}
.SecTItleBrder {
    padding-bottom: 15px;
    border-bottom: 1px solid #c5c5c5;
}
.pb0{
    padding-bottom: 0 !important;
}
.ml0{
    margin-left: 0 !important;
}
.mr0{
    margin-right: 0 !important;
}
.pl0{
    padding-left: 0 !important;
}
.pr0{
    padding-right: 0 !important;
}
.mb0{
    margin-bottom: 0 !important;
}
.mb5{
    margin-bottom: 5px !important;
}
.mb10{
    margin-bottom: 10px !important;
}
.mb15{
    margin-bottom: 15px !important;
}
.mb20{
    margin-bottom: 20px !important;
}
.mb25{
    margin-bottom: 25px !important;
}
.mb30{
    margin-bottom: 30px !important;
}
.mb35{
    margin-bottom: 35px !important;
}
.mb40{
    margin-bottom: 40px !important;
}
.mb45{
    margin-bottom: 45px !important;
}
.table-striped>tbody>tr:nth-of-type(even) {
    background-color: #f9f9f9 ! IMPORTANT;
}
.table>thead>tr>th {
    font-weight: 600;
}
.badge-menu{
    background: #ddd;
    font-size: 10px;
    padding: 1px 3px;
    border-radius: 200px;
    color: #000;
    contain-intrinsic-block-size: auto 100px;
}
table.table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
    box-shadow: none !important;
        vertical-align: middle;
}
.Container_Datatable {
    padding: 0px 4px 0 0  px;
}
.DataAlign tr td:first-child{
       text-align: left !important;
}
.DataAlign tr td:last-child{
       text-align: right !important;
}
.DataAlign tr td{
       vertical-align: middle !important;
}
.RoleDataBg{
    background: #fff !important;
    padding: 0;
}
.RolListTable thead tr {
    background: #212121 !important;
    color: #7c7c7c !important;
}
.RolListTable tr td {
   vertical-align: middle;
}
div.dt-container div.dt-layout-row div.dt-layout-cell
 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.pagination>.disabled>a, .pagination>.disabled>a:focus, .pagination>.disabled>a:hover, .pagination>.disabled>span, .pagination>.disabled>span:focus, .pagination>.disabled>span:hover {
    border-color: #ddd;
}
.pagination>li>a, .pagination>li>span{
    background: none;
}
.flowlast .btn-three-sm i {
    color: #494949;
    position: relative;
    top: 2px;
    left: 1px;
}
.pagination>li:first-child>a, .pagination>li:first-child>span {
    background: none !important;
}
.pagination>li:last-child>a, .pagination>li:last-child>span {
    background: none !important;
}
.ArchBtn:focus {
    color: #494949 !important;
}
.DataLogMargin{
    margin-left:15px !important;
    margin-right:15px !important;
}
.TitlePosition{
    position: relative;
    top:-4px;
}
.headTitleBtm{
    margin-bottom: 20px;
    margin-top: 20px;
        display: flex;
    align-items: center;
}

element.style {
    opacity: 0.999987;
}
.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
    width: 400px;
    position: absolute;
    right: 0;
    top: 10px;
}
.BugTable{
    float: left;
    margin-bottom: 0;
    font-size: 18px;
    margin-top: 4px;
}
.BugInp{
    float: left !important;
    width: 300px;
    border: 1px solid #ddd;
    box-shadow: none;
    margin-left: 15px;
}
.AlertBug {
    padding: 8px 16px 11px;
    margin-top: 42px;
    background: #fff;
    border: none;
    margin-bottom: 0 !important;
}
.InpBudget {
    border: 1px solid #ddd;
    box-shadow: none;
}
.form-control{
    height: 34px;
}
div.dt-container div.dt-layout-row {
    width: auto;
    margin-left: 25px !important;
    margin-right: 18px !important;
}
.AlertBug h6 {
    font-size: 15px;
    color: #494949;
}
.BudCount{
    font-size: 18px;
    font-weight: 700;
}
.BtToolTip {
    display: block;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    float: left;
    background: none;
        font-size: 16px;
}
.MyToolTip {
position: absolute;
    color: #494949;
    background: #fff;
    width: 220px;
    font-size: 13px;
    padding: 8px;
    transition: 0.3s;
    opacity: 0;
    border: 1px solid #ddd;
    z-index: 10000;
}
.BtToolTip:hover {
    overflow: visible;
}
.BtToolTip:hover span {
    opacity: 1;
}
.MyToolTip::after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
}
.BtToolTip[data-position = "top"] span {
    top: -9px;
    left: -10px;
}
/* .BtToolTip[data-position = "top"] span::after {
border-top-color: black;
    left: 8px;
    bottom: -18px;
} */
.BtToolTip[data-position = "left"] span {
    top: 10px;
    left: -150px;
}
.BtToolTip[data-position = "left"] span::after {
    border-left-color: black;
    right: -20px;
    bottom: 5px;
}
.BtToolTip[data-position = "bottom"] span {
    bottom: -45px;
    left: -10px;
}
.BtToolTip[data-position = "bottom"] span::after {
    border-bottom-color: black;
    left: 50px;
    top: -20px;
}
.BtToolTip[data-position = "right"] span {
    top: 10px;
    right: -150px;
}
.BtToolTip[data-position = "right"] span::after {
    border-right-color: black;
    left: -20px;
    bottom: 5px;
}
.EmpNote{
    float: left;
    margin: 2px;
}
.BudEditInp {
    background: #fff !important;
    border: 1px solid #ddd;
    box-shadow: none;
}
.budgetTble td, th {
    vertical-align: middle !important;
    font-size: 13px;
}
.budgetTble th {
    padding: 11px 5px !important;   
}
.btn-primary i{
    color: #e1e1e1;
}
.btn-primary:hover i{
    color: #494949;
}
.btn-two i{
    
}
.btn-two:hover i{
    color: #fff;
}
.btn-three i{
    
}
.btn-three:hover i{
    color: #ffffff;
}
.modal-header {
    background: #e8e7e7;
    color: #494949;
    padding: 13px 30px;
}
.modal-title {
    font-size: 25px;
}
.table>thead>tr>th {
padding: 10px 6px !important;
}
#weeklyBudgetsTotalsRow th{
    font-weight: 700;
}

.select2-container--default .select2-selection--single {
    background-color: #fff !important;
    border: none !important;
    border-radius: 0 !important;
    height: 34px !important;
}
.form-control {
    border-radius: 0 !important;
        background: #fff !important;
}
.InpPAss{
        float: left;
    width: 100%;
    background: #fff;
    border-radius: 0;
    padding: 5px 10px;
    height: 34px;
    resize: none;
    border: 1px solid #ebebeb;
    font-family: "Nunito", sans-serif;
    font-size: 12px;
}
a {
    color: #494949;
    text-decoration: none;
}
.ActBtn{
margin: 0;
    padding: 16px;
    background: #ffffff;
    text-align: right;
        padding-right: 0;
}
.AutMar{
    margin: 2px;
}
a:focus, a:hover {
    color: #23527c;
    text-decoration: none;
}
.btnpm{
    padding: 5px 28px !important;
    margin: 0 2px;
}
.alert-info{
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}
.btnAdNew:hover{
    background: #000000 !important;
    color: #fff !important;
}
.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: #666768 !important;
}
.table-responsive {
    min-height: .01%;
    overflow-x: hidden;
}
.tmsBtn {
    background: #e1e1e1;
    padding: 6px 14px;
    color: #9c9797;
}
.tmsBtnActive {
    background-color: #212121;
    color: #d8d8d8 !important;
}
.btn-three-sm i {
    color: #7b7b7b;
}
.TsbtnApproveActive {
    background: #5f5f5f !important;
    border: 1px solid #5f5f5f !important;
}   
.TsbtnApproveActive i {
    color: #fff !important;
}