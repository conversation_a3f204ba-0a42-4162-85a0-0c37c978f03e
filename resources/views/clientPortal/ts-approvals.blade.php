@extends('common.layout')

@section('styles')
<link rel="stylesheet" href="{{ asset('unit-portal/css/managementReports.css') }}">
<link rel="stylesheet" href="{{ asset('unit-portal/css/daterangepicker.css') }}">
<style>
    table {
        background: white;
        border: 1px solid grey;
        border-collapse: collapse;
    }

    table thead th,
    table tfoot th {
        background: rgba(0, 0, 0, .1);
    }

    table caption {
        padding: .5em;
    }

    table th,
    table td {
        padding: .5em;
        border: 1px solid lightgrey;
    }
</style>
@endsection
@section('content')


<div class="Container_Fluid_Main">
    <div class="row headTitleBtm">
        <div class="col-sm-12">
            <div class="GpBoxHeading no-bdr pb0">
                <h4>
                    Timesheet Approval
                </h4>
            </div>
        </div>
    </div>
</div>


<div class="Container_Fluid_Main">

    <div class="mngsScrlVie">
        <div class="graphPage" fetch="{{ route('management.reports.graph') }}" token="{{ csrf_token() }}">
        </div>


        <div class="MainBoxRow mb25">
            @if (\Illuminate\Support\Facades\Auth::user()->portal_type == 'App\Models\Client')
            <form method="GET" action="{{ route('ts.approvals') }}">
                <div class='col-sm-4'>
                    <div class='form-group'>
                        <select class="form-control select2" id="filter_unit" name="filter_unit">
                            <option value="0">Units</option>
                            @foreach ($units as $unit)
                            <option @if (request('filter_unit')==$unit->clientUnitId) selected @endif
                                value="{{ $unit->clientUnitId }}">{{ $unit->alias }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class='col-sm-3'>
                    <div class='form-group'>
                        <select class="form-control select2" id="filter_tax_year" name="filter_tax_year">
                            <option value="0">Tax Year</option>
                            @foreach ($TaxYear as $year)
                            <option @if (request('filter_tax_year')==$year->taxYearId) selected @elseif($year->default==1) selected @endif
                                value="{{ $year->taxYearId }}">{{ $year->taxYearFrom }}-{{ $year->taxYearTo }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class='col-sm-3'>
                    <div class='form-group'>
                        <select class="form-control select2" id="filter_tax_week" name="filter_tax_week">
                            <option value="0">Week No</option>
                            @foreach ($TaxWeek as $week)
                            <option @if (request('filter_tax_week')==$week->weekNumber) selected @endif
                                value="{{ $week->weekNumber }}">{{ $week->weekNumber }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                {{-- Preserve action_week if present --}}
                @if (request()->has('action_week'))
                <input type="hidden" name="action_week" value="{{ request('action_week') }}">
                @endif
                <button type="submit" class="btn-primary">Filter</button>
                <a href="{{ route('clear.filter') }}">
                    <button type="button"
                        class="btn-three">Reset</button>
                </a>
            </form>
            @endif

        </div>

        <div style="margin-top: 0;" class="shift_box mb10">
            <div class="shift_bottom">

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Week No</th>
                            <th>Weekend</th>
                            <th>Unit</th>
                            <th>No of shifts current week</th>
                            <th>No of shifts from Previous week</th>
                            <th>Total No of Ts to approve</th>
                            <th>No of Ts Approved</th>
                            <th>No of shifts moved to next week</th>
                            <th>Invoice generated</th>
                            <th style="text-align: right;">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($summaryData as $row)
                        <tr>
                            <td>{{ $row['Week No'] }}</td>
                            <td>{{ $row['Weekending'] }}</td>
                            <td>{{ $row['Unit'] }}</td>
                            <td>{{ $row['No of shifts current week'] }}</td>
                            <td>{{ $row['No of shifts from Previous week'] }}</td>
                            <td>{{ $row['Total No of Ts to approve'] }}</td>
                            <td>{{ $row['No of Ts Approved'] }}</td>
                            <td>{{ $row['No of shifts moved to next week'] }}</td>
                            <td>{{ $row['Invoice generated'] }}</td>
                            <td style="text-align: right;">
                                <form method="GET" action="{{ route('ts.approvals') }}">
                                    <input type="hidden" name="action_week" value="{{ $row['Week No'] }}">

                                    {{-- Preserve unit filter --}}
                                    
                                    <input type="hidden" name="filter_unit"
                                        value="{{ $row['unitId'] }}">
                                    
                                    @if (request()->has('filter_tax_year'))
                                    <input type="hidden" name="filter_tax_year"
                                        value="{{ request('filter_tax_year') }}">
                                    @endif
                                    @if (request()->has('filter_tax_week'))
                                    <input type="hidden" name="filter_tax_week"
                                        value="{{ request('filter_tax_week') }}">
                                    @endif
                                     @if (request()->has('searchStaff'))
                                    <input type="hidden" name="searchStaff"
                                        value="{{ request('searchStaff') }}">
                                    @endif

                                     @if (request()->has('searchCategory'))
                                    <input type="hidden" name="searchCategory"
                                        value="{{ request('searchCategory') }}">
                                    @endif

                                     @if (request()->has('searchShift'))
                                    <input type="hidden" name="searchShift"
                                        value="{{ request('searchShift') }}">
                                    @endif

                                    @if (request()->has('searchDate'))
                                    <input type="hidden" name="searchDate"
                                        value="{{ request('searchDate') }}">
                                    @endif
                                    @if (request()->has('action_week'))
                                    <input type="hidden" name="action_week"
                                        value="{{ request('action_week') }}">
                                    @endif

                                    <button type="submit" class="btn-three-sm">
                                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                    </button>
                                </form>

                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="9" class="text-center">No data available</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
                @if ($selectedWeekTimesheets && $selectedWeekTimesheets->count())

                <h4 class="mt25 mb15">Detailed Timesheets for Week {{ $actionWeek }}</h4>

                <div class="MainBoxRow">
                    <div class="box">
                        <div class="box-body">
                            <form action="{{ route('ts.approvals') }}" method="GET" enctype="multipart/form-data"
                                id="timesheet_form">
                                @if (request()->has('filter_unit'))
                                <input type="hidden" name="filter_unit" value="{{ request('filter_unit') }}">
                                @endif
                             

                                 <div class="row" >
                                    <div class='col-sm-2'>
                                        <div class='form-group'>
                                            <label for="date">Date</label>

                                            <input class="form-control datepicker" id="searchDate" name="searchDate"
                                                @if (request('searchDate')) value="{{ request('searchDate') }}"
                                                @else value="{{ \Carbon\Carbon::now()->subWeek()->startOfWeek()->format('d-m-Y') }} - {{ \Carbon\Carbon::now()->subWeek()->endOfWeek()->format('d-m-Y') }}" @endif
                                                type="text" placeholder="Date" autocomplete="off" />
                                        </div>
                                    </div>
                                   <div class='col-sm-2'>
                                        <div class='form-group'>
                                            <label for="date"> Category</label>
                                            <select class="form-control select2" id="searchCategory"
                                                name="searchCategory">
                                                <option value=""></option>
                                                @foreach ($categories as $category)
                                                <option @if (request('searchCategory')==$category->categoryId) selected @endif
                                                    value="{{ $category->categoryId }}">{{ $category->name }}
                                                </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <div class='col-sm-2'>
                                        <div class='form-group'>
                                            <label for="date">Shift</label>
                                            <select class="form-control select2" id="searchShift"
                                                name="searchShift">
                                                <option value=""></option>
                                                @foreach ($shifts as $shift)
                                                <option @if (request('searchShift')==$shift->shiftId) selected @endif
                                                    value="{{ $shift->shiftId }}">{{ $shift->name }}</option>
                                                @endforeach
                                            </select>
                                            <p class="error">Shift is required</p>
                                        </div>
                                    </div>
                                    <div class='col-sm-2'>
                                        <div class='form-group'>
                                            <label for="date">Staff</label>
                                            <select class="form-control select2" id="searchStaff"
                                                name="searchStaff">
                                                <option value=""></option>
                                                @foreach ($staffs as $staff)
                                                <option @if (request('searchStaff')==$staff->staffId) selected @endif
                                                    value="{{ $staff->staffId }}">
                                                    {{ $staff->forname . ' ' . $staff->surname }}
                                                </option>
                                                @endforeach
                                            </select>
                                            <p class="error">Staff is required</p>
                                        </div>
                                    </div>
                                        {{-- Preserve unit filter --}}
                                    @if (request()->has('filter_unit'))
                                    <input type="hidden" name="filter_unit"
                                        value="{{ request('filter_unit') }}">
                                    @endif
                                    @if (request()->has('filter_tax_year'))
                                    <input type="hidden" name="filter_tax_year"
                                        value="{{ request('filter_tax_year') }}">
                                    @endif
                                    @if (request()->has('filter_tax_week'))
                                    <input type="hidden" name="filter_tax_week"
                                        value="{{ request('filter_tax_week') }}">
                                    @endif
                                    @if (request()->has('action_week'))
                                    <input type="hidden" name="action_week"
                                        value="{{ request('action_week') }}">
                                    @endif
                                    <div class='col-sm-2 m-t-25'>
                                        <button class="btn btn-primary filterNow" data-action="">Filter</button>
                                        <a href="{{ route('clear.filter') }}"><button type="button"
                                                class="btn btn-three">Reset</button></a>
                                    </div> 

                                </div>
                            </form>
                        </div>
                    </div>
                </div>


                <div class="ActBtn action-buttons mt-3">
                    <button type="button" class="tmsBtn" id="approveSelected">Approve
                        Selected
                    </button>
                    <button type="button" class="tmsBtn" id="generateInvoiceSelected">Generate
                        Invoice
                    </button>
                    <button type="button" class="tmsBtn" id="moveToNextWeek">Move to Next
                        Week
                    </button>
                    <button type="button" class="tmsBtn downloadBtn tmsBtnActive" id="exportExcel">Export to
                        Excel
                    </button>
                </div>

                <table class="table table-bordered" id="my-table">

                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"> Booking ID</th>
                            <th>Number</th>
                            <th>PO</th>
                            <th>Week</th>
                            <th>Date</th>
                            <th>Shift</th>
                            <th>Unit</th>
                            <th>Staff</th>
                            <th>Category</th>
                            <th>Start</th>
                            <th>End</th>
                            <th>Hours</th>
                            <th>Hourly Rate</th>
                            <th>ENIC</th>
                            <th>TA</th>
                            <th>Additional Pay</th>
                            <th>Line Total</th>
                            <th style="text-align: right">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                          //echo '<pre>';  print_r($selectedWeekTimesheets[0]->quotation); exit;

                        @endphp
                        @foreach ($selectedWeekTimesheets as $ts)



                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input" name="ts_ids[]"
                                    value="{{ $ts->timesheetId }}">
                                {{ $ts->booking->bookingId }}
                            </td>
                            <td>{{ $ts->number ?? '-' }}</td>
                            <td>{{ $ts->booking->po_number ?? '-' }}</td>
                            <td>{{ $ts->paymentWeek ?? '-' }}</td>
                            <td>{{ \Carbon\Carbon::parse($ts->booking->date)->format('d-m-Y') ?? '-' }}</td>
                            <td>{{ $ts->booking->shift->name ?? '-' }}</td>
                            <td>{{ $ts->booking->unit->alias ?? '-' }}</td>
                            <td>{{ $ts->booking->staff->full_name ?? '-' }}</td>
                            <td>{{ $ts->booking->category->name ?? '-' }}</td>
                            <td>{{ date('H:i', strtotime($ts->startTime)) }}</td>
                            <td>{{ date('H:i', strtotime($ts->endTime)) }}</td>
                            <td>{{ number_format($ts->unitHours, 2) }}</td>
                            <td>{{ $ts->quotation->hourly_rate }}</td>
                            <td>-</td>
                            <td>0</td>
                            <td>0</td>
                            <td>{{ $ts->quotation->hourly_rate * number_format($ts->unitHours, 2) }}</td>
                            <td style="text-align: right">
                                <form method="POST" style="display:inline-block;">
                                    @csrf
                                    <button class="btn btn-sm btn-three-sm approveForm"
                                        @if ($ts->verification_from_portal == 1) disabled @endif
                                        fetch="{{ route('ts.approve', ['id' => $ts->timesheetId]) }}"
                                        type="button" id="">
                                        @if ($ts->verification_from_portal == 1)
                                        <i class="fa fa-thumbs-up" aria-hidden="true"></i>
                                        @else
                                        <i class="fa fa-check" aria-hidden="true"></i>
                                        @endif
                                    </button>
                                </form>
                                <form method="POST" style="display:inline-block;">
                                    @csrf
                                    <button id="declineForm" class="btn btn-sm btn-three-sm" type="button"
                                        data-toggle="modal" data-target="#declineReasonModal"
                                        @if ($ts->verification_from_portal == 1) disabled @endif
                                        fetch="{{ route('ts.decline', ['id' => $ts->timesheetId]) }}">
                                        @if ($ts->verification_from_portal == 2)
                                       <i class="fa fa-minus-circle" aria-hidden="true"></i>
                                        @else
                                        <i class="fa fa-times" aria-hidden="true"></i>
                                        @endif
                                    </button>
                                </form>
                                <button type="button" class="btn btn-sm btn-three-sm openTimesheetModal"
                                    data-toggle="modal" data-target="#popTimesheet"
                                    data-timesheet-id="{{ $ts->timesheetId }}"
                                    data-start-time="{{ date('H:i', strtotime($ts->startTime)) }}"
                                    data-end-time="{{ date('H:i', strtotime($ts->endTime)) }}"
                                    data-total-hours="{{ number_format($ts->unitHours, 2) }}"
                                    data-image-url="{{ $ts->image_url ?? '' }}"
                                    approved=" @if ($ts->verification_from_portal == 1) Approved @else Approve @endif"
                                    declined=" @if ($ts->verification_from_portal == 1) Declined @else Decline @endif">
                                    <i class="fa fa-eye" aria-hidden="true"></i>
                                </button>

                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @endif
                <!-- Pagination -->

            </div>
        </div>
    </div>
</div>

{{-- <div class="modal fade" id="timesheetModalPop" tabindex="-1" role="dialog" aria-labelledby="timesheetModalLabel" aria-hidden="true"> --}}
<div class="modal fade" id="popTimesheet" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="timesheetModalLabel">Timesheet Details</h5>
               <button style="position: absolute;top: 12px;right: 0;" type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                             <span aria-hidden="true">&times;</span>
                        </button>
            </div>
            <div class="modal-body">
                <p><strong>Start Time:</strong> <span id="modalStartTime"></span></p>
                <p><strong>End Time:</strong> <span id="modalEndTime"></span></p>
                <p><strong>Total Hours:</strong> <span id="modalTotalHours"></span></p>
                <p><strong>Timesheet Image:</strong></p>
                <img id="modalTimesheetImage" src="" alt="Timesheet Image" class="img-fluid"
                    style="max-height: 300px;" />
            </div>
            <div class="modal-footer">
                <form method="POST" style="display:inline-block;">
                    @csrf
                    <button type="submit" class="btn btn-primary approveForm">Approve</button>
                </form>
                <form method="POST" style="display:inline-block;">
                    @csrf
                    <button type="button" class="btn btn-two openDeclineModal" data-toggle="modal"
                        data-target="#declineReasonModal">Decline</button>
                </form>
            </div>
        </div>
    </div>
</div>



<!-- Decline Reason Modal -->
<div class="modal fade" id="declineReasonModal" tabindex="-1" role="dialog"
    aria-labelledby="declineModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="declineModalLabel">Reason for Decline</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="declineReason">Please enter the reason for declining:</label>
                        <textarea class="form-control" name="decline_reason" id="declineReason" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger declineFormSubmit">Submit</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<script src="{{ asset('unit-portal/js/daterangepicker.min.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/1.3.8/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.14.1/xlsx.full.min.js"></script>
<script>
    $('.downloadBtn').on('click', function() {
        // Clone the table to avoid affecting the original table
        var $originalTable = $('#my-table');
        var $clonedTable = $originalTable.clone();

        // Remove the "Action" column (index 11)
        $clonedTable.find('tr').each(function() {
            $(this).find('th:eq(11), td:eq(11)').remove();
        });

        // Export the cleaned table
        var wb = XLSX.utils.table_to_book($clonedTable[0], {
            sheet: "Sheet name"
        });
        var wbout = XLSX.write(wb, {
            bookType: 'xlsx',
            bookSST: true,
            type: 'binary'
        });

        function s2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i < s.length; i++) {
                view[i] = s.charCodeAt(i) & 0xFF;
            }
            return buf;
        }

                saveAs(new Blob([s2ab(wbout)], {
                    type: "application/octet-stream"
                }), 'Timesheets.xlsx');
            });

            $(document).ready(function() {
                $('input[name="date"]').daterangepicker({
                    autoUpdateInput: false,
                    locale: {
                        format: 'DD-MM-YYYY',
                        cancelLabel: 'Clear'
                    }
                });
                $('input[name="date"]').on('apply.daterangepicker', function(ev, picker) {
                    $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format(
                        'DD-MM-YYYY'));
                });
                $('input[name="date"]').on('cancel.daterangepicker', function(ev, picker) {
                    $(this).val('');
                });

                $(document).on('click', '.openTimesheetModal', function() {
                    const $btn = $(this);
                    const timesheetId = $btn.data('timesheet-id');
                    const approve = $btn.data('approved');
                    const decline = $btn.data('declined');
                    const startTime = $btn.data('start-time');
                    const endTime = $btn.data('end-time');
                    const totalHours = $btn.data('total-hours');
                    const imageUrl = $btn.data('image-url');
                   // alert(imageUrl);
                    $('#modalStartTime').text(startTime);
                    $('#modalEndTime').text(endTime);
                    $('#modalTotalHours').text(totalHours);
                    $('#modalTimesheetImage').attr('src', imageUrl);

                    $('.approveForm').attr('fetch', '/approve/' + timesheetId);
                    $('.declineFormSubmit').attr('fetch', '/decline/' + timesheetId);
                    $('.approveForm').text(approve);
                    $('.declineFormSubmit').text(decline);

                    $('#timesheetModalPop').modal('show');
                });

                // Approve AJAX
                $('.approveForm').on('click', function(e) {
                    e.preventDefault();
                    let url = $(this).attr('fetch');

                    $.ajax({
                        url: url,
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            alert(response.message);
                            $('#timesheetModalPop').modal('hide');
                            location.reload(); // Or remove the row dynamically
                        },
                        error: function(xhr) {
                            alert('An error occurred while approving.');
                        }
                    });
                });

                // Decline AJAX
                $('#declineForm').on('click', function(e) {
                    e.preventDefault();
                    let url = $(this).attr('fetch');
                    $('.declineFormSubmit').attr('fetch', url);
                    //alert(url);
                });
                $('.declineFormSubmit').on('click', function(e) {
                    e.preventDefault();
                    let url = $(this).attr('fetch');
                    let reason = $('#declineReason').val();

                    $.ajax({
                        url: url,
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            decline_reason: reason
                        },
                        success: function(response) {
                            alert(response.message);
                            $('#declineReasonModal').modal('hide');
                            location.reload(); // Or remove the row dynamically
                        },
                        error: function(xhr) {
                            alert('An error occurred while declining.');
                        }
                    });
                });
                //selct all checkboxes

                // Select/Deselect All Checkboxes
                $('#selectAll').on('change', function() {
                    $('input[name="ts_ids[]"]').prop('checked', this.checked);
                });

                // Uncheck Select All if any individual checkbox is unchecked
                $(document).on('change', 'input[name="ts_ids[]"]', function() {
                    if (!this.checked) {
                        $('#selectAll').prop('checked', false);
                    } else if ($('input[name="ts_ids[]"]:not(:checked)').length === 0) {
                        $('#selectAll').prop('checked', true);
                    }
                });

                // Approve Selected
                $('#approveSelected').on('click', function() {
                    let selected = [];
                    $('input[name="ts_ids[]"]:checked').each(function() {
                        selected.push($(this).val());
                    });
                    if (selected.length === 0) {
                        alert('Please select at least one timesheet.');
                        return;
                    }
                    if (!confirm('Are you sure you want to approve the selected timesheets?')) return;

                    $.ajax({
                        url: '/approve-multiple',
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            timesheet_ids: selected
                        },
                        success: function(response) {
                            alert('timesheets approved successfully.');
                            location.reload();
                        },
                        error: function() {
                            alert('Error while approving selected timesheets.');
                        }
                    });
                });

                // Move to next week Selected
                $('#moveToNextWeek').on('click', function() {
                    let selected = [];
                    $('input[name="ts_ids[]"]:checked').each(function() {
                        selected.push($(this).val());
                    });
                    if (selected.length === 0) {
                        alert('Please select at least one timesheet.');
                        return;
                    }
                    if (!confirm('Are you sure you want to move the selected timesheets?')) return;

                    $.ajax({
                        url: '/move-to-next-week',
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            timesheet_ids: selected
                        },
                        success: function(response) {
                            alert('Information moved to next week successfully.');
                            location.reload();
                        },
                        error: function() {
                            alert('Error while approving selected timesheets.');
                        }
                    });
                });


                //Generate Invoice for Selected
                $('#generateInvoiceSelected').on('click', function() {
                    let selected = [];
                    $('input[name="ts_ids[]"]:checked').each(function() {
                        selected.push($(this).val());
                    });
                    if (selected.length === 0) {
                        alert('Please select at least one timesheet.');
                        return;
                    }
                    
                    if (!confirm('Are you sure you want to generate the selected timesheets?')) return;

                    $.ajax({
                        url: 'generate-invoice-multiple',
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            timesheet_ids: selected
                        },
                        success: function(response) {
                            alert('invoice generated successfully.');
                            //location.reload();
                        },
                        error: function() {
                            alert('Error while approving selected timesheets.');
                        }
                    });
                });

              $(function () {
    $('#searchDate').daterangepicker({
        autoUpdateInput: false, // Prevent automatic update
        locale: {
            format: 'DD-MM-YYYY',
            cancelLabel: 'Clear'
        },
        showDropdowns: true,
        opens: 'left',
    });

    // When user selects a date range, manually update the input
    $('#searchDate').on('apply.daterangepicker', function (ev, picker) {
        $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format('DD-MM-YYYY'));
    });
    // Optionally handle clear/cancel
    $('#searchDate').on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
    });
});


    });
</script>
<script>
$(document).ready(function () {

    // ✅ Toggle "activeBtn" class based on checkbox selection
    function toggleApproveButtonClass() {
        const selected = $('input[name="ts_ids[]"]:checked').length;
        if (selected > 0) {
            $('#approveSelected,#moveToNextWeek').addClass('tmsBtnActive');
        } else {
            $('#approveSelected,#moveToNextWeek').removeClass('tmsBtnActive');
        }
    }

    // ✅ Select/Deselect All Checkboxes
    $('#selectAll').on('change', function () {
        const isChecked = this.checked;
        $('input[name="ts_ids[]"]').prop('checked', isChecked);
        toggleApproveButtonClass();
    });

    // ✅ Individual checkbox toggle
    $(document).on('change', 'input[name="ts_ids[]"]', function () {
        const allChecked = $('input[name="ts_ids[]"]:not(:checked)').length === 0;
        $('#selectAll').prop('checked', allChecked);
        toggleApproveButtonClass();
    });
});
</script>

@endsection