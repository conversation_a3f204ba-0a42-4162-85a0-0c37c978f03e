@extends('clientPortal.layout')
@section('title', 'Advanced Dashboard | Client Portal')

@push('styles')
    <link rel="stylesheet" href="{{ asset('client-portal/css/dashboard-components.css') }}">
    <link rel="stylesheet" href="{{ asset('client-portal/css/advanced-dashboard.css') }}">
@endpush

@section('content')
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper clientvalue" client="{{ $clientId }}">
    <div class="Container_Fluid_Main">
        <!-- Content Header with Enhanced Filters -->
        <div class="content-header">
            <div class="row headTitleBtm">
                <div class="col-md-6">
                    <div class="GpBoxHeading no-bdr pb0">
                        <h4>
                            <i class="fa fa-tachometer" style="margin-right: 8px; color: #1d75bd;"></i>
                            Advanced Dashboard
                        </h4>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-filters-container">
                        <!-- Main Filter -->
                        <select style="border: 1px solid #eae9e9 !important; margin-top: 0px; margin-right: 10px;"
                                name="main_filter"
                                id="monthselect"
                                class="form-control filter FlitSelect">
                            <option value="1" {{ request()->get('filter') == 1 ? 'selected' : '' }}>This Month</option>
                            <option value="2" {{ request()->get('filter') == 2 ? 'selected' : '' }}>Last Month</option>
                            <option value="4" {{ request()->get('filter') == 4 ? 'selected' : '' }}>Next Month</option>
                        </select>

                        <!-- Unit Filter -->
                        <select style="border: 1px solid #eae9e9 !important; margin-top: 0px; margin-right: 10px;"
                                name="unit_filter"
                                id="unitselect"
                                class="form-control filter FlitSelect">
                            <option value="">All Units</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->clientUnitId }}">{{ $unit->name }}</option>
                            @endforeach
                        </select>

                        <!-- Financial Year Filter -->
                        <select style="border: 1px solid #eae9e9 !important; margin-top: 0px;"
                                name="financial_year_filter"
                                id="financialyearselect"
                                class="form-control filter FlitSelect">
                            @php
                                $currentYear = date('Y');
                                $currentMonth = date('n');
                                $startYear = $currentMonth >= 4 ? $currentYear : $currentYear - 1;
                            @endphp
                            @for($i = 0; $i < 3; $i++)
                                @php
                                    $year = $startYear - $i;
                                    $financialYear = $year . '-' . ($year + 1);
                                @endphp
                                <option value="{{ $financialYear }}" {{ $i == 0 ? 'selected' : '' }}>
                                    {{ $financialYear }}
                                </option>
                            @endfor
                        </select>
                    </div>
                </div>
            </div>
        </div>

        {{-- Use the modular dashboard wrapper with advanced features --}}
        @include('common.dashboard.main-wrapper', [
            'clientId' => $clientId,
            'permissions' => $permissions
        ])
    </div>
</div>
@endsection

@section('scripts')
{{-- Include Highcharts for charts --}}
<script src="{{ asset('unit-portal/js/highcharts.js') }}"></script>
<script src="{{ asset('unit-portal/js/variable-pie.js') }}"></script>
<script src="{{ asset('client-portal/js/bootbox.min.js') }}"></script>

{{-- Include modular dashboard components --}}
<script src="{{ asset('client-portal/js/dashboard-components/main.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/filter.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/budget-summary-cards.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/budget-donut-chart.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/expenses-donut-chart.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/remaining-components.js') }}"></script>

{{-- Advanced Dashboard Filters --}}
<script src="{{ asset('client-portal/js/advanced-dashboard-filters.js') }}"></script>

{{-- jQuery and jQuery UI --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<script>
// Global variables for dashboard
var clientId = '{{ $clientId }}';
var apiBaseUrl = "{{ config('app.api_base_url') }}";

$(document).ready(function() {
    // Set client information in localStorage
    localStorage.setItem('nsg-client-portal-login-user-id', "{{ $clientId }}");
    localStorage.setItem('nsg-client-portal-login-user-name', "{{ auth()->user()->portal->name ?? '' }}");

    // Initialize Advanced Dashboard Filters
    if (typeof AdvancedDashboardFilters !== 'undefined') {
        AdvancedDashboardFilters.init({
            clientId: '{{ $clientId }}',
            currentFilter: {{ request()->get('filter', 1) }},
            currentFinancialYear: '{{ $financialYear }}'
        });
    }

    // Override API endpoints for advanced dashboard
    if (typeof DashboardComponents !== 'undefined') {
        // Update API base URL to use advanced dashboard endpoints
        DashboardComponents.config.apiBaseUrl = "{{ config('app.api_base_url') }}";

        // Initialize with advanced dashboard configuration
        DashboardComponents.init({
            clientId: '{{ $clientId }}',
            apiBaseUrl: "{{ config('app.api_base_url') }}",
            currentFilter: {{ request()->get('filter', 1) }},
            isAdvancedDashboard: true
        });
    }

    // Add fade-in animation to cards
    $('.BudgDashBox').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });

    console.log('Advanced Dashboard loaded successfully');
});
</script>

{{-- Advanced Dashboard Specific Scripts --}}
<script>
// Override API endpoints for advanced dashboard
$(document).ready(function() {
    // Update API endpoints to use advanced dashboard controller
    var advancedEndpoints = {
        budgetSummary: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/budget-summary",
        budgetDonut: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/budget-donut",
        expenseDonut: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/expense-donut",
        barChart: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/bar-chart",
        lineChart: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/line-chart",
        bookingReasons: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/booking-reasons",
        clientUnits: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/client-units"
    };
    
    // Update component endpoints
    if (typeof BudgetSummaryCards !== 'undefined') {
        $('#budget-summary-cards').attr('data-fetch', advancedEndpoints.budgetSummary);
    }
    
    if (typeof BudgetDonutChart !== 'undefined') {
        $('#thisMonthBudgetPie').attr('data-fetch', advancedEndpoints.budgetDonut);
    }
    
    if (typeof ExpensesDonutChart !== 'undefined') {
        $('#nextMonthExpensePie').attr('data-fetch', advancedEndpoints.expenseDonut);
    }
    
    if (typeof SmartViewChart !== 'undefined') {
        $('#smart-view-component').attr('data-fetch', advancedEndpoints.barChart);
    }
    
    if (typeof UnitExpenseOverview !== 'undefined') {
        $('#unit-expense-overview-component').attr('data-fetch', advancedEndpoints.lineChart);
    }
    
    if (typeof BookingReasons !== 'undefined') {
        $('#booking-reasons').attr('data-fetch', advancedEndpoints.bookingReasons);
    }
});
</script>
@endsection
