@extends('clientPortal.layout')
@section('title', 'Advanced Dashboard | Client Portal')

@push('styles')
    <link rel="stylesheet" href="{{ asset('client-portal/css/dashboard-components.css') }}">
@endpush

@section('content')
{{-- Use the modular dashboard wrapper with advanced features --}}
@include('common.dashboard.main-wrapper', [
    'clientId' => $clientId,
    'permissions' => $permissions
])
@endsection

@section('scripts')
{{-- Include Highcharts for charts --}}
<script src="{{ asset('unit-portal/js/highcharts.js') }}"></script>
<script src="{{ asset('unit-portal/js/variable-pie.js') }}"></script>
<script src="{{ asset('client-portal/js/bootbox.min.js') }}"></script>

{{-- Include modular dashboard components --}}
<script src="{{ asset('client-portal/js/dashboard-components/main.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/filter.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/budget-summary-cards.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/budget-donut-chart.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/expenses-donut-chart.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/remaining-components.js') }}"></script>

{{-- jQuery and jQuery UI --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    // Set client information in localStorage
    localStorage.setItem('nsg-client-portal-login-user-id', "{{ $clientId }}");
    localStorage.setItem('nsg-client-portal-login-user-name', "{{ auth()->user()->portal->name ?? '' }}");
    
    // Override API endpoints for advanced dashboard
    if (typeof DashboardComponents !== 'undefined') {
        // Update API base URL to use advanced dashboard endpoints
        DashboardComponents.config.apiBaseUrl = "{{ config('app.api_base_url') }}";
        
        // Initialize with advanced dashboard configuration
        DashboardComponents.init({
            clientId: '{{ $clientId }}',
            apiBaseUrl: "{{ config('app.api_base_url') }}",
            currentFilter: {{ request()->get('filter', 1) }},
            isAdvancedDashboard: true
        });
    }
    
    console.log('Advanced Dashboard loaded successfully');
});
</script>

{{-- Advanced Dashboard Specific Scripts --}}
<script>
// Override API endpoints for advanced dashboard
$(document).ready(function() {
    // Update API endpoints to use advanced dashboard controller
    var advancedEndpoints = {
        budgetSummary: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/budget-summary",
        budgetDonut: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/budget-donut",
        expenseDonut: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/expense-donut",
        barChart: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/bar-chart",
        lineChart: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/line-chart",
        bookingReasons: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/booking-reasons",
        clientUnits: "{{ config('app.api_base_url') }}/client-portal/advanced-dashboard/client-units"
    };
    
    // Update component endpoints
    if (typeof BudgetSummaryCards !== 'undefined') {
        $('#budget-summary-cards').attr('data-fetch', advancedEndpoints.budgetSummary);
    }
    
    if (typeof BudgetDonutChart !== 'undefined') {
        $('#thisMonthBudgetPie').attr('data-fetch', advancedEndpoints.budgetDonut);
    }
    
    if (typeof ExpensesDonutChart !== 'undefined') {
        $('#nextMonthExpensePie').attr('data-fetch', advancedEndpoints.expenseDonut);
    }
    
    if (typeof SmartViewChart !== 'undefined') {
        $('#smart-view-component').attr('data-fetch', advancedEndpoints.barChart);
    }
    
    if (typeof UnitExpenseOverview !== 'undefined') {
        $('#unit-expense-overview-component').attr('data-fetch', advancedEndpoints.lineChart);
    }
    
    if (typeof BookingReasons !== 'undefined') {
        $('#booking-reasons').attr('data-fetch', advancedEndpoints.bookingReasons);
    }
});
</script>
@endsection
