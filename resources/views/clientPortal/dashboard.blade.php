@extends('clientPortal.layout')
@section('title', 'Dashboard | Client Portal')
@section('content')
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper clientvalue" client="{{ auth()->user()->portal_id }}">

    <div class="Container_Fluid_Main">
        <div class="content-header">
            <div class="row headTitleBtm">
                <div class="col-md-7">
                    <div class="GpBoxHeading no-bdr pb0">
                        <h4>
                            Dashboard
                        </h4>
                    </div>
                </div>
                 <div class="col-sm-5 text-right">
                    <select style="border: 1px solid #eae9e9 !important; margin-top: 0px;" name="" id="monthselect" class="form-control filter FlitSelect">
                        <option value="1" {{ Request::get('filter') == 1 ? 'selected' : '' }}>This Month
                        </option>
                        <option value="2" {{ Request::get('filter') == 2 ? 'selected' : '' }}>Last Month
                        </option>
                        <option value="4" {{ Request::get('filter') == 4 ? 'selected' : '' }}>Next Month
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <!-- /.content-header -->
        <!-- Main content -->

        <section class="ClientCtBG content">
            <div class="firstBox marginCenter budgetBox row"
                fetch="{{ config('app.api_base_url') }}/client-portal/get-client-budget" year="{{ date('Y') }}"
                month="{{ date('m') }}"></div>
            <div class="row ">
                <div class="col-md-4">
                    <div class="marginCenter GpBox thisMonthBudget shadowWidget">
                        <figure class="highcharts-figure">
                            <h4 class="greenFont BudTitle">Budget</h4>
                            <div id="thisMonthBudgetPie"
                                fetch="{{ config('app.api_base_url') }}/client-portal/get-dashboard-donut-graph"
                                year="{{ date('Y') }}" month="{{ date('m') }}"></div>
                            <div class="graphInnerSUm"></div>
                            <div class=" ">
                                <table cellpadding="0" cellspacing="0" border="0" class="table table-striped table-bordered"
                                    id="example">
                                    <thead>
                                        <tr>
                                            <th>Units</th>
                                            <th style='text-align: end;'>Amount</th>
                                            <th style='text-align: end;'>Percentage</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tfoot></tfoot>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </figure>
                    </div>
                </div>

                <div class="col-md-4 client_name" client="{{ auth()->user()->portal_id }}">
                    <div class="marginCenter GpBox nextMonthExpense shadowWidget">
                        <h4 class="redFont BudTitle">Expenses</h4>
                        <figure class="highcharts-figure">
                            <div id="nextMonthExpensePie"></div>
                            <div class="graphInnerSUm redFont"></div>
                            <div class="">
                                <table cellpadding="0" cellspacing="0" border="0" class="table table-striped table-bordered"
                                    id="example">
                                    <thead>
                                        <tr>
                                            <th>Units</th>
                                            <th style='text-align: end;'>Amount</th>
                                            <th style='text-align: end;'>Percentage</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tfoot></tfoot>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </figure>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="marginCenter reminderList">
                        <div class="">
                            <div class="col-md-12 NtBox">
                                <div class="h50 shadowWidget blueBullet todo" id="todo" style="margin-top: 10px;"
                                    fetch="{{ config('app.api_base_url') }}/client-portal/list-dashboard-todos">
                                    <div class="headRem dashboard-todos"
                                        fetch="{{ config('app.api_base_url') }}/client-portal/add-dashboard-todo"
                                        remove-url="{{ config('app.api_base_url') }}/client-portal/remove-dashboard-todos"
                                        edit-url="{{ config('app.api_base_url') }}/client-portal/get-dashboard-todos">
                                        <span class=" BudTitle noteTitle no-bdr"> <i class="fa fa-sticky-note-o"
                                                aria-hidden="true"></i>&nbsp;
                                            Notes
                                        </span>
                                        <a class='btn-three-sm addTodoBtn add-todos'> <span class='p4' data-toggle='tooltip'
                                                title='Add Todo'><i class='fa fa-plus'></i></span>
                                        </a>
                                        <a class='btn addTodoBtnRemove  remove-todosForm' style='display: none;'> <span
                                                class='p4' data-toggle='tooltip' title='Remove'><i
                                                    class='fa fa-times'></i></span>
                                        </a>
                                    </div>
                                    <div class="headRem todos-form" style="margin-top: 21px;"></div>
                                    <ul></ul>
                                </div>
                            </div>
                            <div class="col-md-12 NtBox">
                                <div class="h50 shadowWidget yellowBullet reminders" id="reminders"
                                    fetch="{{ config('app.api_base_url') }}/client-portal/list-dashboard-reminders"
                                    style="margin-top: 10px;">
                                    <div class="headRem dashboard-reminder"
                                        fetch="{{ config('app.api_base_url') }}/client-portal/add-dashboard-reminder"
                                        remove-url="{{ config('app.api_base_url') }}/client-portal/remove-dashboard-reminders"
                                        edit-url="{{ config('app.api_base_url') }}/client-portal/get-dashboard-reminders">
                                        <span class="noteTitle BudTitle no-bdr">
                                            <i class="fa fa-bell-o" aria-hidden="true"></i>&nbsp;Reminders
                                        </span>
                                        <a class='btn-three-sm addTodoBtn add-reminder' style=''> <span class='p4'
                                                data-toggle='tooltip' title='Add Reminder'><i class='fa fa-plus'></i></span>
                                        </a>
                                        <a class='btn addTodoBtnRemove remove-form' style='display: none;'> <span class='p4'
                                                data-toggle='tooltip' title='Remove'><i class='fa fa-times'></i></span>
                                        </a>
                                    </div>
                                    <div class="headRem reminder-form" style="margin-top: 21px;"></div>
                                    <ul class="reminder_ul_form"></ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="thisMonthBudgetVsExpense row mt25" id="thisMonthBudgetVsExpense"
                fetch="{{ config('app.api_base_url') }}/client-portal/get-dashboard-bar-budget-usage"
                year="{{ date('Y') }}" month="{{ date('m') }}">

                <div class="marginCenter shadowWidget col-md-12">
                    <div class="GpBoxHeading">
                        <div class="row">
                            <div class="col-md-9">
                                <h4>Smart View - <span class="smlFnt"><span
                                            class="greenFont">Budget</span> vs
                                        <span class="redFont">Expenses</span></span> </h4>
                            </div>
                            <div class="col-md-3">
                                <select style="border: 1px solid #eae9e9 !important;    margin-top: 2px;" name="" id="smartViewMonthselect" class="form-control smartViewFilter">
                                    <option value="1" {{ Request::get('filter') == 1 ? 'selected' : '' }}>This Month
                                    </option>
                                    <option value="2" {{ Request::get('filter') == 2 ? 'selected' : '' }}>Last Month
                                    </option>
                                    <option value="4" {{ Request::get('filter') == 4 ? 'selected' : '' }}>Next Month
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <figure style="width: 100%;max-width: 100%;" class="highcharts-figure">
                    <div id="usageThisMonth"></div>
                </figure>
            </div>



            <div class="marginCenter shadowWidget ResonPaddCL row">
                <div class="col-md-12">

                    <div class="GpBoxHeading mt25">
                        <h4>Reason for Booking</h4>
                    </div>

                    <div class="row" id="booking-reasons" fetch="{{ config('app.api_base_url') }}/client-portal/booking-counts-costs" data-client-id="{{ auth()->user()->portal_id }}">
                        <!-- Dynamic content will be injected here -->
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>Sickness</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>Holiday</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>Vacant</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>Resident Admission</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>One two_One</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>Extra Staff</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>Management</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="single_feature">
                                <div class="row" style="margin: 0;">
                                    <h5>Others</h5>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseLeft">
                                            <h3>Count</h3>
                                            <p>10</p>
                                        </div>
                                    </div>
                                    <div style="padding:0" class="col-md-6">
                                        <div class="stfBgseRIght">
                                            <h3>Cost</h3>
                                            <p>£ 100</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="marginCenter shadowWidget" id="timelineUsageExpense"
                        fetch="{{ config('app.api_base_url') }}/client-portal/get-dashboard-line-charts" year="{{ date('Y') }}"
                        month="{{ date('m') }}">
                        <div class="GpBoxHeading mt25">
                            <h4>Unit Expense Overview</h4>
                        </div>
                        <div id="containerLine"></div>
                    </div>

                    {{-- <div class="marginCenter GpBox m-b-12 col-md-12 shadowWidget">
                <div class="row">
                    <div class="marginCenter overview col-md-12" id="overview"
                        fetch="{{ config('app.api_base_url') }}/client-portal/get-budget-page-overview"
                    year="{{ date('Y') }}" month="{{ date('m') }}">
                    <table class="table expandTableBooks">
                        <thead>
                            <tr>
                                <th>Booking ID</th>
                                <th>Unit</th>
                                <th>Date</th>
                                <th>Shift</th>
                                <th>Category</th>
                                <th>Staff</th>
                                <th>Profile</th>
                                <th>No. Of Hours</th>
                                <th>Hourly Rate</th>
                                <th>TA</th>
                                <th>Cost</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                        <tfoot>
                            <ul class="pagination"></ul>
                        </tfoot>
                    </table>
                </div>
            </div>
    </div> --}}

</div>
</div>
</section>


<!-- /.content -->
</div>
</div>
<!-- /.content-wrapper -->
@endsection

@section('scripts')
<script>
    localStorage.setItem('nsg-client-portal-login-user-id', "{{ auth()->user()->portal_id }}");
    localStorage.setItem('nsg-client-portal-login-user-name', "{{ auth()->user()->portal->name }}");
</script>
<script src="{{asset('unit-portal/js/highcharts.js')}}"></script>
<script src="{{asset('unit-portal/js/variable-pie.js')}}"></script>
<script src="{{ asset('client-portal/js/bootbox.min.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard.js') }}"></script>
<script src="{{ asset('client-portal/js/login.js') }}"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<script>
    $(document).ready(function() {

    });
</script>

@endsection