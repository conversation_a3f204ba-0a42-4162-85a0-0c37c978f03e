@extends('clientPortal.layout')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/budget-form.css') }}">

@endsection

@section('content')
<!-- ============================================================================
     BUDGET FORM PAGE - MAIN CONTENTS
     ============================================================================ -->

<div class="content-wrapper">
    <!-- ========================================================================
         HEADER SECTION - Page Title and Financial Year Selector
         ======================================================================== -->
    <div class="content-header">
        <div class="Container_Fluid_Main">

            <div class="row headTitleBtm">
                <div class="col-md-8">
                     <div class="GpBoxHeading no-bdr pb0"> 
                        <h4 style="position: relative;top: 7px;">
                            {{ isset($annualBudget) ? 'Edit' : 'Add' }} Budget for {{ $unit->alias }}
                        </h4>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <form method="GET" id="yearSelectForm" action="{{ route('budget.allocation.edit', ['unit_id' => $unit->clientUnitId, 'financial_year' => $financialYear]) }}">
                        <input type="hidden" name="unit_id" value="{{ $unit->clientUnitId }}">
                        <select style="border: 1px solid #e5e5e5 !important;" name="financial_year" id="financial_year" class="form-control filter">
                            @foreach($financialYears as $year => $label)
                            <option value="{{ $year }}" {{ $financialYear == $year ? 'selected' : '' }}>
                                Financial Year {{ $label }}
                            </option>
                            @endforeach
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- ========================================================================
         MAIN CONTENT SECTION
         ======================================================================== -->
    <section class="ClientCtBG content">
        <div class="Container_Fluid_Main">
            <div class="row">
                <div class="col-md-12">
                    <div class="message-container"></div>

                    <!-- Success Message Display -->
                    @if(session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                    @endif

                    <!-- ================================================================
                     ANNUAL BUDGET SECTION
                     ================================================================ -->
                    <div class="marginCenter GpBoxAuto shadowWidget">
                        <form method="POST" action="{{ route('budget.allocation.store') }}" id="budgetForm">

                            @csrf
                            <input type="hidden" name="unit_id" value="{{ $unit->clientUnitId }}">
                            <input type="hidden" name="financial_year" value="{{ $financialYear }}">

                            <div class="MainBoxRow mb25">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="BugTable" for="annual_budget">Annual Budget Amount (£)</label>
                                            <input type="number" step="0.01" min="0" class="BugInp form-control" id="annual_budget" name="annual_budget"
                                                data-input-type="annual-budget" data-calculation-input
                                                value="{{ isset($annualBudget) ? $annualBudget->annual_budget : old('annual_budget', 0) }}" required>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- ================================================================
                             AUTO GENERATE WEEKS SECTION
                             ================================================================ -->
                            @if(!isset($weeklyBudgets) || $weeklyBudgets->count() == 0)
                            <div class="marginCenter GpBoxAuto shadowWidget mt-4" id="autoGenerateSection">
                                <h5 class="BudTitle GpBoxHeading">Auto Generate Weeks</h5>
                                <form id="autoGenerateForm">

                                    <input type="hidden" name="unit_id" value="{{ $unit->clientUnitId }}">
                                    <input type="hidden" name="financial_year" value="{{ $financialYear }}">
                                    <input type="hidden" name="annual_budget" id="auto_annual_budget">

                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="first_end_date">First Week End Date</label>
                                                <input type="date" class="form-control InpBudget" id="first_end_date" name="first_end_date"
                                                    data-input-type="first-end-date" data-auto-generate-input
                                                    value="{{ $financialYear }}-04-07" required>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="week_interval">Week Interval (days)</label>
                                                <input type="number" min="1" class="form-control InpBudget" id="week_interval" name="week_interval"
                                                    data-input-type="week-interval" data-auto-generate-input value="7" required>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="number_of_weeks">Number of Weeks</label>
                                                <input type="number" min="1" class="form-control InpBudget" id="number_of_weeks" name="number_of_weeks"
                                                    data-input-type="number-of-weeks" data-auto-generate-input value="52" required>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="weekly_allowance_percentage">Weekly Allowance (%)</label>
                                                <input type="number" min="0" max="100" step="0.01" class="form-control InpBudget" id="weekly_allowance_percentage" name="weekly_allowance_percentage"
                                                    data-input-type="weekly-allowance-percentage" data-auto-generate-input value="0">
                                                <small class="form-text text-muted">Percentage of weekly budget to be allocated as weekly allowance</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12 text-right">
                                            <button type="button" id="previewGenerateBtn" class="btn btn-primary" data-action="preview-generate">Preview Generated Weeks</button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- ================================================================
                             PREVIEW GENERATED WEEKS SECTION
                             ================================================================ -->
                            <div class="marginCenter GpBoxAuto shadowWidget mt-4" id="previewSection" style="display: none;">
                                <h5 class="BudTitle">Preview Generated Weeks</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered budgetTble" id="previewTable">
                                        <thead>
                                            <tr>
                                                <th>Wk #</th>
                                                <th>Week Ending</th>
                                                <th style='text-align: end;'>Weekly Budget</th>
                                                <th style='text-align: end;'>Weekly Allowance</th>
                                                <th style='text-align: end;'>Special Allowance</th>
                                                <th style='text-align: end;'>Internal Transfers (i)</th>
                                                <th style='text-align: end;'>TA llowance for the week</th>
                                                <th style='text-align: end;'>Total Weekly Utilisation (i)</th>
                                                <th style='text-align: end;'>% of Utilisation</th>
                                                <th style='text-align: end;'>Weekly Unutilised Fund</th>
                                                <th style='text-align: end;'>Consolidated Unutilised Fund</th>
                                                <th style='text-align: end;'>Balance Fund</th>
                                                <th>Notes</th>
                                            </tr>
                                        </thead>
                                        <tbody id="previewTableBody">
                                            <!-- Preview data will be inserted here -->
                                        </tbody>
                                    </table>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-12 text-right">
                                        <button type="button" id="saveGeneratedBtn" class="btn btn-primary" data-action="save-generated">Save and Proceed</button>
                                        <button type="button" id="cancelGeneratedBtn" class="btn btn-three " data-action="cancel-generated">Cancel</button>
                                    </div>
                                </div>
                            </div>






                        </form>
                    </div>
                    @endif

                    <!-- ================================================================
                     WEEKLY BUDGET ALLOCATIONS TABLE SECTION
                     ================================================================ -->
                    @if(isset($weeklyBudgets) && $weeklyBudgets->count() > 0)
                    <div class="marginCenter GpBoxAuto shadowWidget mt-4">
                        <div class="row">
                            <div class="col-md-7">
                                <h5 style="margin-top: 0;border: none;" class="BudTitle mb-0">Weekly Budget Allocations - Financial Year {{ $financialYear }}-{{ $financialYear + 1 }}</h5>
                            </div>
                            <div class="col-md-5 text-right">
                                <div class="recalculate-actions">
                                    <button type="button" class="btn-primary" id="recalculateBtn" data-action="recalculate">
                                        Recalculate
                                    </button>
                                    <div id="saveActions" style="display: none;">
                                        <button type="button" class="btn btn-success" id="saveChangesBtn" data-action="save-changes">
                                            <i class="fa fa-save mr-1"></i> Save Changes
                                        </button>
                                        <button type="button" class="btn btn-danger ml-2" id="cancelChangesBtn" data-action="cancel-changes">
                                            <i class="fa fa-times mr-1"></i> Cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered budgetTble" id="weeklyBudgetsTable">
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="selectAllCheckbox" data-checkbox-type="select-all" title="Select All"></th>
                                        <th>Week</th>
                                        <th data-column="budget-id" style="background-color: #ffeb3b;">Budget ID</th>
                                        <th class="week-ending-header" data-column="week-ending">Week<br> Ending</th>
                                        <th data-column="weekly-budget">Weekly<br> Budget</th>
                                        <th data-column="weekly-allowance">Weekly<br> Allowance</th>
                                        <th data-column="special-allowance">Special<br> Allowance</th>
                                        <th data-column="internal-transfers">Internal<br>Transfers</th>
                                        <th data-column="total-allocation">Total Allowance<br>for the week</th>
                                        <th data-column="total-utilisation">Total Weekly<br>Utilisation</th>
                                        <th data-column="percent-utilisation">% of <br>Utilisation</th>
                                        <th data-column="weekly-balance">Weekly<br> Balance</th>
                                        <th data-column="cumulative-balance">Cumulative<br> Balance</th>
                                        <th data-column="balance-fund">Balance<br> Fund</th>
                                        <th  data-column="notes">Notes</th>
                                        <th style="text-align: end;" data-column="actions">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(isset($weeks) && count($weeks) > 0)
                                    @foreach($weeks as $weekNum => $week)
                                    @php
                                    $weeklyBudget = $week instanceof \App\Models\ClientUnitWeeklyBudget
                                    ? $week : null;

                                    $weeklyBudgetAmount = $weeklyBudget
                                    ? $weeklyBudget->weekly_budget
                                    : ($week['weekly_budget'] ?? 0);

                                    $weeklyAllowance = $weeklyBudget
                                    ? $weeklyBudget->weekly_allowance
                                    : ($week['weekly_allowance'] ?? 0);

                                    $specialAllowance = $weeklyBudget
                                    ? $weeklyBudget->special_allowance
                                    : ($week['special_allowance'] ?? 0);

                                    $internalTransfers = $weeklyBudget
                                    ? $weeklyBudget->internal_transfers
                                    : 0;

                                    $totalAllocation = $weeklyBudget
                                    ? $weeklyBudget->total_weekly_allocation
                                    : ($weeklyBudgetAmount + $weeklyAllowance + $specialAllowance + $internalTransfers);

                                    $totalUtilisation = $weeklyBudget
                                    ? $weeklyBudget->total_weekly_utilisation
                                    : 0;

                                    $percentUtilisation = $totalAllocation > 0
                                    ? ($totalUtilisation / $totalAllocation) * 100
                                    : 0;

                                    $weeklyBalance = $weeklyBudget
                                    ? $weeklyBudget->weekly_unutilised_fund
                                    : ($totalAllocation - $totalUtilisation);

                                    $cumulativeBalance = $weeklyBudget
                                    ? $weeklyBudget->consolidated_unutilised_fund
                                    : 0;

                                    $balanceFund = $weeklyBudget
                                    ? $weeklyBudget->balance_fund
                                    : 0;

                                    $notes = $weeklyBudget
                                    ? $weeklyBudget->notes
                                    : '';

                                    $weeklyBudgetId = $weeklyBudget
                                    ? $weeklyBudget->id
                                    : null;
                                    @endphp
                                    <tr data-id="{{ $weeklyBudgetId }}" data-row-type="budget-row" data-week-number="{{ $weekNum }}" data-original-values="{{ json_encode([
                                            'week_number' => $weekNum,
                                            'weekly_budget' => $weeklyBudgetAmount,
                                            'weekly_allowance' => $weeklyAllowance,
                                            'special_allowance' => $specialAllowance,
                                            'total_weekly_allocation' => $totalAllocation,
                                            'total_weekly_utilisation' => $totalUtilisation,
                                            'percent_utilisation' => $percentUtilisation,
                                            'weekly_unutilised_fund' => $weeklyBalance,
                                            'consolidated_unutilised_fund' => $cumulativeBalance,
                                            'internal_transfers' => $internalTransfers,
                                            'balance_fund' => $balanceFund,
                                            'notes' => $notes
                                        ]) }}">
                                        <td><input type="checkbox" data-checkbox-type="week-row" data-week-id="{{ $weeklyBudgetId }}" data-week-number="{{ $weekNum }}"></td>
                                        <td data-cell-type="week-number">{{ $weekNum }}</td>
                                        <td data-cell-type="budget-id" data-column="budget-id" style="background-color: #ffeb3b; font-weight: bold;">{{ $weeklyBudgetId ?? 'NULL' }}</td>
                                        <td data-cell-type="date" data-column="week-ending">{{ \Carbon\Carbon::parse($week['end_date'] ?? $week->week_end_date)->format('d M Y') }}</td>
                                        <td data-cell-type="weekly-budget" data-column="weekly-budget">£{{ number_format($weeklyBudgetAmount, 2) }}</td>
                                        <td data-cell-type="weekly-allowance" data-column="weekly-allowance">£{{ number_format($weeklyAllowance, 2) }}</td>
                                        <td data-cell-type="special-allowance" data-column="special-allowance" data-editable="true" data-field="special_allowance">£{{ number_format($specialAllowance, 2) }}</td>
                                        <td data-cell-type="internal-transfers" data-column="internal-transfers">
                                            £{{ number_format($internalTransfers, 2) }}
                                            <div class="transfer-buttons mt-1">
                                                <button type="button" class=" btn-three-sm"
                                                    data-action="transfer" data-button-type="transfer"
                                                    data-week="{{ $weekNum }}" data-unit="{{ $unit->clientUnitId }}"
                                                    data-balance="{{ $balanceFund }}" title="Make Transfer">
                                                    <i class="fa fa-exchange"></i>
                                                </button>
                                                <button type="button" class="btn btn-three-sm ml3"
                                                    data-action="transfer-history" data-button-type="transfer-history"
                                                    data-week="{{ $weekNum }}" data-unit="{{ $unit->clientUnitId }}"
                                                    data-unit-name="{{ $unit->name }}" title="View Transfer History">
                                                    <i class="fa fa-info"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td data-cell-type="total-allocation" data-column="total-allocation">£{{ number_format($totalAllocation, 2) }}</td>
                                        <td data-cell-type="total-utilisation" data-column="total-utilisation">£{{ number_format($totalUtilisation, 2) }}</td>
                                        <td data-cell-type="percent-utilisation" data-column="percent-utilisation">{{ number_format($percentUtilisation, 2) }}%</td>
                                        <td data-cell-type="weekly-balance" data-column="weekly-balance">£{{ number_format($weeklyBalance, 2) }}</td>
                                        <td data-cell-type="cumulative-balance" data-column="cumulative-balance">£{{ number_format($cumulativeBalance, 2) }}</td>
                                        <td data-cell-type="balance-fund" data-column="balance-fund">£{{ number_format($balanceFund, 2) }}</td>
                                        <td data-cell-type="notes" data-column="notes" data-editable="true" data-field="notes">
                                            @if(!empty($notes))
                                            <button class="BtToolTip" data-position="top">
                                                <i class="fa fa-eye" aria-hidden="true"></i>
                                                <span class="MyToolTip">
                                                    {{ $notes  }}
                                                </span>
                                            </button>
                                            @endif
                                            <span class="EmpNote">{{ substr($notes, 0, 20)  }}</span> 
                                        </td>
                                        <!-- {{ $notes  }} -->
                                          

                                        <td style="text-align: end;" data-cell-type="actions" data-column="actions">
                                            <button type="button" class="btn btn-three-sm" data-action="edit-row" data-button-type="edit-row" data-week-id="{{ $weeklyBudgetId }}">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                    @else
                                    <tr>
                                        <td colspan="15" class="text-center">No weekly budgets found. Use the form above to generate weeks.</td>
                                    </tr>
                                    @endif
                                </tbody>
                                <tfoot>
                                    <tr id="weeklyBudgetsTotalsRow">
                                        <th colspan="3" style="text-align:left">Total:</th>
                                        <th style="text-align:left" id="total-weekly-budget">£0.00</th>
                                        <th style="text-align:left" id="total-weekly-allowance">£0.00</th>
                                        <th style="text-align:left" id="total-special-allowance">£0.00</th>
                                        <th style="text-align:left" id="total-internal-transfers">£0.00</th>
                                        <th style="text-align:left" id="total-allocation">£0.00</th>
                                        <th style="text-align:left" id="total-utilisation">£0.00</th>
                                        <th style="text-align:left" id="total-percent-utilisation">0.00%</th>
                                        <th style="text-align:left" id="total-weekly-balance">£0.00</th>
                                        <th style="text-align:left" id="total-cumulative-balance">£0.00</th>
                                        <th style="text-align:left" id="total-balance-fund">£0.00</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    @endif

                    <!-- Include Transfer Modals -->
                    @include('clientPortal.partials.transfer-modals')
                </div>
            </div>
        </div>
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->


<div class="alert alert-success" role="alert">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <strong>Success!</strong> You have been signed in successfully!
</div>


@endsection

@section('scripts')
<!-- Budget Form JavaScript Modules -->
<script src="{{ asset('js/budget-utilities.js') }}"></script>
<script src="{{ asset('js/budget-auto-generate.js') }}"></script>
<script src="{{ asset('js/budget-recalculate.js') }}"></script>
<script src="{{ asset('js/budget-edit.js') }}"></script>
<script src="{{ asset('js/budget-transfer.js') }}"></script>
<script src="{{ asset('js/budget-form.js') }}"></script>
<script src="{{ asset('js/budget-totals.js') }}"></script>

<!-- Compatibility Fix for CSS Selector Refactoring -->
<script src="{{ asset('js/budget-compatibility-fix.js') }}"></script>

{{-- @if(config('app.debug'))
<!-- Testing Suite (Development Only) -->
<script src="{{ asset('js/budget-form-test.js') }}"></script>
<!-- Emergency Rollback (Development Only) -->
<script src="{{ asset('js/budget-emergency-rollback.js') }}"></script>
@endif --}}

<script>
    // Set global variables for JavaScript modules
    window.routes = {
        generateWeeks: '{{ route("budget.generate-weeks") }}',
        saveGeneratedWeeks: '{{ route("budget.save-generated-weeks") }}',
        updateWeekly: '{{ route("budget.update-weekly") }}',
        updateRecalculated: '{{ route("budget.update-recalculated") }}',
        transferUnits: '{{ route("budget.transfer-units") }}',
        transferHistory: '{{ route("budget.transfer-history") }}',
        executeTransfer: '{{ route("budget.execute-transfer") }}',
        budgetEdit: '{{ route("budget.allocation.edit", ["unit_id" => $unit->clientUnitId, "financial_year" => $financialYear]) }}'
    };
    window.csrfToken = '{{ csrf_token() }}';
    window.unitId = '{{ $unit->clientUnitId }}';
    window.financialYear = '{{ $financialYear }}';
    // Financial year change handler is now handled in budget-form.js
</script>

<script>
    window.setTimeout(function() {
        $(".alert").fadeTo(500, 0).slideUp(500, function() {
            $(this).remove();
        });
    }, 4000);
</script>

@endsection