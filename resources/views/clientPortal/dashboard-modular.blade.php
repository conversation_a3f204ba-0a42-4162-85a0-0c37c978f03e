@extends('clientPortal.layout')
@section('title', 'Dashboard | Client Portal')

@push('styles')
    <link rel="stylesheet" href="{{ asset('client-portal/css/dashboard-components.css') }}">
@endpush

@section('content')
{{-- Use the modular dashboard wrapper --}}
@include('common.dashboard.main-wrapper', [
    'clientId' => auth()->user()->portal_id,
    'permissions' => [
        'view_budget_summary' => true,
        'view_budget_charts' => true,
        'view_notes_reminders' => true,
        'view_smart_view' => true,
        'view_booking_reasons' => true,
        'view_expense_overview' => true,
    ]
])
@endsection

@section('scripts')
{{-- Include Highcharts for charts --}}
<script src="{{ asset('unit-portal/js/highcharts.js') }}"></script>
<script src="{{ asset('unit-portal/js/variable-pie.js') }}"></script>
<script src="{{ asset('client-portal/js/bootbox.min.js') }}"></script>

{{-- Include modular dashboard components --}}
<script src="{{ asset('client-portal/js/dashboard-components/main.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/filter.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/budget-summary-cards.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/budget-donut-chart.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/expenses-donut-chart.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard-components/remaining-components.js') }}"></script>

{{-- jQuery and jQuery UI --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    // Dashboard will auto-initialize via main.js
    console.log('Modular dashboard loaded');
});
</script>
@endsection
