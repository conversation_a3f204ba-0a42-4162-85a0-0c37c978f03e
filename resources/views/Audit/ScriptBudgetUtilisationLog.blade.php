@extends('clientPortal.layout')
@section('title', 'Script Budget Utilisation Log | Client Portal')

@section('content')
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper clientvalue" client="{{ auth()->user()->portal_id }}">
    <div class="Container_Fluid_Main">
        <div class="row headTitleBtm">
            <div class="col-sm-12">
                <div class="GpBoxHeading no-bdr pb0">
                    <h4>Script Budget Utilisation Log</h4>
                    <p class="text-muted">Budget Update Script Execution History</p>
                </div>
            </div>
        </div>

        <div class="MainBoxRow">
            <section class="ClientCtBG content">
                <div class="Container_Datatable">
                    <table class="DataAlign table table-striped table-bordered table-hover" id="scriptLogTable" style="background-color: #fff;">
                        <thead style="background-color: #f5c6cb;">
                            <tr>
                                <th style="text-align: left;">ID</th>
                                <th>Booking ID</th>
                                <th>Unit Name</th>
                                <th>Client Name</th>
                                <th>Week/Year</th>
                                <th>Booking Details</th>
                                <th>Initial Cost</th>
                                <th>Final Cost</th>
                                <th>Weekly Total</th>
                                <th>Status</th>
                                <th>Adjustment Type</th>
                                <th>Error Log</th>
                                <th>Processed Count</th>
                                <th>Last Processed</th>
                                <th>Created At</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </section>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

<script>
// Wait for page to be fully loaded
$(window).on('load', function() {
    console.log('Page loaded, jQuery version:', $.fn.jquery);
    console.log('DataTables available:', typeof $.fn.DataTable !== 'undefined');
    
    // Double check jQuery and DataTables are available
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded!');
        return;
    }
    
    if (typeof $.fn.DataTable === 'undefined') {
        console.error('DataTables is not loaded!');
        return;
    }
    
    console.log('Initializing DataTable...');
    console.log('Ajax URL: {{ route("audit.script-budget-log.data") }}');
    
    // Test the endpoint first
    $.ajax({
        url: '{{ route("audit.test-data") }}',
        method: 'GET',
        success: function(response) {
            console.log('Test data response:', response);
        },
        error: function(xhr, status, error) {
            console.error('Test data error:', error, xhr.responseText);
        }
    });
    
    $('#scriptLogTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("audit.script-budget-log.data") }}',
            type: 'GET',
            error: function(xhr, error, thrown) {
                console.error('DataTable AJAX Error:', error, thrown);
                console.error('Response:', xhr.responseText);
                console.error('Status:', xhr.status);
                alert('Error loading data: ' + (xhr.status || 'Unknown error') + '. Check console for details.');
            }
        },
        columns: [
            { data: 'id', name: 'logs.id' },
            { data: 'booking_id', name: 'logs.booking_id' },
            { data: 'unit_name_formatted', name: 'unit_name_formatted', orderable: false },
            { data: 'client_name_formatted', name: 'client_name_formatted', orderable: false },
            { data: 'week_year', name: 'week_year', orderable: false },
            { data: 'booking_details_formatted', name: 'booking_details_formatted', orderable: false },
            { data: 'initial_value', name: 'logs.initial_value' },
            { data: 'unit_final_cost', name: 'logs.unit_final_cost' },
            { data: 'total_weekly_utilisation', name: 'logs.total_weekly_utilisation' },
            { data: 'status', name: 'logs.status' },
            { data: 'adjustment_type', name: 'logs.adjustment_type' },
            { data: 'error_log', name: 'logs.error_log' },
            { data: 'processed_count', name: 'logs.processed_count' },
            { data: 'last_processed_at', name: 'logs.last_processed_at' },
            { data: 'created_at', name: 'logs.created_at' }
        ],
        order: [[0, 'desc']],
        pageLength: 10,
        lengthMenu: [[10, 25, 50], [10, 25, 50]],
        language: {
            processing: 'Loading data...',
            emptyTable: 'No script execution logs found',
            zeroRecords: 'No matching records found'
        }
    });
    
    console.log('DataTable initialized');
}); // End of window.on('load')
</script>
@endsection

<style>
    .badge {
        font-size: 0.75em;
        padding: 0.25em 0.5em;
        border-radius: 3px;
        color: white;
    }
    .badge-success { background-color: #28a745; }
    .badge-danger { background-color: #dc3545; }
    .badge-warning { background-color: #ffc107; color: #212529; }
    .badge-info { background-color: #17a2b8; }
    .badge-secondary { background-color: #6c757d; }
    
    .booking-details {
        max-width: 200px;
        font-size: 0.85em;
    }
    .error-log {
        max-width: 150px;
        font-size: 0.8em;
        color: #dc3545;
    }
    
    .table td {
        vertical-align: middle;
    }
    .table th {
        white-space: nowrap;
        background-color: #f8f9fa;
    }
</style>
