@extends('common.layout')

@section('styles')
<link rel="stylesheet" href="{{asset('unit-portal/css/reports.css')}}">
@endsection
@section('content')


<div class="Container_Fluid_Main">


  <div class="row headTitleBtm">
    <div class="col-md-12">
      <div class="GpBoxHeading no-bdr pb0">
        <h4>
          Reports
        </h4>
      </div>
    </div>
  </div>

  <div class="season_tabs">

    <div class="season_tab">
      <input type="radio" id="tab-1" name="tab-group-1" checked>
      <label class="RepLabel" for="tab-1">Staff-wise Reports</label>

      <div class="Report_Content">

        <div class="row">
          <div class="col-md-6">
            <a href="#">
              <div class="Reportcard">
                <h3>Total Cost Incurred by Staff</h3>
                <p>Evaluate individual staff performance, shift history, and costs.</p>
                <div class="dropdown">
                  <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                    <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-right drpPad">
                    <li><a href="#">Follow</a></li>
                    <li><a href="#">Unfollow</a></li>
                  </ul>
                </div>
              </div>
            </a>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Total Cost Incurred by Staff</h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right drpPad">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
              </a>
            </div>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Staff Name & ID </h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right drpPad">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Total Shifts Completed </h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right drpPad">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Shift Types Worked </h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right drpPad">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Total Hours Worked</h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right drpPad">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Hourly Rate </h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Shift Approval Status</h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right drpPad">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="Reportcard">
              <h3>Cancellations or No-shows</h3>
              <p>Evaluate individual staff performance, shift history, and costs.</p>
              <div class="dropdown">
                <button class="BtTogle dropdown-toggle" type="button" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-right drpPad">
                  <li><a href="#">Follow</a></li>
                  <li><a href="#">Unfollow</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>



      </div>
    </div>

    <div class="season_tab">
      <input type="radio" id="tab-2" name="tab-group-1">
      <label class="RepLabel" for="tab-2">Unit-wise Reports</label>

      <div class="Report_Content">
        <span>Unit-wise Reports</span>
      </div>
    </div>

    <div class="season_tab">
      <input type="radio" id="tab-3" name="tab-group-1">
      <label class="RepLabel" for="tab-3">Budget Reports</label>

      <div class="Report_Content">
        <span>Budget Reports</span>
      </div>
    </div>

    <div class="season_tab">
      <input type="radio" id="tab-4" name="tab-group-1">
      <label class="RepLabel" for="tab-4">Shift Summary Reports</label>

      <div class="Report_Content">
        <span>Shift Summary Reports</span>
      </div>
    </div>

    <div class="season_tab">
      <input type="radio" id="tab-5" name="tab-group-1">
      <label class="RepLabel" for="tab-5">Timesheet & Invoice Reports</label>

      <div class="Report_Content">
        <span>Timesheet & Invoice Reports</span>
      </div>
    </div>

  </div> <!-- End season_tabs -->


  <div class="row headTitleBtm">
    <div class="col-md-12">
      <div class="GpBoxHeading no-bdr pb0">
        <h4>
          View Reports
        </h4>
      </div>
    </div>
  </div>

  <h5 class="titlesm">Generate new Reports</h5>
  <div class="MainBoxRow" style="min-height: 72px;">
    <div class="'row">
      <div class="col-md-2" style="padding-left: 0;">
        <input class="txt_bx" type="text" placeholder="Date Range"> <br>
       <div class="toggle-filters" onclick="toggleFilters()">More Filters</div>
      </div>
      <div class="col-md-8">
        
        <div id="filters" class="filters-wrapper hidden">
          <div class="filter-container">
            <div class="filter-box">
              <input class="txt_bx" type="text" placeholder="Date Range"> <br>
            </div>
            <div class="filter-box">
              <input class="txt_bx" type="text" placeholder="Date Range"> <br>
            </div>
            <div class="filter-box">
              <input class="txt_bx" type="text" placeholder="Date Range"> <br>
            </div>
            <div class="filter-box">
              <input class="txt_bx" type="text" placeholder="Date Range"> <br>
            </div>
            <div class="filter-box">
              <input class="txt_bx" type="text" placeholder="Date Range"> <br>
            </div>
            
          </div>
        </div>
      </div>
      <div class="col-md-2 text-right" style="padding-left: 0;">
        <button class="btn-three">Filter</button>
        <button class="btn-primary">Download</button>

      </div>
    </div>
  </div>
  

  <script>
    let filtersVisible = false;
  
    function toggleFilters() {
      const filters = document.getElementById('filters');
      const toggleText = document.querySelector('.toggle-filters');
      filtersVisible = !filtersVisible;
      filters.classList.toggle('hidden', !filtersVisible);
      toggleText.textContent = filtersVisible ? 'Hide Filters' : 'Show Filters';
    }
  </script>








  <!-- <div class="row">
            <div class="col-md-9">
                <div class="chartOne graphLineRgn" id="graphLineRgn" action="{{route('dashboard.graph')}}"
                    token="{{ csrf_token() }}">
                    <div id="container"></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="chartOne" id="actvtyGuage">
                    <figure class="highcharts-figure">
                        <div id="container"></div>
                    </figure>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="smartbg">
                    <h4 class="ReasonBkTitle">Yearly Report (Month wise)</h4>
                </div>

                <div class="col-md-12">
                    <div class="col-md-12 ">
                        <div class="Gloader" id="monthlySplineChart" data-fetch="{{ route('dashboard.yeargraph') }}">

                        </div>
                    </div>
                </div>
            </div>
            <div class="row no-margin">
                <div class="col-md-12">
                    <div class="smartbg">
                        <form action="#" method="GET" id="management_form">
                            <div class="row">
                              <div class="col-sm-2">
                                <div class="form-group">
                                  <label for="securityUnit">Security Unit</label>
                                  <select class="form-control select2" id="securityUnit" name="securityUnit">
                                    <option value="">Select Unit</option>
                                    <option value="1">Unit 1</option>
                                    <option value="2">Unit 2</option>
                                    <option value="3">Unit 3</option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-sm-2">
                                <div class="form-group">
                                  <label for="fiscalYear">Select FY</label>
                                  <select class="form-control select2" id="fiscalYear" name="fiscalYear">
                                    <option value="">Select FY</option>
                                    <option value="2024">2024-2025</option>
                                    <option value="2025">2025-2026</option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-sm-2">
                                <div class="form-group">
                                  <label for="annual">Annual</label>
                                  <select class="form-control select2" id="annual" name="annual">
                                    <option value="">Select</option>
                                    <option value="yes">Yes</option>
                                    <option value="no">No</option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-sm-2">
                                <div class="form-group">
                                  <label for="month">Select Month</label>
                                  <select class="form-control select2" id="month" name="month">
                                    <option value="">Select Month</option>
                                    <option value="1">January</option>
                                    <option value="2">February</option>
                                    <option value="3">March</option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-sm-2">
                                <div class="form-group">
                                  <label for="week">Select Week</label>
                                  <select class="form-control select2" id="week" name="week">
                                    <option value="">Select Week</option>
                                    <option value="1">Week 1</option>
                                    <option value="2">Week 2</option>
                                    <option value="3">Week 3</option>
                                  </select>
                                </div>
                              </div>
                              <div class="col-sm-2 m-t-25">
                                <button type="submit" class="btn btn-primary">Filter</button>
                              </div>
                          
                            </div>
                          </form>
                          
                    </div>
                </div>
            </div>

            <div class="row no-margin">
                <div class="col-md-12">
                    <div class="smartbg">
                        <table class="table table-bordered">
                            <thead>
                              <tr>
                                <th colspan="5">Generate New Reports</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td>O</td>
                                <td>Daily shift booking reports</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                              <tr>
                                <td>O</td>
                                <td>1 to 1 shifts reports</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                              <tr>
                                <td>O</td>
                                <td>Weekly Budget vs Expenses Report</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                              <tr>
                                <td>O</td>
                                <td>Reasons for booking analysis</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                              <tr>
                                <td>O</td>
                                <td>Outstanding invoices reports</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                              <tr>
                                <td>O</td>
                                <td>New report (Add)</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                              <tr>
                                <td>O</td>
                                <td>New report (Add)</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                              <tr>
                                <td>O</td>
                                <td>New report (Add)</td>
                                <td><button class="btn btn-light">⟳</button></td>
                                <td><button class="btn btn-light">👁️</button></td>
                                <td><button class="btn btn-light">⬇️</button></td>
                              </tr>
                            </tbody>
                        </table>
                          
                        <table class="table table-bordered mt-4">
                        <thead>
                            <tr>
                            <th colspan="5"><strong>Recently Created Reports</strong></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                            <td>O</td>
                            <td>Daily shift booking reports<br>Week 14<br>12/06/2025</td>
                            <td>Generated</td>
                            <td><button class="btn btn-light">👁️</button></td>
                            <td><button class="btn btn-light">⬇️</button></td>
                            </tr>
                            <tr>
                            <td>O</td>
                            <td>1 to 1 shifts reports<br>Week 12<br>12/06/2025</td>
                            <td>Generated</td>
                            <td><button class="btn btn-light">👁️</button></td>
                            <td><button class="btn btn-light">⬇️</button></td>
                            </tr>
                            <tr>
                            <td>O</td>
                            <td>Weekly Budget vs Expenses Report<br>June 25<br>12/06/2025</td>
                            <td>Generated</td>
                            <td><button class="btn btn-light">👁️</button></td>
                            <td><button class="btn btn-light">⬇️</button></td>
                            </tr>
                            <tr>
                            <td>O</td>
                            <td>Reasons for booking analysis<br>June 25<br>12/06/2025</td>
                            <td>Generated</td>
                            <td><button class="btn btn-light">👁️</button></td>
                            <td><button class="btn btn-light">⬇️</button></td>
                            </tr>
                            <tr>
                            <td>O</td>
                            <td>Outstanding invoices reports<br>July 25<br>12/06/2025</td>
                            <td>Generated</td>
                            <td><button class="btn btn-light">👁️</button></td>
                            <td><button class="btn btn-light">⬇️</button></td>
                            </tr>
                        </tbody>
                        </table>                    
                    </div>
                </div>
            </div> -->





</div>
</div>
</div>



@endsection


@section('scripts')
<script src="{{asset('unit-portal/js/highcharts.js')}}"></script>
<script src="{{ asset('unit-portal/js/dashboard.js') }}?v={{ time() }}"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
@endsection