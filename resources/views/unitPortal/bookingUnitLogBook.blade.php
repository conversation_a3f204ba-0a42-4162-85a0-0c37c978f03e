<style>
#BookingUnitLogBook {
    opacity: 1 !important;
}
</style>
<div id="BookingUnitLogBook" unit-name="{{auth()->user()->portal->name}}" class="modal fade" role="dialog"
    get-url="{{route('get.unit.chat')}}" token="{{ csrf_token() }}">
    <div class="ModalShiftLog modal-dialog" style="transform: translate(0, -13%);">
        <div class="ShiftLogHeder modal-header">
            <button type="button" class="close BookingUnitLogBook" style="color: #1d1919;" data-dismiss="modal">X</button> 
            <div class="logEntry">
                <h2 class="TopHeadTxt"></h2>
            </div>
            <form fetch="{{route('save.unit.chat')}}" token="{{csrf_token()}}" class="chat_form" name="chat_form"
                id="chat_form" method="POST">
                <div class="row mt25 log-options">
                    <div class="col-md-10">
                        <label class="entrlab">Entry</label>
                        <input class="form-control" name="content" id="content" type="text" autocomplete="off" />
                        <input class="form-control" name="booking_id" id="booking_id" type="hidden" />
                        <input class="form-control" name="unit_id" id="unit_id" type="hidden" />
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary BtnLogNow newLogEntryAction">Log Now</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-body">
            <div class="history-tl-container">
                <ul class="tl chat_contents">
                </ul>
            </div>
        </div>
        <div class="modal-footer">
          
        </div>
    </div>
</div>