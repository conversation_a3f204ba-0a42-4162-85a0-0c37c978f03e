<div class="modal" id="newShiftModel">
    <div class="modal-dialog" style="width: 98%;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header header_txt">
                <h4 class="modal-title"><b>New Booking</b> <button type="button" class="close"
                        data-dismiss="modal">×</button> </h4>
            </div>
            <div class="row" style=" margin: 0; ">

            </div>
            <div class="modal-body" style="    padding: 22px 43px 20px !important;">
                <div class="box box-primary">
                    <input type="hidden" name="bookingId">
                    <input type="hidden" name="amendSelectedType">
                    <div class="box-body newBookingForm">
                        <div class="row shiftRow">
                            <div class="addNewBookingDiv" cost-fetch="{{route('get.timings')}}" token="{{csrf_token()}}"
                                categories="{{$categories}}" date="{{date('d-m-Y')}}" shifts="{{$shifts}}"
                                contacts="auth()->guard('unit')->user()->unit->contacts">
                            </div>
                            <div class="addBooksNewDivs">
                                <div class="full_width">

                                    <div class="fby_one w10Percntge">
                                        <p>Date</p>
                                        <input type="text" name="date[]" class="txt_bx datepicker_add">
                                    </div>
                                    <div class="fby_one w10Percntge">
                                        <p>Shift</p>
                                        <select name="shift[]" class="txt_bx shiftSel select2">
                                            <option value="">Select</option>
                                            @foreach($shifts as $shift)
                                            <option value="{{$shift->shiftId}}">{{$shift->name}}</option>
                                            @endforeach

                                        </select>
                                    </div>
                                    <input type="hidden" name="booked_user_id" class="sl_box" value="11">
                                    <input type="hidden" name="booked_portal" class="sl_box" value="unit">
                                    <input type="hidden" name="booked_portal_id" class="sl_box" value="22">
                                    <div class="fby_one w10Percntge">
                                        <p>Staff Category</p>
                                        <select name="category[]" class="txt_bx categorySwitch ">
                                            <option value="">Select</option>
                                            @foreach($categories as $category)
                                            <option value="{{$category->categoryId}}">{{$category->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="sby_one">
                                        <p>Numbers</p>
                                        <select name="numbers[]" class="txt_bx">
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                        </select>
                                    </div>
                                    <div class="fby_one w10Percntge">
                                        <p>Start</p>
                                        <input type="text" name="start_time[]" class="txt_bx startTime clockpicker">
                                    </div>
                                    <div class="fby_one w10Percntge">
                                        <p>End</p>
                                        <input type="text" name="end_time[]" class="txt_bx endTime clockpicker">
                                    </div>

                                    <div class="fby_one" style="width: 13%;">
                                        <p>Requested By</p>
                                        <input type="text" value="{{auth()->user()->name}}" name="requestedBy[]"
                                            class="txt_bx" readonly />

                                    </div>
                                    <div class="fby_one" style="width: 9%">
                                        <p>Reason</p>
                                        <select name="reasonBooking[]" class="txt_bx">
                                            <option value="">Select</option>
                                            <option value="1">Staff Sickness</option>
                                            <option value="2">Holiday cover</option>
                                            <option value="3">Vacant Position</option>
                                            <option value="4">New Resident admission</option>
                                            <option value="5">1 to 1 care</option>
                                            <option value="6">Extra staff requirement</option>
                                            <option value="7">Staff training day</option>
                                        </select>
                                    </div>
                                    <!-- Unit Dropdown -->
                                    <div class="fby_one w10Percntge">
                                        <p>Unit</p>
                                        <select name="unit[]" class="txt_bx">
                                            <option value="">Select Unit</option>
                                            @foreach($unitsdropdown as $unit)
                                                <option value="{{ $unit->clientUnitId }}">{{ $unit->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    
                                    <div class="fby_one">
                                        <p>Additional Notes(if)</p>
                                        <input type="text" name="impNotes[]" class="txt_bx">
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="right addnew_button" style="margin-top:20px;">
                                <input type="button" class="add_newrow btn-primary" style="width: 120px;margin-right: 6px;" value="Add New"
                                    manager="auth()->guard('unit')->user()->name">
                                <input type="button" url="{{ route('save') }}" token="{{csrf_token()}}"
                                    style="float: left; "
                                    class="add_save_new_booking btn-three" value="Save">
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </div>



    </div>
</div>
</div>