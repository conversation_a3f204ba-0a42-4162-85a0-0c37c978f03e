{{-- Main Dashboard Wrapper Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => null
])

@php
    // Use DashboardPermissions helper if no permissions provided
    if (!$permissions) {
        $permissions = \App\Helpers\DashboardPermissions::getDashboardPermissions();
    }
@endphp

<div class="content-wrapper clientvalue" client="{{ $clientId }}">
    <div class="Container_Fluid_Main">
        <!-- Header with Title and Filter -->
        <div class="content-header">
            <div class="row headTitleBtm">
                <div class="col-md-7">
                    <div class="GpBoxHeading no-bdr pb0">
                        <h4>Dashboard</h4>
                    </div>
                </div>
                <div class="col-sm-5 text-right">
                    @include('common.dashboard.components.filter', [
                        'selectedFilter' => request()->get('filter', 1)
                    ])
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <section class="ClientCtBG content">
            <!-- Budget Summary Cards -->
            @if(($permissions['view_budget_summary'] ?? true))
                @include('common.dashboard.components.budget-summary-cards', [
                    'clientId' => $clientId
                ])
            @endif

            <!-- Charts Row -->
            <div class="row">
                <!-- Budget Donut Chart -->
                @if(($permissions['view_budget_charts'] ?? true))
                    <div class="col-md-4">
                        @include('common.dashboard.components.budget-donut-chart', [
                            'clientId' => $clientId
                        ])
                    </div>
                @endif

                <!-- Expenses Donut Chart -->
                @if(($permissions['view_budget_charts'] ?? true))
                    <div class="col-md-4">
                        @include('common.dashboard.components.expenses-donut-chart', [
                            'clientId' => $clientId
                        ])
                    </div>
                @endif

                <!-- Notes and Reminders -->
                @if(($permissions['view_notes_reminders'] ?? true))
                    <div class="col-md-4">
                        @include('common.dashboard.components.notes-reminders', [
                            'clientId' => $clientId
                        ])
                    </div>
                @endif
            </div>

            <!-- Smart View Bar Chart -->
            @if(($permissions['view_smart_view'] ?? true))
                @include('common.dashboard.components.smart-view-chart', [
                    'clientId' => $clientId
                ])
            @endif

            <!-- Booking Reasons -->
            @if(($permissions['view_booking_reasons'] ?? true))
                @include('common.dashboard.components.booking-reasons', [
                    'clientId' => $clientId
                ])
            @endif

            <!-- Unit Expense Overview -->
            @if(($permissions['view_expense_overview'] ?? true))
                @include('common.dashboard.components.unit-expense-overview', [
                    'clientId' => $clientId
                ])
            @endif
        </section>
    </div>
</div>

{{-- Component Scripts --}}
@push('scripts')
    <script>
        // Set client information in localStorage
        localStorage.setItem('nsg-client-portal-login-user-id', "{{ $clientId }}");
        localStorage.setItem('nsg-client-portal-login-user-name', "{{ auth()->user()->portal->name ?? '' }}");
        
        // Initialize dashboard components
        $(document).ready(function() {
            // Initialize all dashboard components
            if (typeof DashboardComponents !== 'undefined') {
                DashboardComponents.init();
            }
        });
    </script>
@endpush
