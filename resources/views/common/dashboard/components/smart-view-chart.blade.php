{{-- Smart View Bar Chart Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => [
        'view_chart' => true,
        'change_filter' => true,
    ]
])

<div class="thisMonthBudgetVsExpense row mt25" 
     id="smart-view-component"
     data-fetch="{{ config('app.api_base_url') }}/client-portal/get-dashboard-bar-budget-usage"
     data-year="{{ date('Y') }}" 
     data-month="{{ date('m') }}"
     data-client="{{ $clientId }}">

    @if(($permissions['view_chart'] ?? true))
        <!-- Header with Filter -->
        <div class="marginCenter shadowWidget col-md-12">
            <div class="GpBoxHeading">
                <div class="row">
                    <div class="col-md-9">
                        <h4>Smart View -
                            <span class="smlFnt">
                                <span class="greenFont">Budget</span> vs
                                <span class="redFont">Expenses</span>
                            </span>
                        </h4>
                    </div>

                    @if(($permissions['change_filter'] ?? true))
                        <div class="col-md-3">
                            <select style="border: 1px solid #eae9e9 !important; margin-top: 2px;"
                                    name="smart_view_filter"
                                    id="smartViewMonthselect"
                                    class="form-control smartViewFilter">
                                <option value="1" {{ request()->get('filter') == 1 ? 'selected' : '' }}>This Month</option>
                                <option value="2" {{ request()->get('filter') == 2 ? 'selected' : '' }}>Last Month</option>
                                <option value="4" {{ request()->get('filter') == 4 ? 'selected' : '' }}>Next Month</option>
                            </select>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Chart Container -->
        <figure style="width: 100%; max-width: 100%;" class="highcharts-figure">
            <div id="usageThisMonth">
                <!-- Loading state -->
                <div class="chart-loading text-center" style="padding: 100px;">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p>Loading smart view chart...</p>
                </div>
            </div>
        </figure>
    @endif

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="col-md-12">
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-triangle"></i>
                <span class="error-message">Failed to load smart view chart</span>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize smart view chart component
    SmartViewChart.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions),
        chartContainer: '#usageThisMonth',
        filterSelector: '#smartViewMonthselect'
    });
});
</script>
@endpush
