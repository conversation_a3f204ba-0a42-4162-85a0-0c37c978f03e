{{-- Booking Reasons Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => [
        'view_booking_reasons' => true,
    ]
])

<div class="marginCenter shadowWidget ResonPaddCL row" id="booking-reasons-component">
    @if(($permissions['view_booking_reasons'] ?? true))
        <div class="col-md-12">
            <!-- Header -->
            <div class="GpBoxHeading mt25">
                <h4>Reason for Booking</h4>
            </div>

            <!-- Booking Reasons Grid -->
            <div class="row booking-reasons-grid" 
                 id="booking-reasons" 
                 data-fetch="{{ config('app.api_base_url') }}/client-portal/booking-counts-costs" 
                 data-client-id="{{ $clientId }}">
                
                <!-- Loading State -->
                <div class="loading-state col-md-12 text-center" style="padding: 50px;">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p>Loading booking reasons...</p>
                </div>

                <!-- Booking Reason Cards (populated via AJAX) -->
                <div class="reasons-container" style="display: none;">
                    <!-- Sickness -->
                    <div class="col-md-3 reason-card" data-reason="sickness">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>Sickness</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Holiday -->
                    <div class="col-md-3 reason-card" data-reason="holiday">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>Holiday</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Vacant -->
                    <div class="col-md-3 reason-card" data-reason="vacant">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>Vacant</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resident Admission -->
                    <div class="col-md-3 reason-card" data-reason="resident_admission">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>Resident Admission</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- One to One -->
                    <div class="col-md-3 reason-card" data-reason="one_to_one">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>One to One</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Extra Staff -->
                    <div class="col-md-3 reason-card" data-reason="extra_staff">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>Extra Staff</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Management -->
                    <div class="col-md-3 reason-card" data-reason="management">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>Management</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Others -->
                    <div class="col-md-3 reason-card" data-reason="others">
                        <div class="single_feature">
                            <div class="row" style="margin: 0;">
                                <h5>Others</h5>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseLeft">
                                        <h3>Count</h3>
                                        <p class="count-value">0</p>
                                    </div>
                                </div>
                                <div style="padding:0" class="col-md-6">
                                    <div class="stfBgseRIght">
                                        <h3>Cost</h3>
                                        <p class="cost-value">£ 0.00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="col-md-12">
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-triangle"></i>
                <span class="error-message">Failed to load booking reasons</span>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize booking reasons component
    BookingReasons.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions)
    });
});
</script>
@endpush
