{{-- Budget Donut Chart Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => [
        'view_chart' => true,
        'view_data_table' => true,
    ]
])

<div class="marginCenter GpBox thisMonthBudget shadowWidget" id="budget-donut-component">
    @if(($permissions['view_chart'] ?? true))
        <figure class="highcharts-figure">
            <h4 class="greenFont BudTitle">Budget</h4>

            <!-- Chart Container -->
            <div id="thisMonthBudgetPie"
                 data-fetch="{{ config('app.api_base_url') }}/client-portal/get-dashboard-donut-graph"
                 data-year="{{ date('Y') }}"
                 data-month="{{ date('m') }}"
                 data-client="{{ $clientId }}">
                <!-- Loading state -->
                <div class="chart-loading text-center" style="padding: 50px;">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p>Loading budget chart...</p>
                </div>
            </div>

            <!-- Chart Inner Summary -->
            <div class="graphInnerSUm"></div>

            @if(($permissions['view_data_table'] ?? true))
                <!-- Data Table -->
                <div class="chart-data-table">
                    <table cellpadding="0" cellspacing="0" border="0"
                           class="table table-striped table-bordered budget-table"
                           id="budgetTable">
                        <thead>
                            <tr>
                                <th>Units</th>
                                <th style='text-align: end;'>Amount</th>
                                <th style='text-align: end;'>Percentage</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data populated via AJAX -->
                            <tr class="loading-row">
                                <td colspan="4" class="text-center">
                                    <div class="spinner-border spinner-border-sm" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    Loading data...
                                </td>
                            </tr>
                        </tbody>
                        <tfoot></tfoot>
                    </table>
                </div>
            @endif
        </figure>
    @endif

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-triangle"></i>
            <span class="error-message">Failed to load budget chart</span>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize budget donut chart component
    BudgetDonutChart.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions),
        chartContainer: '#thisMonthBudgetPie',
        tableContainer: '#budgetTable'
    });
});
</script>
@endpush
