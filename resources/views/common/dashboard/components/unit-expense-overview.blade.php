{{-- Unit Expense Overview Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => [
        'view_expense_overview' => true,
    ]
])

<div class="marginCenter shadowWidget" 
     id="unit-expense-overview-component"
     data-fetch="{{ config('app.api_base_url') }}/client-portal/get-dashboard-line-charts" 
     data-year="{{ date('Y') }}"
     data-month="{{ date('m') }}"
     data-client="{{ $clientId }}">
    
    @if(($permissions['view_expense_overview'] ?? true))
        <!-- Header -->
        <div class="GpBoxHeading mt25">
            <h4>Unit Expense Overview</h4>
        </div>
        
        <!-- Chart Container -->
        <div id="containerLine">
            <!-- Loading state -->
            <div class="chart-loading text-center" style="padding: 100px;">
                <div class="spinner-border" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p>Loading expense overview chart...</p>
            </div>
        </div>
    @endif

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-triangle"></i>
            <span class="error-message">Failed to load unit expense overview</span>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize unit expense overview component
    UnitExpenseOverview.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions),
        chartContainer: '#containerLine'
    });
});
</script>
@endpush
