{{-- Notes and Reminders Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => [
        'view_notes' => true,
        'add_notes' => true,
        'edit_notes' => true,
        'delete_notes' => true,
        'view_reminders' => true,
        'add_reminders' => true,
        'edit_reminders' => true,
        'delete_reminders' => true,
    ]
])

<div class="marginCenter reminderList" id="notes-reminders-component">
    <div class="">
        <!-- Notes Section -->
        @if(($permissions['view_notes'] ?? true))
            <div class="col-md-12 NtBox">
                <div class="h50 shadowWidget blueBullet todo"
                     id="todo"
                     style="margin-top: 10px;"
                     data-fetch="{{ config('app.api_base_url') }}/client-portal/list-dashboard-todos">

                    <!-- Notes Header -->
                    <div class="headRem dashboard-todos"
                         data-fetch="{{ config('app.api_base_url') }}/client-portal/add-dashboard-todo"
                         data-remove-url="{{ config('app.api_base_url') }}/client-portal/remove-dashboard-todos"
                         data-edit-url="{{ config('app.api_base_url') }}/client-portal/get-dashboard-todos">

                        <span class="BudTitle noteTitle no-bdr">
                            <i class="fa fa-sticky-note-o" aria-hidden="true"></i>&nbsp;Notes
                        </span>

                        @if(($permissions['add_notes'] ?? true))
                            <a class='btn-three-sm addTodoBtn add-todos'>
                                <span class='p4' data-toggle='tooltip' title='Add Todo'>
                                    <i class='fa fa-plus'></i>
                                </span>
                            </a>
                        @endif

                        @if(($permissions['delete_notes'] ?? true))
                            <a class='btn addTodoBtnRemove remove-todosForm' style='display: none;'>
                                <span class='p4' data-toggle='tooltip' title='Remove'>
                                    <i class='fa fa-times'></i>
                                </span>
                            </a>
                        @endif
                    </div>
                    
                    <!-- Notes Form -->
                    <div class="headRem todos-form" style="margin-top: 21px;"></div>
                    
                    <!-- Notes List -->
                    <ul class="notes-list">
                        <!-- Loading state -->
                        <li class="loading-item text-center" style="padding: 20px;">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            Loading notes...
                        </li>
                    </ul>
                </div>
            </div>
        @endif

        <!-- Reminders Section -->
        @if(($permissions['view_reminders'] ?? true))
            <div class="col-md-12 NtBox">
                <div class="h50 shadowWidget yellowBullet reminders"
                     id="reminders"
                     data-fetch="{{ config('app.api_base_url') }}/client-portal/list-dashboard-reminders"
                     style="margin-top: 10px;">

                    <!-- Reminders Header -->
                    <div class="headRem dashboard-reminder"
                         data-fetch="{{ config('app.api_base_url') }}/client-portal/add-dashboard-reminder"
                         data-remove-url="{{ config('app.api_base_url') }}/client-portal/remove-dashboard-reminders"
                         data-edit-url="{{ config('app.api_base_url') }}/client-portal/get-dashboard-reminders">

                        <span class="noteTitle BudTitle no-bdr">
                            <i class="fa fa-bell-o" aria-hidden="true"></i>&nbsp;Reminders
                        </span>

                        @if(($permissions['add_reminders'] ?? true))
                            <a class='btn-three-sm addTodoBtn add-reminder' style=''>
                                <span class='p4' data-toggle='tooltip' title='Add Reminder'>
                                    <i class='fa fa-plus'></i>
                                </span>
                            </a>
                        @endif

                        @if(($permissions['delete_reminders'] ?? true))
                            <a class='btn addTodoBtnRemove remove-form' style='display: none;'>
                                <span class='p4' data-toggle='tooltip' title='Remove'>
                                    <i class='fa fa-times'></i>
                                </span>
                            </a>
                        @endif
                    </div>
                    
                    <!-- Reminders Form -->
                    <div class="headRem reminder-form" style="margin-top: 21px;"></div>
                    
                    <!-- Reminders List -->
                    <ul class="reminder_ul_form">
                        <!-- Loading state -->
                        <li class="loading-item text-center" style="padding: 20px;">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            Loading reminders...
                        </li>
                    </ul>
                </div>
            </div>
        @endif
    </div>

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-triangle"></i>
            <span class="error-message">Failed to load notes and reminders</span>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize notes and reminders component
    NotesReminders.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions)
    });
});
</script>
@endpush
