{{-- Expenses Donut Chart Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => [
        'view_chart' => true,
        'view_data_table' => true,
    ]
])

<div class="marginCenter GpBox nextMonthExpense shadowWidget client_name" 
     client="{{ $clientId }}" 
     id="expenses-donut-component">
    
    @if(($permissions['view_chart'] ?? true))
        <h4 class="redFont BudTitle">Expenses</h4>
        <figure class="highcharts-figure">
            <!-- Chart Container -->
            <div id="nextMonthExpensePie"
                 data-fetch="{{ config('app.api_base_url') }}/client-portal/get-dashboard-expense-donut-graph"
                 data-year="{{ date('Y') }}"
                 data-month="{{ date('m') }}"
                 data-client="{{ $clientId }}">
                <!-- Loading state -->
                <div class="chart-loading text-center" style="padding: 50px;">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p>Loading expenses chart...</p>
                </div>
            </div>

            <!-- Chart Inner Summary -->
            <div class="graphInnerSUm redFont"></div>

            @if(($permissions['view_data_table'] ?? true))
                <!-- Data Table -->
                <div class="chart-data-table">
                    <table cellpadding="0" cellspacing="0" border="0"
                           class="table table-striped table-bordered expenses-table"
                           id="expensesTable">
                        <thead>
                            <tr>
                                <th>Units</th>
                                <th style='text-align: end;'>Amount</th>
                                <th style='text-align: end;'>Percentage</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data populated via AJAX -->
                            <tr class="loading-row">
                                <td colspan="4" class="text-center">
                                    <div class="spinner-border spinner-border-sm" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    Loading data...
                                </td>
                            </tr>
                        </tbody>
                        <tfoot></tfoot>
                    </table>
                </div>
            @endif
        </figure>
    @endif

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-triangle"></i>
            <span class="error-message">Failed to load expenses chart</span>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize expenses donut chart component
    ExpensesDonutChart.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions),
        chartContainer: '#nextMonthExpensePie',
        tableContainer: '#expensesTable'
    });
});
</script>
@endpush
