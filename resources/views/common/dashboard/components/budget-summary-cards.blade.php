{{-- Budget Summary Cards Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => null
])

@php
    // Use DashboardPermissions helper if no permissions provided
    if (!$permissions) {
        $permissions = \App\Helpers\DashboardPermissions::getPermissions('budget_summary_cards');
    }
@endphp

<div class="firstBox marginCenter budgetBox row" 
     id="budget-summary-cards"
     data-fetch="{{ config('app.api_base_url') }}/client-portal/get-client-budget" 
     data-year="{{ date('Y') }}"
     data-month="{{ date('m') }}"
     data-client="{{ $clientId }}">
    
    <!-- Loading State -->
    <div class="loading-state" style="display: none;">
        <div class="col-md-12 text-center">
            <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p>Loading budget summary...</p>
        </div>
    </div>

    <!-- Cards Container (populated via AJAX) -->
    <div class="cards-container">
        <!-- Budget Card -->
        @if(($permissions['view_budget_card'] ?? true))
            <div class="BudgDashBox budget-card fade-in">
                <div class="ClitBdBg">
                    <div class="card-icon budget-icon">
                        <i class="fa fa-money" style="color: #28a745; font-size: 24px; opacity: 0.3;"></i>
                    </div>
                    <div class="amt greenFont">
                        <i class="fa fa-pound-sign" style="font-size: 0.7em; margin-right: 2px;"></i>
                        <span class="budget-amount" data-counter="0">0.00</span>
                    </div>
                    <div class="title">
                        <i class="fa fa-chart-line" style="margin-right: 5px; font-size: 12px;"></i>
                        <span>Total Budget</span>
                    </div>
                    <div class="subTitle">
                        <i class="fa fa-calendar" style="margin-right: 3px; font-size: 10px;"></i>
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                    <div class="progress-indicator">
                        <div class="progress-bar budget-progress" style="width: 0%; background: linear-gradient(90deg, #28a745, #20c997);"></div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Expenses Card -->
        @if(($permissions['view_expenses_card'] ?? true))
            <div class="BudgDashBox expenses-card fade-in" style="animation-delay: 0.1s;">
                <div class="ClitBdBg">
                    <div class="card-icon expense-icon">
                        <i class="fa fa-credit-card" style="color: #dc3545; font-size: 24px; opacity: 0.3;"></i>
                    </div>
                    <div class="amt redFont">
                        <i class="fa fa-pound-sign" style="font-size: 0.7em; margin-right: 2px;"></i>
                        <span class="expenses-amount" data-counter="0">0.00</span>
                    </div>
                    <div class="title">
                        <i class="fa fa-chart-area" style="margin-right: 5px; font-size: 12px;"></i>
                        <span>Total Expenses</span>
                    </div>
                    <div class="subTitle">
                        <i class="fa fa-calendar" style="margin-right: 3px; font-size: 10px;"></i>
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                    <div class="progress-indicator">
                        <div class="progress-bar expense-progress" style="width: 0%; background: linear-gradient(90deg, #dc3545, #fd7e14);"></div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Balance Card -->
        @if(($permissions['view_balance_card'] ?? true))
            <div class="BudgDashBox balance-card fade-in" style="animation-delay: 0.2s;">
                <div class="ClitBdBg">
                    <div class="card-icon balance-icon">
                        <i class="fa fa-balance-scale" style="color: #1d75bd; font-size: 24px; opacity: 0.3;"></i>
                    </div>
                    <div class="amt balance-amount-color">
                        <i class="fa fa-pound-sign" style="font-size: 0.7em; margin-right: 2px;"></i>
                        <span class="balance-amount" data-counter="0">0.00</span>
                    </div>
                    <div class="title">
                        <i class="fa fa-wallet" style="margin-right: 5px; font-size: 12px;"></i>
                        <span>Available Balance</span>
                    </div>
                    <div class="subTitle">
                        <i class="fa fa-calendar" style="margin-right: 3px; font-size: 10px;"></i>
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                    <div class="progress-indicator">
                        <div class="progress-bar balance-progress" style="width: 0%; background: linear-gradient(90deg, #1d75bd, #4ECDC4);"></div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Utilization Percentage Card -->
        @if(($permissions['view_expense_percentage_card'] ?? true))
            <div class="BudgDashBox expense-percentage-card fade-in" style="animation-delay: 0.3s;">
                <div class="ClitBdBg">
                    <div class="card-icon utilization-icon">
                        <i class="fa fa-percentage" style="color: #fd7e14; font-size: 24px; opacity: 0.3;"></i>
                    </div>
                    <div class="amt" style="color: #fd7e14;">
                        <span class="expense-percentage" data-counter="0">0.00</span>
                        <i class="fa fa-percent" style="font-size: 0.6em; margin-left: 2px;"></i>
                    </div>
                    <div class="title">
                        <i class="fa fa-tachometer-alt" style="margin-right: 5px; font-size: 12px;"></i>
                        <span>Utilization Rate</span>
                    </div>
                    <div class="subTitle">
                        <i class="fa fa-calendar" style="margin-right: 3px; font-size: 10px;"></i>
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                    <div class="progress-indicator">
                        <div class="progress-bar utilization-progress" style="width: 0%; background: linear-gradient(90deg, #fd7e14, #ffc107);"></div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Efficiency Score Card -->
        @if(($permissions['view_balance_percentage_card'] ?? true))
            <div class="BudgDashBox mr-0 balance-percentage-card fade-in" style="animation-delay: 0.4s;">
                <div class="ClitBdBg">
                    <div class="card-icon efficiency-icon">
                        <i class="fa fa-trophy" style="color: #28a745; font-size: 24px; opacity: 0.3;"></i>
                    </div>
                    <div class="amt greenFont">
                        <span class="balance-percentage" data-counter="0">0.00</span>
                        <i class="fa fa-percent" style="font-size: 0.6em; margin-left: 2px;"></i>
                    </div>
                    <div class="title">
                        <i class="fa fa-star" style="margin-right: 5px; font-size: 12px;"></i>
                        <span>Efficiency Score</span>
                    </div>
                    <div class="subTitle">
                        <i class="fa fa-calendar" style="margin-right: 3px; font-size: 10px;"></i>
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                    <div class="progress-indicator">
                        <div class="progress-bar efficiency-progress" style="width: 0%; background: linear-gradient(90deg, #28a745, #20c997);"></div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="col-md-12 text-center">
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-triangle"></i>
                <span class="error-message">Failed to load budget summary</span>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize budget summary cards component
    BudgetSummaryCards.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions)
    });
});
</script>
@endpush
