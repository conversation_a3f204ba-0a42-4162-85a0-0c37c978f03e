{{-- Budget Summary Cards Component --}}
@props([
    'clientId' => auth()->user()->portal_id ?? null,
    'permissions' => null
])

@php
    // Use DashboardPermissions helper if no permissions provided
    if (!$permissions) {
        $permissions = \App\Helpers\DashboardPermissions::getPermissions('budget_summary_cards');
    }
@endphp

<div class="firstBox marginCenter budgetBox row" 
     id="budget-summary-cards"
     data-fetch="{{ config('app.api_base_url') }}/client-portal/get-client-budget" 
     data-year="{{ date('Y') }}"
     data-month="{{ date('m') }}"
     data-client="{{ $clientId }}">
    
    <!-- Loading State -->
    <div class="loading-state" style="display: none;">
        <div class="col-md-12 text-center">
            <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p>Loading budget summary...</p>
        </div>
    </div>

    <!-- Cards Container (populated via AJAX) -->
    <div class="cards-container">
        <!-- Budget Card -->
        @if($permissions['view_budget_card'])
            <div class="BudgDashBox budget-card">
                <div class="ClitBdBg">
                    <div class="amt greenFont">
                        <span class="budget-amount">0.00</span>
                    </div>
                    <div class="title">
                        <span>Budget</span>
                    </div>
                    <div class="subTitle">
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                </div>
            </div>
        @endif

        <!-- Expenses Card -->
        @if($permissions['view_expenses_card'])
            <div class="BudgDashBox expenses-card">
                <div class="ClitBdBg">
                    <div class="amt redFont">
                        <span class="expenses-amount">0.00</span>
                    </div>
                    <div class="title">
                        <span>Expenses</span>
                    </div>
                    <div class="subTitle">
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                </div>
            </div>
        @endif

        <!-- Balance Card -->
        @if($permissions['view_balance_card'])
            <div class="BudgDashBox balance-card">
                <div class="ClitBdBg">
                    <div class="amt balance-amount-color">
                        <span class="balance-amount">0.00</span>
                    </div>
                    <div class="title">
                        <span>Balance</span>
                    </div>
                    <div class="subTitle">
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                </div>
            </div>
        @endif

        <!-- Expense Percentage Card -->
        @if($permissions['view_expense_percentage_card'])
            <div class="BudgDashBox expense-percentage-card">
                <div class="ClitBdBg">
                    <div class="amt redFont">
                        <span class="expense-percentage">0.00%</span>
                    </div>
                    <div class="title">
                        <span>Expenses</span>
                    </div>
                    <div class="subTitle">
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                </div>
            </div>
        @endif

        <!-- Balance Percentage Card -->
        @if($permissions['view_balance_percentage_card'])
            <div class="BudgDashBox mr-0 balance-percentage-card">
                <div class="ClitBdBg">
                    <div class="amt greenFont">
                        <span class="balance-percentage">0.00%</span>
                    </div>
                    <div class="title">
                        <span>Balance</span>
                    </div>
                    <div class="subTitle">
                        <span class="upperCase filter-text">This Month</span>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Error State -->
    <div class="error-state" style="display: none;">
        <div class="col-md-12 text-center">
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-triangle"></i>
                <span class="error-message">Failed to load budget summary</span>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize budget summary cards component
    BudgetSummaryCards.init({
        clientId: '{{ $clientId }}',
        permissions: @json($permissions)
    });
});
</script>
@endpush
