# Modular Dashboard Components System

This document describes the modular, component-based dashboard system that allows for reusable and maintainable dashboard components.

## Overview

The dashboard system is built with the following principles:
- **Modular**: Each UI element is a separate, reusable component
- **Permission-based**: Components can be shown/hidden based on permissions
- **Filter-enabled**: Components respond to filter changes dynamically
- **Maintainable**: Clean separation of concerns between presentation, logic, and styling

## Directory Structure

```
resources/views/common/dashboard/
├── main-wrapper.blade.php          # Main dashboard wrapper
├── components/                     # Individual components
│   ├── filter.blade.php           # Filter component
│   ├── budget-summary-cards.blade.php
│   ├── budget-donut-chart.blade.php
│   ├── expenses-donut-chart.blade.php
│   ├── notes-reminders.blade.php
│   ├── smart-view-chart.blade.php
│   ├── booking-reasons.blade.php
│   └── unit-expense-overview.blade.php
└── README.md                       # This file

public/client-portal/js/dashboard-components/
├── main.js                         # Main controller
├── filter.js                       # Filter functionality
├── budget-summary-cards.js         # Budget cards logic
├── budget-donut-chart.js          # Budget chart logic
├── expenses-donut-chart.js        # Expenses chart logic
└── remaining-components.js         # Other components

public/client-portal/css/
└── dashboard-components.css        # Component styles
```

## Usage

### Basic Usage

```php
@include('common.dashboard.main-wrapper', [
    'clientId' => auth()->user()->portal_id,
    'permissions' => [
        'view_budget_summary' => true,
        'view_budget_charts' => true,
        'view_notes_reminders' => true,
        'view_smart_view' => true,
        'view_booking_reasons' => true,
        'view_expense_overview' => true,
    ]
])
```

### Individual Component Usage

```php
@include('common.dashboard.components.budget-summary-cards', [
    'clientId' => $clientId,
    'permissions' => [
        'view_budget_card' => true,
        'view_expenses_card' => true,
        'view_balance_card' => true,
        'view_expense_percentage_card' => true,
        'view_balance_percentage_card' => true,
    ]
])
```

### Filter Component with Options

```php
@include('common.dashboard.components.filter', [
    'selectedFilter' => request()->get('filter', 1),
    'showUnitFilter' => true,
    'showFinancialYearFilter' => true,
    'showDateRangeFilter' => true,
    'clientId' => $clientId
])
```

## Components

### 1. Main Wrapper (`main-wrapper.blade.php`)
The main container that includes all dashboard components.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: Array of permissions for component visibility

### 2. Filter Component (`components/filter.blade.php`)
Handles filtering functionality for all dashboard components.

**Props:**
- `selectedFilter`: Currently selected filter value
- `showUnitFilter`: Show unit selection dropdown
- `showFinancialYearFilter`: Show financial year selector
- `showDateRangeFilter`: Show date range picker
- `clientId`: Client ID for data filtering

### 3. Budget Summary Cards (`components/budget-summary-cards.blade.php`)
Displays budget, expenses, balance, and percentage cards.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: Array of card-specific permissions

### 4. Budget Donut Chart (`components/budget-donut-chart.blade.php`)
Shows budget breakdown by units in a donut chart with data table.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: Chart and table visibility permissions

### 5. Expenses Donut Chart (`components/expenses-donut-chart.blade.php`)
Shows expense breakdown by units in a donut chart with data table.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: Chart and table visibility permissions

### 6. Notes and Reminders (`components/notes-reminders.blade.php`)
Handles notes and reminders functionality with CRUD operations.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: CRUD operation permissions for notes and reminders

### 7. Smart View Chart (`components/smart-view-chart.blade.php`)
Budget vs expenses bar chart with month filter.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: Chart and filter permissions

### 8. Booking Reasons (`components/booking-reasons.blade.php`)
Shows booking count and cost by reason categories.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: View permissions

### 9. Unit Expense Overview (`components/unit-expense-overview.blade.php`)
Line chart showing expense trends over time.

**Props:**
- `clientId`: Client ID for data filtering
- `permissions`: View permissions

## JavaScript Architecture

### Main Controller (`main.js`)
- Coordinates all dashboard components
- Handles global events and filter changes
- Provides utility functions for AJAX requests
- Manages component initialization

### Component-Specific Files
Each component has its own JavaScript file with:
- Initialization logic
- Data loading and rendering
- Event handling
- Refresh functionality
- Error handling

## Permissions System

All components support permission-based rendering:

```php
'permissions' => [
    'view_component' => true,    // Show/hide entire component
    'view_chart' => true,        // Show/hide chart
    'view_data_table' => true,   // Show/hide data table
    'add_items' => true,         // Allow adding items
    'edit_items' => true,        // Allow editing items
    'delete_items' => true,      // Allow deleting items
]
```

## Filter System

The filter system supports:
- **Month Filter**: This Month, Last Month, Next Month
- **Unit Filter**: Dropdown of client units
- **Financial Year Filter**: Financial year selector
- **Date Range Filter**: Start and end date pickers

Filters automatically update all components when changed.

## Data Sources

Components primarily use data from:
- `client_unit_weekly_budgets` table
- `client_unit_annual_budgets` table
- Related booking and expense data

## API Endpoints

Components expect these API endpoints:
- `/client-portal/get-client-budget` - Budget summary data
- `/client-portal/get-dashboard-donut-graph` - Budget chart data
- `/client-portal/get-dashboard-expense-donut-graph` - Expenses chart data
- `/client-portal/get-dashboard-bar-budget-usage` - Smart view data
- `/client-portal/booking-counts-costs` - Booking reasons data
- `/client-portal/get-dashboard-line-charts` - Expense overview data
- `/client-portal/list-dashboard-todos` - Notes data
- `/client-portal/list-dashboard-reminders` - Reminders data

## Styling

Components use:
- Base styles in `dashboard-components.css`
- Responsive design principles
- Consistent color scheme and spacing
- Loading and error states
- Hover effects and animations

## Best Practices

1. **Always pass clientId** to components for proper data filtering
2. **Use permissions** to control component visibility
3. **Include required JavaScript files** in the correct order
4. **Handle loading and error states** gracefully
5. **Test filter functionality** across all components
6. **Maintain consistent styling** across components

## Example Implementation

See `resources/views/clientPortal/dashboard-modular.blade.php` for a complete implementation example.
