# Dashboard Components Integration Guide

This guide explains how to integrate and test the modular dashboard components system.

## Quick Start

### 1. Include Required Files

In your Blade template, include the necessary CSS and JavaScript files:

```php
@push('styles')
    <link rel="stylesheet" href="{{ asset('client-portal/css/dashboard-components.css') }}">
@endpush

@section('scripts')
    {{-- Include Highcharts for charts --}}
    <script src="{{ asset('unit-portal/js/highcharts.js') }}"></script>
    <script src="{{ asset('unit-portal/js/variable-pie.js') }}"></script>
    
    {{-- Include modular dashboard components --}}
    <script src="{{ asset('client-portal/js/dashboard-components/main.js') }}"></script>
    <script src="{{ asset('client-portal/js/dashboard-components/filter.js') }}"></script>
    <script src="{{ asset('client-portal/js/dashboard-components/budget-summary-cards.js') }}"></script>
    <script src="{{ asset('client-portal/js/dashboard-components/budget-donut-chart.js') }}"></script>
    <script src="{{ asset('client-portal/js/dashboard-components/expenses-donut-chart.js') }}"></script>
    <script src="{{ asset('client-portal/js/dashboard-components/remaining-components.js') }}"></script>
@endsection
```

### 2. Use the Main Wrapper

```php
@include('common.dashboard.main-wrapper', [
    'clientId' => auth()->user()->portal_id
])
```

### 3. Access the Dashboard

Visit `/dashboard-modular` to see the modular dashboard in action.

## Component Architecture

### Main Components

1. **Main Wrapper** (`main-wrapper.blade.php`)
   - Coordinates all dashboard components
   - Handles permissions and client ID passing
   - Provides overall layout structure

2. **Filter Component** (`components/filter.blade.php`)
   - Month, unit, financial year, and date range filters
   - Dynamically updates all components
   - Configurable filter options

3. **Budget Summary Cards** (`components/budget-summary-cards.blade.php`)
   - Budget, expenses, balance, and percentage cards
   - Real-time data updates
   - Permission-based visibility

4. **Chart Components**
   - Budget donut chart with data table
   - Expenses donut chart with data table
   - Smart view bar chart
   - Unit expense overview line chart

5. **Interactive Components**
   - Notes and reminders with CRUD operations
   - Booking reasons summary

## JavaScript Architecture

### Main Controller (`main.js`)
- Initializes all components
- Handles global events and filter changes
- Provides utility functions for AJAX requests
- Manages component lifecycle

### Component Files
Each component has its own JavaScript file with:
- Initialization logic
- Data loading and rendering
- Event handling
- Refresh functionality
- Error handling

## Permission System

### Using DashboardPermissions Helper

```php
use App\Helpers\DashboardPermissions;

// Get all permissions
$permissions = DashboardPermissions::getPermissions();

// Get component-specific permissions
$budgetPermissions = DashboardPermissions::getPermissions('budget_summary_cards');

// Check specific permission
$canView = DashboardPermissions::hasPermission('view_dashboard');

// Get main dashboard permissions
$dashboardPermissions = DashboardPermissions::getDashboardPermissions();
```

### Permission Structure

All permissions are currently set to `true` as requested. The system is designed to be easily extended for role-based permissions in the future.

## Filter System

### Available Filters

1. **Month Filter** (Always available)
   - This Month, Last Month, Next Month
   - Updates all components via page reload

2. **Unit Filter** (Optional)
   - Dropdown of client units
   - Updates components via AJAX

3. **Financial Year Filter** (Optional)
   - Financial year selector
   - Updates components via AJAX

4. **Date Range Filter** (Optional)
   - Start and end date pickers
   - Updates components via AJAX

### Enabling Optional Filters

```php
@include('common.dashboard.components.filter', [
    'selectedFilter' => request()->get('filter', 1),
    'showUnitFilter' => true,
    'showFinancialYearFilter' => true,
    'showDateRangeFilter' => true,
    'clientId' => $clientId
])
```

## Data Sources

Components expect data from these API endpoints:

- `/client-portal/get-client-budget` - Budget summary
- `/client-portal/get-dashboard-donut-graph` - Budget chart
- `/client-portal/get-dashboard-expense-donut-graph` - Expenses chart
- `/client-portal/get-dashboard-bar-budget-usage` - Smart view
- `/client-portal/booking-counts-costs` - Booking reasons
- `/client-portal/get-dashboard-line-charts` - Expense overview
- `/client-portal/list-dashboard-todos` - Notes
- `/client-portal/list-dashboard-reminders` - Reminders

## Testing

### Running Tests

```bash
php artisan test tests/Feature/DashboardComponentsTest.php
```

### Test Coverage

The test suite covers:
- Permission system functionality
- Component rendering
- Required file inclusion
- Route accessibility
- Component integration

### Manual Testing Checklist

1. **Component Rendering**
   - [ ] All components render without errors
   - [ ] Loading states display correctly
   - [ ] Error states handle failures gracefully

2. **Filter Functionality**
   - [ ] Month filter updates all components
   - [ ] Optional filters work when enabled
   - [ ] Filter changes reflect in all components

3. **Permission System**
   - [ ] Components respect permission settings
   - [ ] Hidden components don't render
   - [ ] Permission helper works correctly

4. **Responsive Design**
   - [ ] Components work on mobile devices
   - [ ] Charts resize appropriately
   - [ ] Filter layout adapts to screen size

5. **JavaScript Functionality**
   - [ ] All components initialize correctly
   - [ ] AJAX requests work properly
   - [ ] Error handling functions correctly

## Troubleshooting

### Common Issues

1. **Components Not Rendering**
   - Check if required JavaScript files are included
   - Verify client ID is being passed correctly
   - Check browser console for JavaScript errors

2. **Filters Not Working**
   - Ensure filter JavaScript is loaded
   - Check API endpoints are accessible
   - Verify AJAX requests are successful

3. **Charts Not Displaying**
   - Confirm Highcharts library is loaded
   - Check chart container elements exist
   - Verify chart data format is correct

4. **Permission Issues**
   - Check DashboardPermissions helper is working
   - Verify permission arrays are properly formatted
   - Ensure user authentication is working

### Debug Mode

Enable debug mode by adding to your JavaScript:

```javascript
DashboardComponents.config.debug = true;
```

This will log component initialization and AJAX requests to the browser console.

## Future Enhancements

The system is designed to be easily extended with:

1. **Role-based Permissions**
   - Database-driven permission management
   - User role integration
   - Dynamic permission updates

2. **Additional Filters**
   - Custom date ranges
   - Multiple unit selection
   - Category-based filtering

3. **New Components**
   - Additional chart types
   - Custom widgets
   - Third-party integrations

4. **Performance Optimizations**
   - Component lazy loading
   - Data caching
   - Optimized AJAX requests

## Support

For issues or questions about the modular dashboard system:

1. Check the component README files
2. Review the test suite for examples
3. Examine the existing dashboard implementation
4. Check browser console for JavaScript errors
