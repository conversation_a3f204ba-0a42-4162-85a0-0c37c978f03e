<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\ClientUnit;
use App\Models\Client;
use App\Helpers\DashboardPermissions;

class DashboardPermissionsFixTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $client;
    protected $clientUnit;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->client = Client::factory()->create();
        $this->clientUnit = ClientUnit::factory()->create([
            'clientId' => $this->client->clientId
        ]);
        $this->user = User::factory()->create([
            'portal_id' => $this->client->clientId,
            'portal_type' => Client::class
        ]);
    }

    /** @test */
    public function dashboard_components_render_without_undefined_array_key_errors()
    {
        // Test with empty permissions array
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.advanced.dashboard'));

        $response->assertStatus(200);
        
        // Should not contain any PHP errors in the response
        $content = $response->getContent();
        $this->assertStringNotContainsString('Undefined array key', $content);
        $this->assertStringNotContainsString('Undefined index', $content);
        $this->assertStringNotContainsString('ErrorException', $content);
    }

    /** @test */
    public function budget_summary_cards_component_handles_missing_permissions()
    {
        // Test rendering budget summary cards with empty permissions
        $view = view('common.dashboard.components.budget-summary-cards', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show all cards (default to true)
        $this->assertStringContainsString('budget-card', $html);
        $this->assertStringContainsString('expenses-card', $html);
        $this->assertStringContainsString('balance-card', $html);
        $this->assertStringContainsString('expense-percentage-card', $html);
        $this->assertStringContainsString('balance-percentage-card', $html);
    }

    /** @test */
    public function budget_donut_chart_component_handles_missing_permissions()
    {
        // Test rendering budget donut chart with empty permissions
        $view = view('common.dashboard.components.budget-donut-chart', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show chart and table (default to true)
        $this->assertStringContainsString('thisMonthBudgetPie', $html);
        $this->assertStringContainsString('budgetTable', $html);
        $this->assertStringContainsString('Budget', $html);
    }

    /** @test */
    public function expenses_donut_chart_component_handles_missing_permissions()
    {
        // Test rendering expenses donut chart with empty permissions
        $view = view('common.dashboard.components.expenses-donut-chart', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show chart and table (default to true)
        $this->assertStringContainsString('nextMonthExpensePie', $html);
        $this->assertStringContainsString('expensesTable', $html);
        $this->assertStringContainsString('Expenses', $html);
    }

    /** @test */
    public function notes_reminders_component_handles_missing_permissions()
    {
        // Test rendering notes and reminders with empty permissions
        $view = view('common.dashboard.components.notes-reminders', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show both sections (default to true)
        $this->assertStringContainsString('Notes', $html);
        $this->assertStringContainsString('Reminders', $html);
        $this->assertStringContainsString('fa-sticky-note-o', $html);
        $this->assertStringContainsString('fa-bell-o', $html);
    }

    /** @test */
    public function smart_view_chart_component_handles_missing_permissions()
    {
        // Test rendering smart view chart with empty permissions
        $view = view('common.dashboard.components.smart-view-chart', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show chart and filter (default to true)
        $this->assertStringContainsString('Smart View', $html);
        $this->assertStringContainsString('usageThisMonth', $html);
        $this->assertStringContainsString('smartViewMonthselect', $html);
    }

    /** @test */
    public function booking_reasons_component_handles_missing_permissions()
    {
        // Test rendering booking reasons with empty permissions
        $view = view('common.dashboard.components.booking-reasons', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show booking reasons (default to true)
        $this->assertStringContainsString('Reason for Booking', $html);
        $this->assertStringContainsString('Sickness', $html);
        $this->assertStringContainsString('Holiday', $html);
        $this->assertStringContainsString('Vacant', $html);
    }

    /** @test */
    public function unit_expense_overview_component_handles_missing_permissions()
    {
        // Test rendering unit expense overview with empty permissions
        $view = view('common.dashboard.components.unit-expense-overview', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show chart (default to true)
        $this->assertStringContainsString('Unit Expense Overview', $html);
        $this->assertStringContainsString('containerLine', $html);
    }

    /** @test */
    public function main_wrapper_component_handles_missing_permissions()
    {
        // Test rendering main wrapper with empty permissions
        $view = view('common.dashboard.main-wrapper', [
            'clientId' => $this->client->clientId,
            'permissions' => [] // Empty permissions array
        ]);

        $html = $view->render();
        
        // Should render without errors and show all components (default to true)
        $this->assertStringContainsString('Dashboard', $html);
        $this->assertStringContainsString('budget-summary-cards', $html);
        $this->assertStringContainsString('budget-donut-component', $html);
        $this->assertStringContainsString('expenses-donut-component', $html);
    }

    /** @test */
    public function dashboard_permissions_helper_returns_complete_arrays()
    {
        // Test that helper always returns complete permission arrays
        $permissions = DashboardPermissions::getPermissions('budget_summary_cards');
        
        $this->assertIsArray($permissions);
        $this->assertArrayHasKey('view_budget_card', $permissions);
        $this->assertArrayHasKey('view_expenses_card', $permissions);
        $this->assertArrayHasKey('view_balance_card', $permissions);
        $this->assertArrayHasKey('view_expense_percentage_card', $permissions);
        $this->assertArrayHasKey('view_balance_percentage_card', $permissions);
        
        // All should default to true
        $this->assertTrue($permissions['view_budget_card']);
        $this->assertTrue($permissions['view_expenses_card']);
        $this->assertTrue($permissions['view_balance_card']);
        $this->assertTrue($permissions['view_expense_percentage_card']);
        $this->assertTrue($permissions['view_balance_percentage_card']);
    }

    /** @test */
    public function dashboard_permissions_helper_handles_null_permissions()
    {
        // Test that helper handles null permissions gracefully
        $permissions = DashboardPermissions::getDashboardPermissions();
        
        $this->assertIsArray($permissions);
        $this->assertArrayHasKey('view_budget_summary', $permissions);
        $this->assertArrayHasKey('view_budget_charts', $permissions);
        $this->assertArrayHasKey('view_notes_reminders', $permissions);
        $this->assertArrayHasKey('view_smart_view', $permissions);
        $this->assertArrayHasKey('view_booking_reasons', $permissions);
        $this->assertArrayHasKey('view_expense_overview', $permissions);
        
        // All should default to true
        foreach ($permissions as $permission) {
            $this->assertTrue($permission);
        }
    }

    /** @test */
    public function components_use_null_coalescing_operator_correctly()
    {
        // Test that components handle undefined array keys with null coalescing
        $permissions = ['view_budget_card' => false]; // Only one permission set
        
        $view = view('common.dashboard.components.budget-summary-cards', [
            'clientId' => $this->client->clientId,
            'permissions' => $permissions
        ]);

        $html = $view->render();
        
        // Should render without undefined array key errors
        // Budget card should be hidden (false), others should show (default true)
        $this->assertStringNotContainsString('Undefined array key', $html);
        $this->assertStringContainsString('expenses-card', $html); // Should show (defaults to true)
        $this->assertStringContainsString('balance-card', $html); // Should show (defaults to true)
    }
}
