<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\ClientUnit;
use App\Helpers\DashboardPermissions;

class DashboardComponentsTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $clientUnit;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and client unit
        $this->clientUnit = ClientUnit::factory()->create();
        $this->user = User::factory()->create([
            'portal_id' => $this->clientUnit->clientUnitId
        ]);
    }

    /** @test */
    public function dashboard_permissions_helper_returns_default_permissions()
    {
        $permissions = DashboardPermissions::getPermissions();
        
        $this->assertIsArray($permissions);
        $this->assertTrue($permissions['view_dashboard']);
        $this->assertTrue($permissions['view_budget_summary']);
        $this->assertTrue($permissions['view_budget_charts']);
        $this->assertTrue($permissions['view_notes_reminders']);
    }

    /** @test */
    public function dashboard_permissions_helper_returns_component_specific_permissions()
    {
        $permissions = DashboardPermissions::getPermissions('budget_summary_cards');
        
        $this->assertIsArray($permissions);
        $this->assertArrayHasKey('view_budget_card', $permissions);
        $this->assertArrayHasKey('view_expenses_card', $permissions);
        $this->assertArrayHasKey('view_balance_card', $permissions);
        $this->assertTrue($permissions['view_budget_card']);
    }

    /** @test */
    public function user_can_access_modular_dashboard()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        $response->assertViewIs('clientPortal.dashboard-modular');
    }

    /** @test */
    public function dashboard_main_wrapper_renders_correctly()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        $response->assertSee('Dashboard');
        $response->assertSee('clientvalue');
        $response->assertSee('budget-summary-cards');
    }

    /** @test */
    public function dashboard_components_are_included()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for component containers
        $response->assertSee('budget-summary-cards');
        $response->assertSee('budget-donut-component');
        $response->assertSee('expenses-donut-component');
        $response->assertSee('notes-reminders-component');
        $response->assertSee('smart-view-component');
        $response->assertSee('booking-reasons-component');
        $response->assertSee('unit-expense-overview-component');
    }

    /** @test */
    public function dashboard_includes_required_javascript_files()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for JavaScript includes
        $response->assertSee('dashboard-components/main.js');
        $response->assertSee('dashboard-components/filter.js');
        $response->assertSee('dashboard-components/budget-summary-cards.js');
        $response->assertSee('dashboard-components/budget-donut-chart.js');
    }

    /** @test */
    public function dashboard_includes_required_css_files()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for CSS includes
        $response->assertSee('dashboard-components.css');
    }

    /** @test */
    public function filter_component_renders_with_default_options()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for filter elements
        $response->assertSee('monthselect');
        $response->assertSee('This Month');
        $response->assertSee('Last Month');
        $response->assertSee('Next Month');
    }

    /** @test */
    public function budget_summary_cards_component_renders()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for budget card elements
        $response->assertSee('budget-amount');
        $response->assertSee('expenses-amount');
        $response->assertSee('balance-amount');
        $response->assertSee('Budget');
        $response->assertSee('Expenses');
        $response->assertSee('Balance');
    }

    /** @test */
    public function chart_components_render_with_containers()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for chart containers
        $response->assertSee('thisMonthBudgetPie');
        $response->assertSee('nextMonthExpensePie');
        $response->assertSee('usageThisMonth');
        $response->assertSee('containerLine');
    }

    /** @test */
    public function notes_and_reminders_component_renders()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for notes and reminders elements
        $response->assertSee('Notes');
        $response->assertSee('Reminders');
        $response->assertSee('fa-sticky-note-o');
        $response->assertSee('fa-bell-o');
    }

    /** @test */
    public function booking_reasons_component_renders()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.dashboard.modular'));

        $response->assertStatus(200);
        
        // Check for booking reasons elements
        $response->assertSee('Reason for Booking');
        $response->assertSee('Sickness');
        $response->assertSee('Holiday');
        $response->assertSee('Vacant');
        $response->assertSee('Count');
        $response->assertSee('Cost');
    }

    /** @test */
    public function has_permission_method_works_correctly()
    {
        $this->assertTrue(DashboardPermissions::hasPermission('view_dashboard'));
        $this->assertTrue(DashboardPermissions::hasPermission('view_budget_summary'));
        $this->assertFalse(DashboardPermissions::hasPermission('non_existent_permission'));
    }

    /** @test */
    public function get_dashboard_permissions_returns_main_permissions()
    {
        $permissions = DashboardPermissions::getDashboardPermissions();
        
        $this->assertIsArray($permissions);
        $this->assertArrayHasKey('view_budget_summary', $permissions);
        $this->assertArrayHasKey('view_budget_charts', $permissions);
        $this->assertArrayHasKey('view_notes_reminders', $permissions);
        $this->assertArrayHasKey('view_smart_view', $permissions);
        $this->assertArrayHasKey('view_booking_reasons', $permissions);
        $this->assertArrayHasKey('view_expense_overview', $permissions);
    }
}
