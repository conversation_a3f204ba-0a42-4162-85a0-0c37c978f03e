<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\ClientUnit;
use App\Models\Client;
use App\Models\ClientUnitAnnualBudget;
use App\Models\ClientUnitWeeklyBudget;

class AdvancedDashboardEnhancementsTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $client;
    protected $clientUnit;
    protected $annualBudget;
    protected $weeklyBudget;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->client = Client::factory()->create();
        $this->clientUnit = ClientUnit::factory()->create([
            'clientId' => $this->client->clientId,
            'status' => 1
        ]);
        
        $this->annualBudget = ClientUnitAnnualBudget::factory()->create([
            'client_unit_id' => $this->clientUnit->clientUnitId,
            'financial_year' => '2024-2025',
            'annual_budget' => 50000.00
        ]);
        
        $this->weeklyBudget = ClientUnitWeeklyBudget::factory()->create([
            'client_unit_annual_budget_id' => $this->annualBudget->id,
            'week_start_date' => now()->startOfWeek(),
            'week_end_date' => now()->endOfWeek(),
            'total_weekly_allocation' => 1000.00,
            'total_weekly_utilisation' => 750.00,
            'balance_fund' => 250.00
        ]);
        
        $this->user = User::factory()->create([
            'portal_id' => $this->client->clientId,
            'portal_type' => Client::class
        ]);
    }

    /** @test */
    public function advanced_dashboard_loads_with_enhanced_design()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.advanced.dashboard'));

        $response->assertStatus(200);
        
        // Check for enhanced CSS
        $response->assertSee('advanced-dashboard.css');
        
        // Check for filter elements
        $response->assertSee('monthselect');
        $response->assertSee('unitselect');
        $response->assertSee('financialyearselect');
        
        // Check for enhanced title with icon
        $response->assertSee('fa-tachometer');
        $response->assertSee('Advanced Dashboard');
    }

    /** @test */
    public function budget_summary_returns_data_from_weekly_budgets()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/budget-summary', [
                             'client' => $this->client->clientId,
                             'filter' => 1,
                             'financial_year' => '2024-2025'
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'budget',
            'expense',
            'balance',
            'utilisation_percent',
            'units',
            'client',
            'filter_info' => [
                'month',
                'year',
                'financial_year',
                'filter_name'
            ]
        ]);
        
        // Verify data comes from weekly budgets
        $data = $response->json();
        $this->assertEquals(1000.00, $data['budget']);
        $this->assertEquals(750.00, $data['expense']);
        $this->assertEquals(250.00, $data['balance']);
        $this->assertEquals(75.0, $data['utilisation_percent']);
    }

    /** @test */
    public function budget_donut_chart_returns_enhanced_data()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/budget-donut', [
                             'client' => $this->client->clientId,
                             'filter' => 1,
                             'financial_year' => '2024-2025'
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'graph' => [
                'budgetGraph' => [
                    'data',
                    'title',
                    'table' => [
                        'rows',
                        'tfoot'
                    ]
                ]
            ]
        ]);
        
        $data = $response->json();
        
        // Check for enhanced data structure
        $this->assertArrayHasKey('color', $data['graph']['budgetGraph']['data'][0]);
        $this->assertStringContains('2024', $data['graph']['budgetGraph']['title']);
    }

    /** @test */
    public function filters_work_correctly_with_different_parameters()
    {
        // Test different filter values
        $filters = [1, 2, 4]; // This Month, Last Month, Next Month
        
        foreach ($filters as $filter) {
            $response = $this->actingAs($this->user)
                             ->postJson('/api/client-portal/advanced-dashboard/budget-summary', [
                                 'client' => $this->client->clientId,
                                 'filter' => $filter,
                                 'financial_year' => '2024-2025'
                             ]);

            $response->assertStatus(200);
            
            $data = $response->json();
            $this->assertArrayHasKey('filter_info', $data);
            $this->assertArrayHasKey('filter_name', $data['filter_info']);
        }
    }

    /** @test */
    public function unit_filter_works_correctly()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/budget-summary', [
                             'client' => $this->client->clientId,
                             'filter' => 1,
                             'unit_id' => $this->clientUnit->clientUnitId,
                             'financial_year' => '2024-2025'
                         ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertCount(1, $data['units']);
        $this->assertEquals($this->clientUnit->name, $data['units'][0]['name']);
    }

    /** @test */
    public function financial_year_filter_works_correctly()
    {
        // Create budget for different financial year
        $differentYearBudget = ClientUnitAnnualBudget::factory()->create([
            'client_unit_id' => $this->clientUnit->clientUnitId,
            'financial_year' => '2023-2024',
            'annual_budget' => 30000.00
        ]);
        
        ClientUnitWeeklyBudget::factory()->create([
            'client_unit_annual_budget_id' => $differentYearBudget->id,
            'week_start_date' => now()->startOfWeek(),
            'week_end_date' => now()->endOfWeek(),
            'total_weekly_allocation' => 500.00,
            'total_weekly_utilisation' => 300.00,
            'balance_fund' => 200.00
        ]);

        // Test with different financial year
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/budget-summary', [
                             'client' => $this->client->clientId,
                             'filter' => 1,
                             'financial_year' => '2023-2024'
                         ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(500.00, $data['budget']);
        $this->assertEquals(300.00, $data['expense']);
    }

    /** @test */
    public function enhanced_cards_include_animation_classes()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.advanced.dashboard'));

        $response->assertStatus(200);
        
        // Check for animation classes
        $response->assertSee('fade-in');
        $response->assertSee('data-counter');
        $response->assertSee('progress-indicator');
        $response->assertSee('card-icon');
    }

    /** @test */
    public function dashboard_includes_enhanced_icons_and_styling()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.advanced.dashboard'));

        $response->assertStatus(200);
        
        // Check for enhanced icons
        $response->assertSee('fa-money');
        $response->assertSee('fa-credit-card');
        $response->assertSee('fa-balance-scale');
        $response->assertSee('fa-percentage');
        $response->assertSee('fa-trophy');
        
        // Check for enhanced titles
        $response->assertSee('Total Budget');
        $response->assertSee('Total Expenses');
        $response->assertSee('Available Balance');
        $response->assertSee('Utilization Rate');
        $response->assertSee('Efficiency Score');
    }

    /** @test */
    public function dashboard_includes_filter_javascript()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.advanced.dashboard'));

        $response->assertStatus(200);
        
        // Check for filter JavaScript
        $response->assertSee('advanced-dashboard-filters.js');
        $response->assertSee('AdvancedDashboardFilters');
    }

    /** @test */
    public function api_endpoints_return_properly_formatted_data()
    {
        $endpoints = [
            'budget-summary',
            'budget-donut',
            'expense-donut',
            'bar-chart',
            'line-chart',
            'booking-reasons',
            'client-units'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->actingAs($this->user)
                             ->postJson("/api/client-portal/advanced-dashboard/{$endpoint}", [
                                 'client' => $this->client->clientId,
                                 'filter' => 1,
                                 'financial_year' => '2024-2025'
                             ]);

            $response->assertStatus(200);
            $response->assertJsonStructure(['status']);
        }
    }

    /** @test */
    public function dashboard_handles_empty_data_gracefully()
    {
        // Create client with no budget data
        $emptyClient = Client::factory()->create();
        $emptyUnit = ClientUnit::factory()->create([
            'clientId' => $emptyClient->clientId,
            'status' => 1
        ]);
        
        $emptyUser = User::factory()->create([
            'portal_id' => $emptyClient->clientId,
            'portal_type' => Client::class
        ]);

        $response = $this->actingAs($emptyUser)
                         ->postJson('/api/client-portal/advanced-dashboard/budget-summary', [
                             'client' => $emptyClient->clientId,
                             'filter' => 1,
                             'financial_year' => '2024-2025'
                         ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertEquals(0, $data['budget']);
        $this->assertEquals(0, $data['expense']);
        $this->assertEquals(0, $data['balance']);
    }

    /** @test */
    public function dashboard_responsive_design_elements_present()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.advanced.dashboard'));

        $response->assertStatus(200);
        
        // Check for responsive classes and elements
        $response->assertSee('dashboard-filters-container');
        $response->assertSee('col-md-6');
        $response->assertSee('flex-wrap');
    }
}
