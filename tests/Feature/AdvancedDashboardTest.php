<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\ClientUnit;
use App\Models\Client;
use App\Models\ClientUnitAnnualBudget;
use App\Models\ClientUnitWeeklyBudget;
use App\Models\Booking;

class AdvancedDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $client;
    protected $clientUnit;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->client = Client::factory()->create();
        $this->clientUnit = ClientUnit::factory()->create([
            'clientId' => $this->client->clientId
        ]);
        $this->user = User::factory()->create([
            'portal_id' => $this->client->clientId,
            'portal_type' => Client::class
        ]);
    }

    /** @test */
    public function user_can_access_advanced_dashboard()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('client.portal.advanced.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('clientPortal.advanced-dashboard');
        $response->assertViewHas('clientId', $this->client->clientId);
        $response->assertViewHas('permissions');
        $response->assertViewHas('units');
        $response->assertViewHas('financialYear');
    }

    /** @test */
    public function advanced_dashboard_menu_item_exists()
    {
        $menuItems = config('menu');
        
        // Find the Home menu item
        $homeMenu = collect($menuItems)->firstWhere('key', 'home');
        $this->assertNotNull($homeMenu);
        
        // Check if Advanced Dashboard menu item exists
        $advancedDashboard = collect($homeMenu['children'])->firstWhere('key', 'home.advanced_dashboard');
        $this->assertNotNull($advancedDashboard);
        $this->assertEquals('Advanced Dashboard', $advancedDashboard['name']);
        $this->assertEquals('client.portal.advanced.dashboard', $advancedDashboard['route']);
    }

    /** @test */
    public function advanced_dashboard_controller_returns_budget_summary()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/budget-summary', [
                             'client' => $this->client->clientId,
                             'filter' => 1
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'budget',
            'expense',
            'balance',
            'units',
            'client'
        ]);
    }

    /** @test */
    public function advanced_dashboard_controller_returns_budget_donut_data()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/budget-donut', [
                             'client' => $this->client->clientId,
                             'filter' => 1
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'graph' => [
                'budgetGraph' => [
                    'data',
                    'title',
                    'table' => [
                        'rows',
                        'tfoot'
                    ]
                ]
            ]
        ]);
    }

    /** @test */
    public function advanced_dashboard_controller_returns_expense_donut_data()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/expense-donut', [
                             'client' => $this->client->clientId,
                             'filter' => 1
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'graph' => [
                'expenseGraph' => [
                    'data',
                    'title',
                    'table' => [
                        'rows',
                        'tfoot'
                    ]
                ]
            ]
        ]);
    }

    /** @test */
    public function advanced_dashboard_controller_returns_bar_chart_data()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/bar-chart', [
                             'client' => $this->client->clientId,
                             'filter' => 1
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'categories',
            'series'
        ]);
    }

    /** @test */
    public function advanced_dashboard_controller_returns_line_chart_data()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/line-chart', [
                             'client' => $this->client->clientId
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'categories',
            'series'
        ]);
    }

    /** @test */
    public function advanced_dashboard_controller_returns_booking_reasons()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/booking-reasons', [
                             'client_id' => $this->client->clientId,
                             'filter' => 1
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'reasons'
        ]);
    }

    /** @test */
    public function advanced_dashboard_controller_returns_client_units()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/api/client-portal/advanced-dashboard/client-units', [
                             'client' => $this->client->clientId
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'units'
        ]);
    }

    /** @test */
    public function advanced_dashboard_handles_different_filter_values()
    {
        // Test with different filter values
        $filters = [1, 2, 4]; // This Month, Last Month, Next Month
        
        foreach ($filters as $filter) {
            $response = $this->actingAs($this->user)
                             ->postJson('/api/client-portal/advanced-dashboard/budget-summary', [
                                 'client' => $this->client->clientId,
                                 'filter' => $filter
                             ]);

            $response->assertStatus(200);
            $response->assertJsonStructure([
                'status',
                'budget',
                'expense',
                'balance'
            ]);
        }
    }

    /** @test */
    public function advanced_dashboard_requires_authentication()
    {
        $response = $this->get(route('client.portal.advanced.dashboard'));
        
        // Should redirect to login or return unauthorized
        $this->assertTrue(
            $response->status() === 302 || $response->status() === 401
        );
    }

    /** @test */
    public function api_endpoints_require_authentication()
    {
        $endpoints = [
            'budget-summary',
            'budget-donut',
            'expense-donut',
            'bar-chart',
            'line-chart',
            'booking-reasons',
            'client-units'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->postJson("/api/client-portal/advanced-dashboard/{$endpoint}");
            
            // Should return unauthorized
            $this->assertTrue(
                $response->status() === 401 || $response->status() === 403
            );
        }
    }
}
