<?php

use App\Http\Controllers\ClientPortal\BudgetController;
use App\Http\Controllers\ClientPortal\DashboardController;
use App\Http\Controllers\ClientPortal\AdvancedDashboardController;
use App\Http\Controllers\ClientPortal\InvoiceUnitController;
use App\Http\Controllers\ClientPortal\UnitViewController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });

Route::group(['prefix'=>'client-portal'],function(){

    Route::post('do-login', [DashboardController::class, 'doLogin']);
    Route::post('get-client-budget', [DashboardController::class, 'getBudgets']);
    Route::post('get-dashboard-donut-graph', [DashboardController::class, 'getDonutGraph']);
    Route::post('get-dashboard-bar-budget-usage', [DashboardController::class, 'getBarBudgetUsage']);
    Route::post('get-dashboard-line-charts', [DashboardController::class, 'getLineCharts']);
    Route::post('get-dashboard-table-bookings', [DashboardController::class, 'getTableBookings']);
    Route::post('get-dashboard-overview', [DashboardController::class, 'overview']);
    Route::post('password-save', [DashboardController::class, 'changePassword']);
    Route::get('booking-counts-costs', [DashboardController::class, 'getBookingCountsAndCosts']);
    Route::get('booking-counts-costs-unitview', [UnitViewController::class, 'getBookingCountsAndCosts']);

    Route::post('add-dashboard-reminder', [DashboardController::class, 'addReminder']);
    Route::post('list-dashboard-reminders', [DashboardController::class, 'listReminders']);
    Route::post('get-dashboard-reminders', [DashboardController::class, 'getReminders']);
    Route::post('remove-dashboard-reminders', [DashboardController::class, 'removeReminders']);
    Route::post('get-dashboard-budget-edit', [DashboardController::class, 'unitBudgetEdit']);
  
    Route::post('add-dashboard-todo', [DashboardController::class, 'addTodo']);
    Route::post('list-dashboard-todos', [DashboardController::class, 'listTodos']);
    Route::post('get-dashboard-todos', [DashboardController::class, 'getTodos']);
    Route::post('remove-dashboard-todos', [DashboardController::class, 'removeTodos']);

    Route::post('get-budget-view-invoices', [UnitViewController::class, 'getClientBudgetInvoices']);
    Route::post('get-weekly-budget-data', [UnitViewController::class, 'getWeeklyBudgetData']);
    Route::post('get-weekly-utilisation-logs', [UnitViewController::class, 'getWeeklyUtilisationLogs']);
    Route::post('get-unit-view-budget', [UnitViewController::class, 'getBudgets']);
    Route::post('get-unit-view-budget-invoices', [UnitViewController::class, 'getBudgetInvoices']);
    Route::post('get-unit-budget-edit', [UnitViewController::class, 'unitBudgetEdit']);
    Route::post('get-unit-view-donut-graph', [UnitViewController::class, 'getDonutGraph']);
    Route::post('get-unit-bar-chart', [UnitViewController::class, 'getUnitBarChart']);
    Route::post('get-budget-page-overview', [BudgetController::class, 'overview']);

    Route::get('invoice-unit', [InvoiceUnitController::class, 'index']);

    // Advanced Dashboard API Routes
    Route::group(['prefix' => 'advanced-dashboard'], function() {
        Route::post('budget-summary', [\App\Http\Controllers\ClientPortal\AdvancedDashboardController::class, 'getBudgetSummary']);
        Route::post('budget-donut', [\App\Http\Controllers\ClientPortal\AdvancedDashboardController::class, 'getBudgetDonutGraph']);
        Route::post('expense-donut', [\App\Http\Controllers\ClientPortal\AdvancedDashboardController::class, 'getExpenseDonutGraph']);
        Route::post('bar-chart', [\App\Http\Controllers\ClientPortal\AdvancedDashboardController::class, 'getBarChartData']);
        Route::post('line-chart', [\App\Http\Controllers\ClientPortal\AdvancedDashboardController::class, 'getLineChartData']);
        Route::post('booking-reasons', [\App\Http\Controllers\ClientPortal\AdvancedDashboardController::class, 'getBookingReasons']);
        Route::post('client-units', [\App\Http\Controllers\ClientPortal\AdvancedDashboardController::class, 'getClientUnits']);
    });

    // Test route for debugging weekly budget functionality
    Route::get('test-weekly-budget/{bookingId}', function($bookingId) {
        $controller = new \App\Http\Controllers\UnitPortal\BookingController();
        return $controller->debugWeeklyBudgetForBooking($bookingId);
    });
});

