<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::table('bookings', function (Blueprint $table) {
            $table->decimal('initial_cost', 10, 3)
                  ->nullable()
                  ->after('unit_cost') // Adjust this to place column where you want it
                  ->comment('Stores the initial cost of the booking at the time of creation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
       Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn('initial_cost');
        });
    }
};
