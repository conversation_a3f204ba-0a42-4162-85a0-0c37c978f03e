<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to use raw SQL to modify the enum
        // Using the full table name with prefix as <PERSON><PERSON> adds 'ng_' prefix automatically
        DB::statement("ALTER TABLE ng_weekly_utilised_fund_logs MODIFY COLUMN adjustment_type ENUM('addition', 'deduction', 'updation')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'updation' from the enum (revert to original values)
        DB::statement("ALTER TABLE ng_weekly_utilised_fund_logs MODIFY COLUMN adjustment_type ENUM('addition', 'deduction')");
    }
};
