<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::table('timesheets', function (Blueprint $table) {
            $table->string('unit_expense_type')->nullable()->after('verified_at');
            $table->float('unit_expense_amount', 4, 2)->nullable()->after('unit_expense_type');
        });
    }

    public function down(): void
    {
        Schema::table('timesheets', function (Blueprint $table) {
            $table->dropColumn(['unit_expense_type', 'unit_expense_amount']);
        });
    }
};
