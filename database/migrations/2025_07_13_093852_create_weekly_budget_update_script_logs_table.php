<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('weekly_budget_update_script_logs')) {
            Schema::create('weekly_budget_update_script_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('booking_id')->index();
            $table->json('booking_details'); // Snapshot: date, unit_name, staff_name, shift_name, category_name
            $table->decimal('initial_value', 10, 2)->nullable(); // Original cost before update
            $table->decimal('unit_final_cost', 10, 2)->nullable(); // Final calculated cost
            $table->enum('status', ['success', 'failed', 'skipped', 'updated'])->index();
            $table->decimal('total_weekly_utilisation', 10, 2)->nullable(); // Weekly budget total after update
            $table->unsignedBigInteger('client_unit_weekly_budget_id')->nullable()->index();
            $table->enum('adjustment_type', ['addition', 'deduction', 'updation'])->index();
            $table->text('error_log')->nullable(); // Plain language error explanation
            $table->integer('processed_count')->default(1); // How many times processed
            $table->timestamp('last_processed_at');
            $table->timestamps();
            
            // Foreign key constraints with shorter names
            $table->foreign('booking_id', 'wbusl_booking_fk')->references('bookingId')->on('bookings')->onDelete('cascade');
            $table->foreign('client_unit_weekly_budget_id', 'wbusl_weekly_budget_fk')->references('id')->on('client_unit_weekly_budgets')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('weekly_budget_update_script_logs');
    }
};
