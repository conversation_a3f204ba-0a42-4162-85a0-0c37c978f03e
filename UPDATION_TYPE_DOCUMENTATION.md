# updateWeeklyUtilisedFund Function - 'updation' Type

## Overview
The `updateWeeklyUtilisedFund()` function in `app/Helpers/helpers.php` now supports three adjustment types:

- **'addition'**: Adds amount to budget utilization (for new bookings)
- **'deduction'**: Subtracts amount from budget utilization (for cancellations)  
- **'updation'**: Updates existing booking costs by calculating the difference (for cost changes)

## New 'updation' Type

### Purpose
The 'updation' type handles updates to existing booking costs by:
1. Retrieving the booking's current `initial_cost`
2. Calculating the difference between old and new costs
3. Applying only the difference to the weekly budget utilization
4. Updating the booking's `initial_cost` to the new value

### Usage
```php
updateWeeklyUtilisedFund(
    null,                    // Weekly budget ID (auto-detected from booking)
    $newCostValue,          // NEW cost value (not a delta)
    'updation',             // Adjustment type
    'App\Models\Booking',   // Source type (required for updation)
    $bookingId,             // Source ID (required for updation)
    $description            // Optional description (auto-generated if null)
);
```

### Example Scenario
```
Initial state:
- Booking #123 initial_cost = £75.00
- Weekly budget utilization = £200.00

Update booking cost to £100.00:
- Previous cost: £75.00
- New cost: £100.00  
- Difference: +£25.00
- New weekly utilization: £200.00 + £25.00 = £225.00
- Booking initial_cost updated to £100.00
```

### Key Features
1. **Automatic difference calculation**: No need to manually calculate cost changes
2. **Dual updates**: Updates both weekly budget and booking record
3. **Enhanced logging**: Detailed log descriptions showing old/new costs and differences
4. **Transaction safety**: All operations wrapped in database transactions
5. **Backward compatibility**: Existing 'addition' and 'deduction' types unchanged

### Log Entry Format
```
"Booking cost updated from £75.00 to £100.00 for booking ID: 123 (difference: £+25.00)"
```

### Validation
- Requires `sourceType === 'App\Models\Booking'` and valid `sourceId`
- Throws exception if booking not found
- No restriction on adjustment amount (can be negative for cost decreases)

### Integration with UpdateBudgetAndCosts Command
The existing command continues to use 'addition' type for initial booking cost processing. The 'updation' type is intended for scenarios where booking costs change after initial processing.
