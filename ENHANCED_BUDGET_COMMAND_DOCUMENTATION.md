# Enhanced Budget Update Command Documentation

## Overview
The `UpdateBudgetAndCosts` Laravel Artisan command has been enhanced with improved cost calculation logic, comprehensive logging, and better error handling for managing booking costs and budget utilization.

## Key Enhancements

### 1. Priority-Based Cost Calculation
The command now uses a three-tier priority system for determining costs:

1. **`unit_final_cost`** (Highest Priority)
   - Uses the `unit_final_cost` column if available and > 0
   - Direct cost stored in the database

2. **`finalcostCalculation` Method** (Medium Priority)
   - Calls the `finalcostCalculation` method from `BookingController`
   - Dynamic calculation based on current rates and conditions
   - Includes approved timesheets, staff payments, and expenses

3. **`initial_cost`** (Fallback Priority)
   - Uses the calculated `initial_cost` as final fallback
   - Ensures backward compatibility

### 2. Comprehensive Logging System
All budget update activities are now logged in the `weekly_budget_update_script_logs` table:

#### Log Table Schema
```sql
- id: Primary key
- booking_id: Foreign key to bookings table
- booking_details: JSON snapshot of booking info
- initial_value: Original cost before update
- unit_final_cost: Final calculated cost used
- status: 'success', 'failed', 'skipped', 'updated'
- total_weekly_utilisation: Weekly budget total after update
- client_unit_weekly_budget_id: Related weekly budget
- adjustment_type: 'addition', 'deduction', 'updation'
- error_log: Human-readable error explanation
- processed_count: Number of times processed
- last_processed_at: When last processed
- timestamps: Created/updated timestamps
```

#### Status Types
- **success**: Budget successfully updated
- **failed**: Error occurred during processing
- **skipped**: Booking not eligible or zero cost
- **updated**: Initial cost calculation updated

### 3. Enhanced Single Booking Processing
The `--booking-id` option now provides detailed information:

```bash
php artisan budget:update-costs --booking-id=12345
```

**Output includes:**
- Cost breakdown by source (unit_final_cost, calculated, fallback)
- Final cost determination logic
- Budget utilization update status
- Logging information
- Detailed error messages if applicable

## Usage Examples

### Basic Commands
```bash
# Dry run (no changes made)
php artisan budget:update-costs --dry-run

# Quick summary overview
php artisan budget:update-costs --summary-only

# Process with custom batch size
php artisan budget:update-costs --batch-size=50

# Reset all processing flags
php artisan budget:update-costs --reset-all

# Process specific booking
php artisan budget:update-costs --booking-id=12345
```

### Production Workflow
```bash
# 1. First, run a summary to see what will be processed
php artisan budget:update-costs --summary-only

# 2. Run a dry-run to validate the process
php artisan budget:update-costs --dry-run

# 3. Execute the actual update
php artisan budget:update-costs

# 4. Monitor logs for any issues
php artisan budget:update-costs --booking-id=SPECIFIC_ID
```

## Error Handling

### Transaction Safety
- All budget updates are wrapped in database transactions
- Rollback on any error to maintain data integrity
- Comprehensive error logging

### Graceful Degradation
- If `finalcostCalculation` fails, falls back to `initial_cost`
- Continues processing other bookings if one fails
- Detailed error messages for troubleshooting

### Logging Integration
- All errors logged to Laravel logs
- Additional logging to the tracking table
- Human-readable error messages

## Cost Calculation Logic

### Priority Flow
```
1. Check unit_final_cost
   ├─ If > 0: Use this value ✓
   └─ If null/0: Continue to step 2

2. Run finalcostCalculation()
   ├─ If > 0: Use calculated value ✓
   ├─ If error: Log warning, continue to step 3
   └─ If 0: Continue to step 3

3. Use initial_cost
   ├─ If > 0: Use fallback value ✓
   └─ If 0: Skip booking (log as skipped)
```

### Adjustment Types
- **addition**: New budget utilization entry
- **deduction**: Removing/reducing utilization
- **updation**: Modifying existing utilization (when costs change)

## Database Requirements

### Required Tables
- `bookings` - Must have `unit_final_cost` column
- `weekly_budget_update_script_logs` - New logging table
- `client_unit_weekly_budgets` - Weekly budget tracking
- `weekly_utilised_fund_logs` - Fund utilization logs

### Required Migrations
```bash
# Add unit_final_cost column
php artisan migrate --path=database/migrations/2025_07_12_172506_add_unit_final_cost_to_bookings.php

# Create logging table
php artisan migrate --path=database/migrations/2025_07_13_093852_create_weekly_budget_update_script_logs_table.php
```

## Monitoring and Maintenance

### Log Analysis
```sql
-- Check recent processing activity
SELECT booking_id, status, unit_final_cost, adjustment_type, created_at 
FROM weekly_budget_update_script_logs 
ORDER BY created_at DESC 
LIMIT 50;

-- Count by status
SELECT status, COUNT(*) as count 
FROM weekly_budget_update_script_logs 
GROUP BY status;

-- Find failed bookings
SELECT booking_id, error_log, created_at 
FROM weekly_budget_update_script_logs 
WHERE status = 'failed';
```

### Performance Monitoring
- Batch processing reduces memory usage
- Progress bars show real-time status
- Configurable batch sizes for different environments

## Troubleshooting

### Common Issues

1. **"unit_final_cost column not found"**
   - Run: `php artisan migrate`

2. **"weekly_budget_update_script_logs table not found"**
   - Run: `php artisan migrate`

3. **"finalcostCalculation method errors"**
   - Check booking data completeness
   - Verify staff/unit/category relationships
   - Review timesheet approval status

4. **High error rates**
   - Run `--booking-id` on specific failing IDs
   - Check Laravel logs for detailed errors
   - Verify data integrity

### Testing
```bash
# Test the enhanced functionality
php test_enhanced_budget_command.php

# Test specific booking
php artisan budget:update-costs --booking-id=KNOWN_GOOD_ID --dry-run
```

## Integration Notes

### Helper Function Usage
The command uses the existing `updateWeeklyUtilisedFund()` helper with enhanced description logging:

```php
updateWeeklyUtilisedFund(
    null, // weeklyBudgetId - auto-determined
    $costToUse,
    'addition',
    'App\Models\Booking',
    $booking->bookingId,
    '[SCRIPT BASED] Enhanced description with cost source'
);
```

### BookingController Integration
Direct integration with `BookingController::finalcostCalculation()`:
- Method accepts Booking model
- Returns formatted string (converted to numeric)
- Handles all cost calculation complexity
- Includes approved timesheet data

## Future Enhancements

### Potential Improvements
- Email notifications for high error rates
- Dashboard for monitoring processing status
- Automated retry mechanism for failed bookings
- Cost comparison reporting (before/after)
- Bulk cost recalculation tools

### Scalability Considerations
- Consider job queues for very large datasets
- Database indexing on frequently queried columns
- Archive old log entries periodically
- Memory optimization for batch processing

---

## Change Log

### Version 2.0 (Current)
- ✅ Priority-based cost calculation (unit_final_cost > finalcostCalculation > initial_cost)
- ✅ Comprehensive logging system with detailed tracking
- ✅ Enhanced --booking-id option with detailed output
- ✅ Improved error handling and transaction safety
- ✅ Backward compatibility maintained
- ✅ Database migration for logging table

### Version 1.0 (Previous)
- Basic cost calculation using unit_cost/initial_cost
- Simple budget utilization updates
- Basic error logging
- Limited single booking support
