# Advanced Dashboard Setup Guide

This guide explains how to set up and use the new Advanced Dashboard system.

## Overview

The Advanced Dashboard is a modular, component-based dashboard system that provides:
- Enhanced data visualization
- Modular component architecture
- Permission-based access control
- Dynamic filtering capabilities
- Real-time data updates

## Setup Complete

The following components have been created and configured:

### 1. Controller
- **File**: `app/Http/Controllers/ClientPortal/AdvancedDashboardController.php`
- **Purpose**: Handles all advanced dashboard functionality and API endpoints
- **Methods**:
  - `index()` - Main dashboard view
  - `getBudgetSummary()` - Budget summary data
  - `getBudgetDonutGraph()` - Budget breakdown chart
  - `getExpenseDonutGraph()` - Expense breakdown chart
  - `getBarChartData()` - Budget vs expenses comparison
  - `getLineChartData()` - Expense trends over time
  - `getBookingReasons()` - Booking reasons analysis
  - `getClientUnits()` - Client units for filtering

### 2. Routes
- **Web Route**: `/advanced-dashboard` → `client.portal.advanced.dashboard`
- **API Routes**: All under `/api/client-portal/advanced-dashboard/`
  - `POST /budget-summary`
  - `POST /budget-donut`
  - `POST /expense-donut`
  - `POST /bar-chart`
  - `POST /line-chart`
  - `POST /booking-reasons`
  - `POST /client-units`

### 3. View
- **File**: `resources/views/clientPortal/advanced-dashboard.blade.php`
- **Features**: Uses the modular dashboard wrapper with advanced API endpoints

### 4. Menu Integration
- **Location**: Home → Advanced Dashboard
- **Permission**: `client_portal_dashboard_dashboard_view`
- **Order**: Second item under Home menu

## Usage

### Accessing the Advanced Dashboard

1. **Via Menu**: Navigate to Home → Advanced Dashboard
2. **Direct URL**: `/advanced-dashboard`
3. **Route Name**: `route('client.portal.advanced.dashboard')`

### API Endpoints

All API endpoints expect POST requests with the following common parameters:

```json
{
    "client": "client_id",
    "filter": 1,  // 1=This Month, 2=Last Month, 4=Next Month
    "unit_id": "optional_unit_id",
    "financial_year": "optional_financial_year"
}
```

### Example API Usage

```javascript
// Get budget summary
$.post('/api/client-portal/advanced-dashboard/budget-summary', {
    client: clientId,
    filter: 1
}, function(response) {
    console.log(response.budget, response.expense, response.balance);
});

// Get budget donut chart data
$.post('/api/client-portal/advanced-dashboard/budget-donut', {
    client: clientId,
    filter: 1
}, function(response) {
    // Use response.graph.budgetGraph.data for chart
});
```

## Data Sources

The Advanced Dashboard uses the following primary data sources:

1. **ClientUnitWeeklyBudget** - Weekly budget allocations and utilizations
2. **ClientUnitAnnualBudget** - Annual budget planning
3. **ClientUnit** - Unit information and status
4. **Booking** - Booking data for reasons analysis
5. **Client** - Client information

## Features

### 1. Budget Summary Cards
- Total budget allocation
- Total expenses
- Remaining balance
- Percentage calculations

### 2. Donut Charts
- Budget breakdown by units
- Expense breakdown by units
- Interactive data tables

### 3. Bar Chart (Smart View)
- Budget vs expenses comparison
- Unit-wise breakdown
- Monthly filter options

### 4. Line Chart
- Expense trends over 6 months
- Multi-unit comparison
- Historical analysis

### 5. Booking Reasons Analysis
- Count and cost by reason type
- Categories: Sickness, Holiday, Vacant, etc.
- Real-time calculations

### 6. Filtering System
- Month-based filtering (This/Last/Next Month)
- Unit-specific filtering
- Financial year filtering
- Date range filtering

## Permissions

The Advanced Dashboard uses the existing permission system:
- **Main Permission**: `client_portal_dashboard_dashboard_view`
- **Component Permissions**: Managed via `DashboardPermissions` helper
- **All permissions currently set to `true`** as requested

## Testing

### Running Tests

```bash
# Run all advanced dashboard tests
php artisan test tests/Feature/AdvancedDashboardTest.php

# Run specific test
php artisan test --filter=user_can_access_advanced_dashboard
```

### Test Coverage

The test suite covers:
- Route accessibility
- Controller method responses
- API endpoint functionality
- Authentication requirements
- Menu item configuration
- Data structure validation

### Manual Testing

1. **Access Test**: Visit `/advanced-dashboard`
2. **Menu Test**: Check Home → Advanced Dashboard menu item
3. **API Test**: Test all API endpoints with valid data
4. **Filter Test**: Try different filter combinations
5. **Permission Test**: Verify access control works

## Troubleshooting

### Common Issues

1. **Route Not Found**
   - Clear route cache: `php artisan route:clear`
   - Check route registration in `routes/web.php` and `routes/api.php`

2. **Controller Not Found**
   - Run: `composer dump-autoload`
   - Check namespace and class name

3. **Menu Item Not Showing**
   - Clear config cache: `php artisan config:clear`
   - Check permissions in `config/menu.php`

4. **API Endpoints Not Working**
   - Check authentication middleware
   - Verify CSRF token for POST requests
   - Check database connections

5. **Charts Not Loading**
   - Verify Highcharts library is loaded
   - Check JavaScript console for errors
   - Ensure API endpoints return valid data

### Debug Mode

Enable debug logging by adding to your `.env`:

```env
LOG_LEVEL=debug
APP_DEBUG=true
```

## Migration from Old Dashboard

To migrate users from the old dashboard to the advanced dashboard:

1. **Update Menu Links**: Change existing dashboard links to point to advanced dashboard
2. **Update Bookmarks**: Users may need to update bookmarked URLs
3. **Training**: Provide user training on new features
4. **Gradual Rollout**: Consider feature flags for gradual deployment

## Future Enhancements

The system is designed to support:

1. **Real-time Updates**: WebSocket integration for live data
2. **Custom Widgets**: User-configurable dashboard components
3. **Export Features**: PDF/Excel export capabilities
4. **Advanced Filtering**: More granular filter options
5. **Mobile Optimization**: Enhanced mobile experience
6. **Role-based Dashboards**: Different views for different user roles

## Support

For issues or questions:

1. Check the test suite for examples
2. Review the controller methods for API usage
3. Check browser console for JavaScript errors
4. Review Laravel logs for server-side issues

## Conclusion

The Advanced Dashboard is now fully integrated and ready for use. It provides a modern, modular approach to dashboard functionality while maintaining compatibility with existing systems.
